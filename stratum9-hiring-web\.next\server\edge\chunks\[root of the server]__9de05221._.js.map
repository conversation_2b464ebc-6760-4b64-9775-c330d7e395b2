{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/commonConstants.ts"], "sourcesContent": ["import { ExtendedFormValues } from \"@/types/types\";\n\nexport const ACCESS_TOKEN_KEY = \"__ATK__\";\n\nexport const EMAIL_REGEX = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n\nexport const PASSWORD_REGEX = /^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\\s).{8,16}$/;\nexport const NAME_REGEX = /^[a-zA-Z0-9\\s.'-]+$/;\n\nexport const MAX_IMAGE_SIZE = 5242880;\n\nexport const ScheduleInterviewFormSubmissionType = {\n  SCHEDULE: \"schedule\",\n  UPDATE: \"update\",\n};\n\nexport const S3_PATHS = {\n  PROFILE_IMAGE: \"profile-images/:path\",\n};\n\nexport const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [\n  \"Arrive at the interview location on time with a government-issued ID.\",\n  \"Ensure your phone is on silent mode and distractions are minimized.\",\n  \"Bring a printed copy of your resume and any supporting documents.\",\n  \"Dress professionally and maintain proper body language.\",\n  \"Listen carefully, answer honestly, and ask for clarification if needed.\",\n  \"Respect the interview flow and do not interrupt the interviewer.\",\n  \"Take brief notes if necessary, but focus on active conversation.\",\n  \"If you need assistance or face any issues, notify the interview coordinator.\",\n];\n\nexport const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [\n  \"Join the interview on time using the link provided.\",\n  \"Ensure a stable internet connection and a quiet, well-lit space.\",\n  \"Test your camera, microphone, and audio settings in advance.\",\n  \"Keep your video on unless instructed otherwise by the interviewer.\",\n  \"Minimize background noise and avoid multitasking during the session.\",\n  \"Use headphones if possible for better audio clarity.\",\n  \"Be attentive, respond clearly, and maintain professional posture.\",\n  \"Contact support if you face technical difficulties before or during the interview.\",\n];\n\n/**\n * Permission Constants\n */\nexport const PERMISSION = {\n  CREATE_OR_EDIT_JOB_POST: \"create-or-edit-job-post\",\n  SCHEDULE_CONDUCT_INTERVIEWS: \"schedule-conduct-interviews\",\n  VIEW_HIRED_CANDIDATES: \"view-hired-candidates\",\n  ARCHIVE_RESTORE_CANDIDATES: \"archive-restore-candidates\",\n  ARCHIVE_RESTORE_JOB_POSTS: \"archive-restore-job-posts\",\n  MANUAL_RESUME_SCREENING: \"manual-resume-screening\",\n  EDIT_SCHEDULED_INTERVIEWS: \"edit-scheduled-interviews\",\n  ADD_ADDITIONAL_CANDIDATE_INFO: \"add-additional-candidate-info\",\n  ADD_OR_EDIT_INTERVIEW_NOTES: \"add-or-edit-interview-notes\",\n  MANAGE_TOP_CANDIDATES: \"manage-top-candidates\",\n  MANAGE_PRE_INTERVIEW_QUESTIONS: \"manage-pre-interview-questions\",\n  CREATE_FINAL_ASSESSMENT: \"create-final-assessment\",\n  VIEW_FINAL_ASSESSMENT: \"view-final-assessment\",\n  VIEW_CANDIDATE_PROFILE_SUMMARY: \"view-candidate-profile-summary\",\n  HIRE_CANDIDATE: \"hire-candidate\",\n  CREATE_NEW_ROLE: \"create-new-role\",\n  MANAGE_USER_PERMISSIONS: \"manage-user-permissions\",\n  CREATE_NEW_DEPARTMENT: \"create-new-department\",\n  ADD_INTERVIEW_PARTICIPANTS: \"add-interview-participants\",\n  VIEW_SUBSCRIPTION_PLAN: \"view-subscription-plan\",\n  MANAGE_SUBSCRIPTIONS: \"manage-subscriptions\",\n  VIEW_AUDIT_LOGS_UPCOMING: \"view-audit-logs-upcoming\",\n  VIEW_ALL_SCHEDULED_INTERVIEWS: \"view-all-scheduled-interviews\",\n};\n\n/**\n * Plan Configuration Constants\n */\nexport const PLAN_CONFIG = {\n  free: {\n    maxCandidates: 1,\n    showAddButton: false,\n    planDisplayName: \"Free\",\n  },\n  pro: {\n    maxCandidates: 1,\n    showAddButton: false,\n    planDisplayName: \"Pro\",\n  },\n  growth: {\n    maxCandidates: 5,\n    showAddButton: true,\n    planDisplayName: \"Growth\",\n  },\n  enterprise: {\n    maxCandidates: 5,\n    showAddButton: true,\n    planDisplayName: \"Enterprise\",\n  },\n} as const;\n\n/**\n * Plan Configuration Type\n */\nexport type PlanConfigType = (typeof PLAN_CONFIG)[keyof typeof PLAN_CONFIG];\n\n/**\n * Plan Name Constants\n */\nexport const PLAN_NAMES = {\n  FREE: \"Free\",\n  PRO: \"Pro\",\n  GROWTH: \"Growth\",\n  ENTERPRISE: \"Enterprise\",\n} as const;\n\n/**\n * Skill Constants\n */\nexport const SKILL_CONSTANTS = {\n  REQUIRED_ROLE_SKILLS: 10,\n  REQUIRED_CULTURE_SKILLS: 5,\n};\nexport const commonConstants = {\n  finalAssessmentId: \"finalAssessmentId\",\n  token: \"token\",\n  isShared: \"isShared\",\n  isSubmitted: \"isSubmitted\",\n  jobId: \"jobId\",\n  jobApplicationId: \"jobApplicationId\",\n};\n\nexport const QuestionType = {\n  MCQ: \"mcq\",\n  TRUE_FALSE: \"true_false\",\n};\n\n// Constants for option IDs\nexport const OPTION_ID = {\n  A: \"A\",\n  B: \"B\",\n  C: \"C\",\n  D: \"D\",\n  TRUE: \"true\",\n  FALSE: \"false\",\n} as const;\n\n// Constants for question types\nexport const QUESTION_TYPE = {\n  MCQ: \"mcq\" as const,\n  TRUE_FALSE: \"true_false\" as const,\n};\n\n// Constants for default options\nexport const DEFAULT_MCQ_OPTIONS = [\n  { id: OPTION_ID.A, text: \"\" },\n  { id: OPTION_ID.B, text: \"\" },\n  { id: OPTION_ID.C, text: \"\" },\n  { id: OPTION_ID.D, text: \"\" },\n];\n\nexport const DEFAULT_TRUE_FALSE_OPTIONS = [\n  { id: OPTION_ID.TRUE, text: \"True\" },\n  { id: OPTION_ID.FALSE, text: \"False\" },\n];\n\nexport const INTERVIEW_SCHEDULE_ROUND_TYPE = [\n  {\n    label: \"One-On-One\",\n    value: \"One-On-One\",\n  },\n  {\n    label: \"Video Call\",\n    value: \"Video Call\",\n  },\n];\n\n/**\n * Interview Question Types\n */\nexport const QUESTION_TYPES = {\n  ROLE_SPECIFIC: \"role_specific\",\n  CULTURE_SPECIFIC: \"culture_specific\",\n  CAREER_BASED: \"career_based\",\n} as const;\n\nexport type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];\n/**\n * Empty Content Patterns\n */\nexport const EMPTY_CONTENT_PATTERNS = [\"<p><br></p>\", \"<p></p>\", \"<div><br></div>\", \"<div></div>\", \"<p>&nbsp;</p>\"];\n\n// Define the initial state using FormValues type\nexport const initialState: ExtendedFormValues = {\n  title: \"\",\n  employment_type: \"\",\n  department_id: \"\",\n  salary_range: \"\",\n  salary_cycle: \"\",\n  location_type: \"\",\n  state: \"\",\n  city: \"\",\n  role_overview: \"\",\n  experience_level: \"\",\n  responsibilities: \"\",\n  educations_requirement: \"\",\n  certifications: undefined,\n  skills_and_software_expertise: \"\",\n  experience_required: \"\",\n  ideal_candidate_traits: \"\",\n  about_company: \"\",\n  perks_benefits: undefined,\n  tone_style: \"\",\n  additional_info: undefined,\n  compliance_statement: [],\n  show_compliance: false,\n  hiring_type: \"\",\n};\n\n// Define the skill item interface\nexport interface ISkillItem {\n  id: number;\n  title: string;\n  description: string;\n  short_description: string;\n}\n\n// Define a skill category interface\nexport interface ISkillCategory {\n  type: string;\n  items: ISkillItem[];\n}\n\n// Define the slice state type\nexport interface AllSkillsState {\n  categories: ISkillCategory[];\n  loading: boolean;\n  error: string | null;\n}\n\nexport const FILE_EXTENSION = [\n  \"pdf\",\n  \"plain\",\n  \"csv\",\n  \"vnd.ms-excel.sheet.macroEnabled.12\",\n  \"vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n  \"vnd.openxmlformats-officedocument.wordprocessingml.document\",\n  \"vnd.openxmlformats-officedocument.presentationml.presentation\",\n];\n\nexport const ACTIVE = \"active\";\nexport const TOKEN_EXPIRED = \"Session Expired! Please log in again.\";\nexport const DEFAULT_LIMIT = 15;\nexport const STANDARD_LIMIT = 18;\nexport const DEFAULT_OFFSET = 0;\n\nexport enum MessageType {\n  success = \"success\",\n  error = \"error\",\n}\n\nexport const IMAGE_EXTENSIONS = [\"png\", \"jpg\", \"jpeg\", \"gif\", \"webp\"];\n\nexport const ASSESSMENT_INSTRUCTIONS = {\n  instructions: [\n    \"Do not refresh or close the browser\",\n    \"Check your internet connection\",\n    \"Ensure a distraction-free environment\",\n    \"Click 'Submit' only once when finished\",\n    \"Read each question carefully\",\n    \"Manage your time efficiently\",\n    \"Avoid any form of plagiarism\",\n    \"Reach out to support if needed\",\n  ],\n};\nexport const PERMISSIONS_COOKIES_KEY = \"permissions_data\";\n\nexport const PDF_FILE_NAME = \"pdf\";\nexport const PDF_FILE_TYPE = \"application/pdf\";\nexport const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;\nexport const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAmB;AAEzB,MAAM,cAAc;AAEpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AAEnB,MAAM,iBAAiB;AAEvB,MAAM,sCAAsC;IACjD,UAAU;IACV,QAAQ;AACV;AAEO,MAAM,WAAW;IACtB,eAAe;AACjB;AAEO,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAKM,MAAM,aAAa;IACxB,yBAAyB;IACzB,6BAA6B;IAC7B,uBAAuB;IACvB,4BAA4B;IAC5B,2BAA2B;IAC3B,yBAAyB;IACzB,2BAA2B;IAC3B,+BAA+B;IAC/B,6BAA6B;IAC7B,uBAAuB;IACvB,gCAAgC;IAChC,yBAAyB;IACzB,uBAAuB;IACvB,gCAAgC;IAChC,gBAAgB;IAChB,iBAAiB;IACjB,yBAAyB;IACzB,uBAAuB;IACvB,4BAA4B;IAC5B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,+BAA+B;AACjC;AAKO,MAAM,cAAc;IACzB,MAAM;QACJ,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;IACA,KAAK;QACH,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;IACA,QAAQ;QACN,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;IACA,YAAY;QACV,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;AACF;AAUO,MAAM,aAAa;IACxB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,YAAY;AACd;AAKO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,yBAAyB;AAC3B;AACO,MAAM,kBAAkB;IAC7B,mBAAmB;IACnB,OAAO;IACP,UAAU;IACV,aAAa;IACb,OAAO;IACP,kBAAkB;AACpB;AAEO,MAAM,eAAe;IAC1B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,YAAY;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM;IACN,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,sBAAsB;IACjC;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;CAC7B;AAEM,MAAM,6BAA6B;IACxC;QAAE,IAAI,UAAU,IAAI;QAAE,MAAM;IAAO;IACnC;QAAE,IAAI,UAAU,KAAK;QAAE,MAAM;IAAQ;CACtC;AAEM,MAAM,gCAAgC;IAC3C;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAKM,MAAM,iBAAiB;IAC5B,eAAe;IACf,kBAAkB;IAClB,cAAc;AAChB;AAMO,MAAM,yBAAyB;IAAC;IAAe;IAAW;IAAmB;IAAe;CAAgB;AAG5G,MAAM,eAAmC;IAC9C,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,cAAc;IACd,eAAe;IACf,OAAO;IACP,MAAM;IACN,eAAe;IACf,kBAAkB;IAClB,kBAAkB;IAClB,wBAAwB;IACxB,gBAAgB;IAChB,+BAA+B;IAC/B,qBAAqB;IACrB,wBAAwB;IACxB,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,sBAAsB,EAAE;IACxB,iBAAiB;IACjB,aAAa;AACf;AAuBO,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AAEvB,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,MAAM,mBAAmB;IAAC;IAAO;IAAO;IAAQ;IAAO;CAAO;AAE9D,MAAM,0BAA0B;IACrC,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AACO,MAAM,0BAA0B;AAEhC,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,sBAAsB,IAAI,OAAO;AACvC,MAAM,kCAAkC"}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\n  LOGIN: \"/login\",\n  FORGOT_PASSWORD: \"/forgot-password\",\n  VERIFY: \"/verify\",\n  RESET_PASSWORD: \"/reset-password\",\n  CANDIDATE_ASSESSMENT: \"/candidate-assessment\",\n  DASHBOARD: \"/dashboard\",\n  HOME: \"/\",\n  BUY_SUBSCRIPTION: \"/buy-subscription\",\n  PROFILE: {\n    MY_PROFILE: \"/my-profile\",\n  },\n  SUBSCRIPTIONS: {\n    SUCCESS: \"/subscriptions/success\",\n    CANCEL: \"/subscriptions/cancel\",\n  },\n  JOBS: {\n    CAREER_BASED_SKILLS: \"/career-based-skills\",\n    ROLE_BASED_SKILLS: \"/role-based-skills\",\n    CULTURE_BASED_SKILLS: \"/culture-based-skills\",\n    GENERATE_JOB: \"/generate-job\",\n    EDIT_SKILLS: \"/edit-skills\",\n    HIRING_TYPE: \"/hiring-type\",\n    JOB_EDITOR: \"/job-editor\",\n    ACTIVE_JOBS: \"/active-jobs\",\n    CANDIDATE_PROFILE: \"/candidate-profile\",\n    ARCHIVE: \"/archive\",\n  },\n  SCREEN_RESUME: {\n    MANUAL_CANDIDATE_UPLOAD: \"/manual-upload-resume\",\n    CANDIDATE_QUALIFICATION: \"/candidate-qualification\",\n    CANDIDATE_LIST: \"/candidates-list\",\n    CANDIDATES: \"/candidates\",\n  },\n  INTERVIEW: {\n    ADD_CANDIDATE_INFO: \"/additional-submission\",\n    SCHEDULE_INTERVIEW: \"/schedule-interview\",\n    PRE_INTERVIEW_QUESTIONS_OVERVIEW: \"/pre-interview-questions-overview\",\n    INTERVIEW_QUESTION: \"/interview-question\",\n    CALENDAR: \"/calendar\",\n    INTERVIEW_SUMMARY: \"/interview-summary\",\n  },\n\n  ROLE_EMPLOYEES: {\n    ROLES_PERMISSIONS: \"/roles-permissions\",\n    EMPLOYEE_MANAGEMENT: \"/employee-management\",\n    EMPLOYEE_MANAGEMENT_DETAIL: \"/employee-management-detail\",\n    ADD_EMPLOYEE: \"/add-employees\",\n    ADD_DEPARTMENT: \"/add-department\",\n  },\n\n  FINAL_ASSESSMENT: {\n    FINAL_ASSESSMENT: \"/final-assessment\",\n  },\n};\n\nexport const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];\n\n// Routes that don't require permission checks for authenticated users\nexport const UNRESTRICTED_ROUTES = [ROUTES.SUBSCRIPTIONS.SUCCESS, ROUTES.SUBSCRIPTIONS.CANCEL];\n\nexport default ROUTES;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,OAAO;IACP,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,sBAAsB;IACtB,WAAW;IACX,MAAM;IACN,kBAAkB;IAClB,SAAS;QACP,YAAY;IACd;IACA,eAAe;QACb,SAAS;QACT,QAAQ;IACV;IACA,MAAM;QACJ,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,mBAAmB;QACnB,SAAS;IACX;IACA,eAAe;QACb,yBAAyB;QACzB,yBAAyB;QACzB,gBAAgB;QAChB,YAAY;IACd;IACA,WAAW;QACT,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;QACpB,UAAU;QACV,mBAAmB;IACrB;IAEA,gBAAgB;QACd,mBAAmB;QACnB,qBAAqB;QACrB,4BAA4B;QAC5B,cAAc;QACd,gBAAgB;IAClB;IAEA,kBAAkB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,sBAAsB;IAAC,OAAO,KAAK;IAAE,OAAO,eAAe;IAAE,OAAO,MAAM;IAAE,OAAO,cAAc;IAAE,OAAO,oBAAoB;CAAC;AAGrI,MAAM,sBAAsB;IAAC,OAAO,aAAa,CAAC,OAAO;IAAE,OAAO,aAAa,CAAC,MAAM;CAAC;uCAE/E"}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getToken } from \"next-auth/jwt\";\n\nimport { PERMISSION, PERMISSIONS_COOKIES_KEY } from \"./constants/commonConstants\";\nimport ROUTES, { BEFORE_LOGIN_ROUTES, UNRESTRICTED_ROUTES } from \"./constants/routes\";\n// import { IUserData, Role } from \"./interfaces/authInterfaces\";\n\n// Define session data structure with permissions\n// type SessionData = {\n//   data?: {\n//     token?: string;\n//     authData: { userData: IUserData; role: Role; permissions?: string[] };\n//     [key: string]: unknown;\n//   };\n//   [key: string]: unknown;\n// };\n\n// Route permission mapping\nconst routePermissionMap: { [key: string]: string | string[] } = {\n  // User role routes\n  [ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS]: PERMISSION.CREATE_NEW_ROLE,\n\n  // Archive route (contains both candidates and jobs tabs)\n  [ROUTES.JOBS.ARCHIVE]: [PERMISSION.ARCHIVE_RESTORE_CANDIDATES, PERMISSION.ARCHIVE_RESTORE_JOB_POSTS],\n\n  // Manual Resume Screening routes - protect all paths including dynamic routes\n  [ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD]: PERMISSION.MANUAL_RESUME_SCREENING,\n  [ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD + \"/[...path]\"]: PERMISSION.MANUAL_RESUME_SCREENING,\n\n  // Department management - these routes are accessed from employee-management\n  [ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT]: PERMISSION.CREATE_NEW_DEPARTMENT,\n\n  // Job routes - require job post creation/editing permission\n  [ROUTES.JOBS.JOB_EDITOR]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.GENERATE_JOB]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.ACTIVE_JOBS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.CAREER_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.ROLE_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.CULTURE_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.EDIT_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n  [ROUTES.JOBS.HIRING_TYPE]: PERMISSION.CREATE_OR_EDIT_JOB_POST,\n\n  // Buy subscription routes\n  [ROUTES.BUY_SUBSCRIPTION]: PERMISSION.MANAGE_SUBSCRIPTIONS,\n\n  // Add other protected routes and their required permissions here\n};\n\n// Helper function to normalize route path\nconst normalizePath = (path: string): string => {\n  // Remove trailing slash except for root path\n  return path === \"/\" ? path : path.replace(/\\/$/, \"\");\n};\n\n// Helper function to convert Next.js dynamic route pattern to regex pattern\nconst routeToRegex = (pattern: string): RegExp => {\n  // Normalize and escape the pattern for regex\n  const regexPattern = normalizePath(pattern)\n    // Escape special regex characters except [ and ]\n    .replace(/[.+?^${}()|\\\\\\/]/g, \"\\\\$&\")\n    // Replace [...param] catch-all routes\n    .replace(/\\[\\.\\.\\.[^\\]]+\\]/g, \".*\")\n    // Replace [param] dynamic segments\n    .replace(/\\[[^\\]]+\\]/g, \"[^/]+\");\n\n  return new RegExp(`^${regexPattern}$`);\n};\n\n// Type for route patterns map entries\ntype RoutePattern = [RegExp, string | string[]];\n\n// Pre-compile regex patterns for dynamic routes\nconst dynamicRoutePatterns = new Map<RegExp, string | string[]>(\n  Object.entries(routePermissionMap)\n    .filter(([pattern]) => pattern.includes(\"[\"))\n    .map(([pattern, permissions]): RoutePattern => [routeToRegex(pattern), permissions])\n    // Sort patterns by specificity (more specific routes first)\n    .sort((a, b) => {\n      const segmentsA = a[0].toString().split(\"/\").length;\n      const segmentsB = b[0].toString().split(\"/\").length;\n      return segmentsB - segmentsA;\n    })\n);\n\n// Convert routePermissionMap to Map for faster lookups\nconst staticRoutePermissions = new Map<string, string | string[]>(Object.entries(routePermissionMap).filter(([pattern]) => !pattern.includes(\"[\")));\n\n// Helper function to check if user has any of the required permissions\nconst hasAnyPermission = (userPermissions: string[], required: string | string[]): boolean => {\n  return Array.isArray(required) ? required.some((perm) => userPermissions.includes(perm)) : userPermissions.includes(required);\n};\n\n// Helper function to check if a user has permission for a given route\nconst hasPermissionForRoute = (path: string, userPermissions: string[]): boolean => {\n  console.log(\"userPermissions>>>>>>>\", userPermissions);\n\n  // Early return for empty permissions array\n  if (!userPermissions.length) return false;\n\n  // Check static routes first (faster lookup)\n  const staticPermissions = staticRoutePermissions.get(path);\n\n  if (staticPermissions) {\n    return hasAnyPermission(userPermissions, staticPermissions);\n  }\n\n  // Check dynamic routes\n  for (const [pattern, requiredPermissions] of dynamicRoutePatterns) {\n    console.log(`>>>>>>>>>>>>>Checking dynamic route: ${pattern} for path: ${path}`);\n    console.log(\">>>>>>>>>>>>>requiredPermissions\", requiredPermissions);\n\n    if (pattern.test(path)) {\n      return hasAnyPermission(userPermissions, requiredPermissions);\n    }\n  }\n\n  // No match found in either static or dynamic routes - allow unrestricted access\n  return true;\n};\n\n// Helper function to parse cookie data\nconst parseCookieData = <T>(cookieString: string | undefined | null, cookieName: string): T | null => {\n  if (!cookieString) return null;\n  const cookieValue = cookieString.split(\";\").find((c) => c.trim().startsWith(`${cookieName}=`));\n  if (!cookieValue) return null;\n\n  try {\n    // Extract the value part (after '=') and decode it\n    const encodedValue = cookieValue.split(\"=\")[1];\n    if (!encodedValue) return null;\n    // First decode the URL encoded string, then parse as JSON\n    return JSON.parse(decodeURIComponent(encodedValue)) as T;\n  } catch (error) {\n    console.error(`Error parsing ${cookieName} cookie:`, error);\n    return null;\n  }\n};\n\nexport default async function middleware(req: NextRequest) {\n  try {\n    const session = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });\n\n    // Define types for our Redux data structures\n    type AuthData = {\n      permissions?: string[];\n      [key: string]: unknown;\n    };\n    const isAuthenticated = !!session?.data;\n    const path = req.nextUrl.pathname;\n    const isPublicRoute = BEFORE_LOGIN_ROUTES.includes(path);\n    // Check if the current path is in the unrestricted routes list\n    const isUnrestrictedRoute = UNRESTRICTED_ROUTES.includes(path);\n    const response = NextResponse.next();\n\n    if (isAuthenticated) {\n      if (isPublicRoute) {\n        return NextResponse.redirect(new URL(ROUTES.DASHBOARD, req.url));\n      } else {\n        if (isUnrestrictedRoute) {\n          return response; // Allow unrestricted routes without permission check\n        }\n        const cookieHeader = req.headers.get(\"cookie\");\n        const authDataFromCookies = parseCookieData<AuthData>(cookieHeader, PERMISSIONS_COOKIES_KEY);\n        const userPermissions = (authDataFromCookies?.length ? authDataFromCookies : []) as string[];\n\n        // Check if user has permission for the route\n        if (path === ROUTES.DASHBOARD || isUnrestrictedRoute || (userPermissions.length && hasPermissionForRoute(path, userPermissions))) {\n          return response; // Allow access to the route if permissions are valid\n        } else {\n          // If authenticated user doesn't have permission for the route\n          return NextResponse.redirect(new URL(ROUTES.DASHBOARD, req.url));\n        }\n      }\n    } else if (!isPublicRoute) {\n      return NextResponse.redirect(new URL(ROUTES.LOGIN, req.url));\n    }\n    return response;\n  } catch (error) {\n    console.error(\"Error in middleware:\", error);\n    return NextResponse.error();\n  }\n}\n\nexport const config = {\n  matcher: [\"/((?!api|_next/static|_next/image|favicon.ico).*)\"],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAEA;AACA;;;;;AACA,iEAAiE;AAEjE,iDAAiD;AACjD,uBAAuB;AACvB,aAAa;AACb,sBAAsB;AACtB,6EAA6E;AAC7E,8BAA8B;AAC9B,OAAO;AACP,4BAA4B;AAC5B,KAAK;AAEL,2BAA2B;AAC3B,MAAM,qBAA2D;IAC/D,mBAAmB;IACnB,CAAC,kIAAA,CAAA,UAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,eAAe;IAErE,yDAAyD;IACzD,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QAAC,2IAAA,CAAA,aAAU,CAAC,0BAA0B;QAAE,2IAAA,CAAA,aAAU,CAAC,yBAAyB;KAAC;IAEpG,8EAA8E;IAC9E,CAAC,kIAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAClF,CAAC,kIAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,GAAG,aAAa,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAEjG,6EAA6E;IAC7E,CAAC,kIAAA,CAAA,UAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,qBAAqB;IAE7E,4DAA4D;IAC5D,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAC5D,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAC9D,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAC7D,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IACrE,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IACnE,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IACtE,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAC7D,CAAC,kIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,uBAAuB;IAE7D,0BAA0B;IAC1B,CAAC,kIAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,EAAE,2IAAA,CAAA,aAAU,CAAC,oBAAoB;AAG5D;AAEA,0CAA0C;AAC1C,MAAM,gBAAgB,CAAC;IACrB,6CAA6C;IAC7C,OAAO,SAAS,MAAM,OAAO,KAAK,OAAO,CAAC,OAAO;AACnD;AAEA,4EAA4E;AAC5E,MAAM,eAAe,CAAC;IACpB,6CAA6C;IAC7C,MAAM,eAAe,cAAc,QACjC,iDAAiD;KAChD,OAAO,CAAC,qBAAqB,OAC9B,sCAAsC;KACrC,OAAO,CAAC,qBAAqB,KAC9B,mCAAmC;KAClC,OAAO,CAAC,eAAe;IAE1B,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACvC;AAKA,gDAAgD;AAChD,MAAM,uBAAuB,IAAI,IAC/B,OAAO,OAAO,CAAC,oBACZ,MAAM,CAAC,CAAC,CAAC,QAAQ,GAAK,QAAQ,QAAQ,CAAC,MACvC,GAAG,CAAC,CAAC,CAAC,SAAS,YAAY,GAAmB;QAAC,aAAa;QAAU;KAAY,CACnF,4DAA4D;CAC3D,IAAI,CAAC,CAAC,GAAG;IACR,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,MAAM;IACnD,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,MAAM;IACnD,OAAO,YAAY;AACrB;AAGJ,uDAAuD;AACvD,MAAM,yBAAyB,IAAI,IAA+B,OAAO,OAAO,CAAC,oBAAoB,MAAM,CAAC,CAAC,CAAC,QAAQ,GAAK,CAAC,QAAQ,QAAQ,CAAC;AAE7I,uEAAuE;AACvE,MAAM,mBAAmB,CAAC,iBAA2B;IACnD,OAAO,MAAM,OAAO,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC,OAAS,gBAAgB,QAAQ,CAAC,SAAS,gBAAgB,QAAQ,CAAC;AACtH;AAEA,sEAAsE;AACtE,MAAM,wBAAwB,CAAC,MAAc;IAC3C,QAAQ,GAAG,CAAC,0BAA0B;IAEtC,2CAA2C;IAC3C,IAAI,CAAC,gBAAgB,MAAM,EAAE,OAAO;IAEpC,4CAA4C;IAC5C,MAAM,oBAAoB,uBAAuB,GAAG,CAAC;IAErD,IAAI,mBAAmB;QACrB,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,uBAAuB;IACvB,KAAK,MAAM,CAAC,SAAS,oBAAoB,IAAI,qBAAsB;QACjE,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,WAAW,EAAE,MAAM;QAC/E,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,IAAI,QAAQ,IAAI,CAAC,OAAO;YACtB,OAAO,iBAAiB,iBAAiB;QAC3C;IACF;IAEA,gFAAgF;IAChF,OAAO;AACT;AAEA,uCAAuC;AACvC,MAAM,kBAAkB,CAAI,cAAyC;IACnE,IAAI,CAAC,cAAc,OAAO;IAC1B,MAAM,cAAc,aAAa,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC;IAC5F,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI;QACF,mDAAmD;QACnD,MAAM,eAAe,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE;QAC9C,IAAI,CAAC,cAAc,OAAO;QAC1B,0DAA0D;QAC1D,OAAO,KAAK,KAAK,CAAC,mBAAmB;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,WAAW,QAAQ,CAAC,EAAE;QACrD,OAAO;IACT;AACF;AAEe,eAAe,WAAW,GAAgB;IACvD,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;YAAE;YAAK,QAAQ,QAAQ,GAAG,CAAC,eAAe;QAAC;QAO1E,MAAM,kBAAkB,CAAC,CAAC,SAAS;QACnC,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ;QACjC,MAAM,gBAAgB,kIAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC;QACnD,+DAA+D;QAC/D,MAAM,sBAAsB,kIAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC;QACzD,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;QAElC,IAAI,iBAAiB;YACnB,IAAI,eAAe;gBACjB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kIAAA,CAAA,UAAM,CAAC,SAAS,EAAE,IAAI,GAAG;YAChE,OAAO;gBACL,IAAI,qBAAqB;oBACvB,OAAO,UAAU,qDAAqD;gBACxE;gBACA,MAAM,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC;gBACrC,MAAM,sBAAsB,gBAA0B,cAAc,2IAAA,CAAA,0BAAuB;gBAC3F,MAAM,kBAAmB,qBAAqB,SAAS,sBAAsB,EAAE;gBAE/E,6CAA6C;gBAC7C,IAAI,SAAS,kIAAA,CAAA,UAAM,CAAC,SAAS,IAAI,uBAAwB,gBAAgB,MAAM,IAAI,sBAAsB,MAAM,kBAAmB;oBAChI,OAAO,UAAU,qDAAqD;gBACxE,OAAO;oBACL,8DAA8D;oBAC9D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kIAAA,CAAA,UAAM,CAAC,SAAS,EAAE,IAAI,GAAG;gBAChE;YACF;QACF,OAAO,IAAI,CAAC,eAAe;YACzB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kIAAA,CAAA,UAAM,CAAC,KAAK,EAAE,IAAI,GAAG;QAC5D;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,KAAK;IAC3B;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAoD;AAChE"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}