{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/validationSchema.ts"], "sourcesContent": ["import * as yup from \"yup\";\n\n// Regex patterns\nexport const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\nexport const NAME_REGEX = /^[a-zA-Z0-9\\s.'-]+$/;\n\n// Name validation regex - only allows alphabetic characters (including Unicode letters) and spaces\n// This pattern supports international names while rejecting digits and special characters\nexport const CANDIDATE_NAME_REGEX = /^[\\p{L}\\s]+$/u;\n\n// Employee validation schema\nexport const employeeValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    firstName: yup\n      .string()\n      .trim()\n      .required(translation(\"first_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(1, translation(\"min_first_name\"))\n      .max(50, translation(\"max_first_name\")),\n    lastName: yup\n      .string()\n      .trim()\n      .required(translation(\"last_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(1, translation(\"min_last_name\"))\n      .max(50, translation(\"max_last_name\")),\n    email: yup\n      .string()\n      .trim()\n      .required(translation(\"email_req\"))\n      .email(translation(\"email_val_msg\"))\n      .matches(EMAIL_REGEX, translation(\"email_val_msg\")),\n    department: yup\n      .number()\n      .transform((value) => (isNaN(value) ? undefined : value))\n      .required(translation(\"department_req\"))\n      .min(1, \"Department must be selected\"),\n    role: yup\n      .number()\n      .transform((value) => (isNaN(value) ? undefined : value))\n      .required(translation(\"role_req\"))\n      .min(1, \"Role must be selected\"),\n    // orderOfInterview: yup.string().required(translation(\"order_interview_req\")),\n  });\n\n// Employee array validation schema\nexport const employeesValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    employees: yup.array().of(employeeValidationSchema(translation)).required(\"At least one employee is required\"),\n  });\n\n// Department validation schema\nexport const departmentValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(translation(\"department_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(2, translation(\"min_department_name\"))\n      .max(50, translation(\"max_department_name\")),\n  });\n\n// Role validation schema\nexport const roleValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(translation(\"role_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(2, translation(\"min_role_name\"))\n      .max(50, translation(\"max_role_name\")),\n  });\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGO,MAAM,cAAc;AACpB,MAAM,aAAa;AAInB,MAAM,uBAAuB;AAG7B,MAAM,2BAA2B,CAAC,cACvC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,WAAW,CAAA,GAAA,sIAAA,CAAA,SACF,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,mBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,mBACnB,GAAG,CAAC,IAAI,YAAY;QACvB,UAAU,CAAA,GAAA,sIAAA,CAAA,SACD,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,IAAI,YAAY;QACvB,OAAO,CAAA,GAAA,sIAAA,CAAA,SACE,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,cACrB,KAAK,CAAC,YAAY,kBAClB,OAAO,CAAC,aAAa,YAAY;QACpC,YAAY,CAAA,GAAA,sIAAA,CAAA,SACH,AAAD,IACL,SAAS,CAAC,CAAC,QAAW,MAAM,SAAS,YAAY,OACjD,QAAQ,CAAC,YAAY,mBACrB,GAAG,CAAC,GAAG;QACV,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,SAAS,CAAC,CAAC,QAAW,MAAM,SAAS,YAAY,OACjD,QAAQ,CAAC,YAAY,aACrB,GAAG,CAAC,GAAG;IAEZ;AAGK,MAAM,4BAA4B,CAAC,cACxC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,WAAW,CAAA,GAAA,sIAAA,CAAA,QAAS,AAAD,IAAI,EAAE,CAAC,yBAAyB,cAAc,QAAQ,CAAC;IAC5E;AAGK,MAAM,6BAA6B,CAAC,cACzC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,wBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,wBACnB,GAAG,CAAC,IAAI,YAAY;IACzB;AAGK,MAAM,uBAAuB,CAAC,cACnC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,IAAI,YAAY;IACzB", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/validations/screenResumeValidations.ts"], "sourcesContent": ["// validations/screenResumeValidations.ts\nimport * as yup from \"yup\";\nimport { CANDIDATE_NAME_REGEX } from \"@/utils/validationSchema\";\n\nconst candidateSchema = (t: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(t(\"name_required\"))\n      .min(2, t(\"name_min\"))\n      .max(50, t(\"name_max\"))\n      .matches(CANDIDATE_NAME_REGEX, {\n        message: t(\"name_alpha_only\"),\n        excludeEmptyString: true,\n      }),\n    email: yup.string().trim().required(t(\"email_required\")).email(t(\"email_valid\")).max(50, t(\"email_max\")),\n    gender: yup.string().required(t(\"gender_required\")),\n    resume: yup.mixed().required(t(\"resume_required\")),\n    assessment: yup.mixed().optional().nullable(),\n    additionalInfo: yup.string().trim().optional().max(200, t(\"additional_max\")),\n  });\n\nexport const formSchemaValidation = (t: (key: string) => string) =>\n  yup.object({\n    candidates: yup.array().of(candidateSchema(t)).min(1, t(\"at_least_one_candidate\")),\n  });\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;AACzC;AACA;;;AAEA,MAAM,kBAAkB,CAAC,IACvB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,EAAE,kBACX,GAAG,CAAC,GAAG,EAAE,aACT,GAAG,CAAC,IAAI,EAAE,aACV,OAAO,CAAC,mIAAA,CAAA,uBAAoB,EAAE;YAC7B,SAAS,EAAE;YACX,oBAAoB;QACtB;QACF,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,EAAE,mBAAmB,KAAK,CAAC,EAAE,gBAAgB,GAAG,CAAC,IAAI,EAAE;QAC3F,QAAQ,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,EAAE;QAChC,QAAQ,CAAA,GAAA,sIAAA,CAAA,QAAS,AAAD,IAAI,QAAQ,CAAC,EAAE;QAC/B,YAAY,CAAA,GAAA,sIAAA,CAAA,QAAS,AAAD,IAAI,QAAQ,GAAG,QAAQ;QAC3C,gBAAgB,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,EAAE;IAC5D;AAEK,MAAM,uBAAuB,CAAC,IACnC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,EAAE;QACT,YAAY,CAAA,GAAA,sIAAA,CAAA,QAAS,AAAD,IAAI,EAAE,CAAC,gBAAgB,IAAI,GAAG,CAAC,GAAG,EAAE;IAC1D", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/screenResumeConstant.ts"], "sourcesContent": ["export const GENDER_OPTIONS = [\n  { value: \"Male\", label: \"Male\" },\n  { value: \"Female\", label: \"Female\" },\n];\n\nexport enum InterviewTabType {\n  UPCOMING = \"UpcomingInterviews\",\n  PAST = \"PastInterviews\",\n}\n\nexport const APPLICATION_UPDATE_STATUS = {\n  PROMOTED: \"Promoted\",\n  DEMOTED: \"Demoted\",\n};\n\nexport type APPLICATION_UPDATE_STATUS = (typeof APPLICATION_UPDATE_STATUS)[keyof typeof APPLICATION_UPDATE_STATUS];\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,MAAM,4BAA4B;IACvC,UAAU;IACV,SAAS;AACX", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/HoldIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype HoldIconProps = {\n  className?: string;\n  PrimaryColor?: boolean;\n};\n\nfunction HoldIcon({ className, PrimaryColor }: HoldIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" className={className}>\n      <path\n        d=\"M20.0746 0H14.0859C13.827 0 13.6172 0.209859 13.6172 0.46875C13.6172 0.727641 13.827 0.9375 14.0859 0.9375H20.0746C20.932 0.9375 21.6296 1.63505 21.6296 2.49239V21.5076C21.6296 22.365 20.932 23.0625 20.0746 23.0625H3.92505C3.06766 23.0625 2.37012 22.3649 2.37012 21.5075V2.49244C2.37012 1.63505 3.06766 0.9375 3.92505 0.9375H9.96129C10.2202 0.9375 10.43 0.727641 10.43 0.46875C10.43 0.209859 10.2202 0 9.96129 0H3.92505C2.55073 0 1.43262 1.11811 1.43262 2.49244V21.5076C1.43262 22.8819 2.55073 24 3.92505 24H20.0746C21.4489 24 22.5671 22.8819 22.5671 21.5076V2.49239C22.5671 1.11811 21.4489 0 20.0746 0Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M8.53423 4.54633V7.32878C8.07003 6.94197 7.49131 6.73145 6.88053 6.73145C6.62164 6.73145 6.41178 6.94131 6.41178 7.2002V10.7753C6.33068 13.8414 8.90553 16.5269 11.9797 16.528C15.0649 16.528 17.6343 13.8204 17.5475 10.7477V5.84359C17.5475 5.03495 16.8911 4.37702 16.0841 4.37702H16.0752C15.8895 4.37702 15.7118 4.41184 15.5483 4.47531C15.5211 3.66695 14.9015 3.06419 14.0858 3.06419C13.8903 3.06419 13.7036 3.10281 13.5329 3.1728C13.4017 2.47075 12.7994 1.95508 12.0548 1.95508C11.3246 1.95508 10.7143 2.47919 10.5789 3.17167C10.4058 3.10356 10.2146 3.06648 10.0113 3.06648C9.19686 3.06658 8.53423 3.73037 8.53423 4.54633ZM10.5508 4.54633C10.5508 4.57023 10.5507 9.1585 10.5507 9.1585C10.5507 9.41739 10.7606 9.62725 11.0195 9.62725C11.2784 9.62725 11.4882 9.41739 11.4882 9.1585L11.4883 3.46216C11.4883 3.14809 11.7424 2.89262 12.0547 2.89262C12.3834 2.89262 12.6219 3.13216 12.6219 3.46216L12.6211 9.01787C12.6211 9.27677 12.8309 9.48662 13.0898 9.48662C13.3487 9.48662 13.5586 9.27677 13.5586 9.01787C13.5586 9.01787 13.5594 4.5363 13.5594 4.53077C13.5594 4.23902 13.7955 4.00169 14.0858 4.00169C14.3954 4.00169 14.6117 4.21923 14.6117 4.53077L14.6116 9.1585C14.6116 9.41739 14.8214 9.62725 15.0803 9.62725C15.3392 9.62725 15.5491 9.41739 15.5491 9.1585C15.5491 9.1585 15.5492 5.86773 15.5492 5.84359C15.5492 5.55184 15.7851 5.31452 16.0752 5.31452H16.0841C16.3741 5.31452 16.61 5.55184 16.61 5.84359V10.7402C16.5528 13.2973 14.6555 15.5905 11.9798 15.5905C9.36546 15.5896 7.34928 13.3261 7.34928 10.7817V7.73627C7.61103 7.81352 7.85107 7.95569 8.04954 8.15462C8.30107 8.40686 8.53015 9.11251 8.53404 9.64248V10.7503C8.53404 10.9835 8.70317 11.181 8.93412 11.214C8.98432 11.2217 10.1696 11.4158 10.7034 12.5455C10.7835 12.7149 10.9519 12.8141 11.1275 12.8141C11.1946 12.8141 11.2628 12.7996 11.3275 12.769C11.5616 12.6585 11.6616 12.379 11.5511 12.145C11.0166 11.0139 10.0268 10.5572 9.47154 10.3838L9.47173 4.54637C9.47173 4.24736 9.71379 4.00408 10.0113 4.00408C10.4095 4.00408 10.5508 4.29616 10.5508 4.54633Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M14.0444 17.6445C13.7855 17.6445 13.5757 17.8544 13.5757 18.1133V21.1865C13.5757 21.6615 14.042 21.6582 14.3669 21.6582C14.5327 21.6582 14.7728 21.6573 15.1241 21.6553C15.3829 21.6538 15.5917 21.4428 15.5902 21.1839C15.5888 20.9259 15.3792 20.7178 15.1215 20.7178C15.1206 20.7178 15.1197 20.7178 15.1189 20.7178C14.913 20.719 14.6961 20.7198 14.5132 20.7202V18.1133C14.5132 17.8544 14.3033 17.6445 14.0444 17.6445Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M7.48963 21.6579C7.74852 21.6579 7.95838 21.448 7.95838 21.1891V18.1133C7.95838 17.8544 7.74852 17.6445 7.48963 17.6445C7.23074 17.6445 7.02088 17.8544 7.02088 18.1133V19.1297H5.82983V18.1133C5.82983 17.8544 5.61997 17.6445 5.36108 17.6445C5.10219 17.6445 4.89233 17.8544 4.89233 18.1133V21.1891C4.89233 21.448 5.10219 21.6579 5.36108 21.6579C5.61997 21.6579 5.82983 21.448 5.82983 21.1891V20.0672H7.02088V21.1891C7.02088 21.448 7.23074 21.6579 7.48963 21.6579Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M16.2369 21.1799C16.2531 21.3965 16.4816 21.6138 16.7056 21.6138C16.7312 21.6137 17.3346 21.6113 17.5761 21.6071C18.5271 21.5905 19.2173 20.7685 19.2173 19.6526C19.2173 18.4795 18.5445 17.6914 17.5431 17.6914H16.6978C16.436 17.6914 16.229 17.9032 16.229 18.1632C16.2291 18.1632 16.232 21.1528 16.2369 21.1799ZM17.5431 18.6289C18.2267 18.6289 18.2798 19.4124 18.2798 19.6526C18.2798 20.1526 18.0571 20.6611 17.5597 20.6697C17.4683 20.6713 17.3194 20.6727 17.171 20.6737C17.1699 20.3296 17.168 18.9877 17.1673 18.6289H17.5431Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M8.76245 19.6512C8.76245 20.7577 9.66264 21.6579 10.7691 21.6579C11.8756 21.6579 12.7758 20.7577 12.7758 19.6512C12.7758 18.5447 11.8756 17.6445 10.7691 17.6445C9.66264 17.6445 8.76245 18.5447 8.76245 19.6512ZM11.8383 19.6512C11.8383 20.2407 11.3587 20.7204 10.7691 20.7204C10.1796 20.7204 9.69995 20.2407 9.69995 19.6512C9.69995 19.0617 10.1796 18.582 10.7691 18.582C11.3587 18.582 11.8383 19.0617 11.8383 19.6512Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M11.6406 0.647517C11.7161 0.831736 11.9093 0.951173 12.108 0.935705C12.3077 0.920142 12.4789 0.776611 12.528 0.582267C12.6287 0.183783 12.1825 -0.141202 11.8327 0.0667358C11.6361 0.183548 11.5527 0.436392 11.6406 0.647517Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n    </svg>\n  );\n}\n\nexport default HoldIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,SAAS,EAAE,SAAS,EAAE,YAAY,EAAiB;IAC1D,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,6LAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;;;;;;;AAIzC;KAjCS;uCAmCM", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,6LAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;KAD9C;AAIN;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,6LAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,6LAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,6LAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,6LAAC,+IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/DeleteIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction DeleteIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"28\" viewBox=\"0 0 25 28\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.3291 27.3361C10.5225 27.3361 8.76112 27.3161 7.01846 27.2801C4.78912 27.2361 3.24646 25.7908 2.99446 23.5081C2.57446 19.7215 1.85579 10.7961 1.84912 10.7068C1.80379 10.1561 2.21446 9.67347 2.76512 9.62947C3.30779 9.6148 3.79846 9.99614 3.84246 10.5455C3.84912 10.6361 4.56646 19.5308 4.98246 23.2881C5.12512 24.5855 5.82512 25.2548 7.05979 25.2801C10.3931 25.3508 13.7945 25.3548 17.4611 25.2881C18.7731 25.2628 19.4825 24.6068 19.6291 23.2788C20.0425 19.5535 20.7625 10.6361 20.7705 10.5455C20.8145 9.99614 21.3011 9.61214 21.8465 9.62947C22.3971 9.6748 22.8078 10.1561 22.7638 10.7068C22.7558 10.7975 22.0331 19.7455 21.6171 23.4988C21.3585 25.8281 19.8198 27.2455 17.4971 27.2881C15.7198 27.3188 14.0051 27.3361 12.3291 27.3361Z\"\n        fill=\"#D00000\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M23.6107 7.32031H1C0.448 7.32031 0 6.87231 0 6.32031C0 5.76831 0.448 5.32031 1 5.32031H23.6107C24.1627 5.32031 24.6107 5.76831 24.6107 6.32031C24.6107 6.87231 24.1627 7.32031 23.6107 7.32031Z\"\n        fill=\"#D00000\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M19.2538 7.31997C17.7364 7.31997 16.4191 6.23864 16.1204 4.75064L15.7964 3.1293C15.7284 2.88264 15.4471 2.66797 15.1271 2.66797H9.48311C9.16311 2.66797 8.88178 2.88264 8.80045 3.19064L8.48978 4.75064C8.19245 6.23864 6.87378 7.31997 5.35645 7.31997C4.80445 7.31997 4.35645 6.87197 4.35645 6.31997C4.35645 5.76797 4.80445 5.31997 5.35645 5.31997C5.92445 5.31997 6.41778 4.91464 6.52978 4.3573L6.85378 2.73597C7.18311 1.4933 8.25911 0.667969 9.48311 0.667969H15.1271C16.3511 0.667969 17.4271 1.4933 17.7431 2.67597L18.0818 4.3573C18.1924 4.91464 18.6858 5.31997 19.2538 5.31997C19.8058 5.31997 20.2538 5.76797 20.2538 6.31997C20.2538 6.87197 19.8058 7.31997 19.2538 7.31997Z\"\n        fill=\"#D00000\"\n      />\n    </svg>\n  );\n}\n\nexport default DeleteIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KAvBS;uCAyBM", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/InfoIcon.tsx"], "sourcesContent": ["import React, { useId } from \"react\";\nimport { Tooltip } from \"react-tooltip\";\n\ninterface InfoIconProps {\n  tooltip: React.ReactNode;\n  id?: string;\n  place?: \"top\" | \"bottom\" | \"left\" | \"right\";\n  className?: string;\n}\n\nfunction InfoIcon({ tooltip, id, place = \"bottom\", className }: InfoIconProps) {\n  const generatedId = useId();\n  const anchorId = id || `info-icon-${generatedId}`;\n  return (\n    <>\n      <span id={anchorId} className={className}>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 23 23\" style={{ cursor: \"pointer\" }} fill=\"none\">\n          <g clipPath=\"url(#clip0_9605_3144)\">\n            <path\n              d=\"M11.5 2.15625C9.65198 2.15625 7.84547 2.70425 6.30889 3.73096C4.77232 4.75766 3.57471 6.21695 2.8675 7.9243C2.1603 9.63165 1.97526 11.5104 2.33579 13.3229C2.69632 15.1354 3.58623 16.8003 4.89298 18.107C6.19972 19.4138 7.86462 20.3037 9.67713 20.6642C11.4896 21.0247 13.3684 20.8397 15.0757 20.1325C16.783 19.4253 18.2423 18.2277 19.269 16.6911C20.2958 15.1545 20.8438 13.348 20.8438 11.5C20.8411 9.02269 19.8559 6.64759 18.1041 4.89586C16.3524 3.14413 13.9773 2.15887 11.5 2.15625ZM11.1406 6.46875C11.3539 6.46875 11.5623 6.53198 11.7396 6.65045C11.9169 6.76891 12.0551 6.93729 12.1367 7.13429C12.2183 7.3313 12.2396 7.54807 12.198 7.75721C12.1564 7.96634 12.0538 8.15845 11.903 8.30922C11.7522 8.46 11.5601 8.56268 11.351 8.60428C11.1418 8.64588 10.925 8.62453 10.728 8.54293C10.531 8.46133 10.3627 8.32315 10.2442 8.14585C10.1257 7.96855 10.0625 7.76011 10.0625 7.54688C10.0625 7.26094 10.1761 6.98671 10.3783 6.78453C10.5805 6.58234 10.8547 6.46875 11.1406 6.46875ZM12.2188 16.5312C11.8375 16.5312 11.4719 16.3798 11.2023 16.1102C10.9327 15.8406 10.7813 15.475 10.7813 15.0938V11.5C10.5906 11.5 10.4078 11.4243 10.273 11.2895C10.1382 11.1547 10.0625 10.9719 10.0625 10.7812C10.0625 10.5906 10.1382 10.4078 10.273 10.273C10.4078 10.1382 10.5906 10.0625 10.7813 10.0625C11.1625 10.0625 11.5281 10.214 11.7977 10.4835C12.0673 10.7531 12.2188 11.1188 12.2188 11.5V15.0938C12.4094 15.0938 12.5922 15.1695 12.727 15.3043C12.8618 15.4391 12.9375 15.6219 12.9375 15.8125C12.9375 16.0031 12.8618 16.1859 12.727 16.3207C12.5922 16.4555 12.4094 16.5312 12.2188 16.5312Z\"\n              fill=\"#436EB6\"\n            />\n          </g>\n          <defs>\n            <clipPath id=\"clip0_9605_3144\">\n              <rect width=\"23\" height=\"23\" fill=\"white\" />\n            </clipPath>\n          </defs>\n        </svg>\n      </span>\n      <Tooltip anchorSelect={`#${anchorId}`} className=\"responsive-tooltip\" place={place}>\n        {tooltip}\n      </Tooltip>\n    </>\n  );\n}\n\nexport default InfoIcon;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AASA,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAiB;;IAC3E,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IACxB,MAAM,WAAW,MAAM,CAAC,UAAU,EAAE,aAAa;IACjD,qBACE;;0BACE,6LAAC;gBAAK,IAAI;gBAAU,WAAW;0BAC7B,cAAA,6LAAC;oBAAI,OAAM;oBAA6B,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,OAAO;wBAAE,QAAQ;oBAAU;oBAAG,MAAK;;sCACpH,6LAAC;4BAAE,UAAS;sCACV,cAAA,6LAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;sCAGT,6LAAC;sCACC,cAAA,6LAAC;gCAAS,IAAG;0CACX,cAAA,6LAAC;oCAAK,OAAM;oCAAK,QAAO;oCAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC,uKAAA,CAAA,UAAO;gBAAC,cAAc,CAAC,CAAC,EAAE,UAAU;gBAAE,WAAU;gBAAqB,OAAO;0BAC1E;;;;;;;;AAIT;GAzBS;;QACa,6JAAA,CAAA,QAAK;;;KADlB;uCA2BM", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/screenResumeServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { http } from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { IUploadManualCandidate } from \"@/interfaces/screenResumeInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\n// Define interface for pagination parameters\ninterface PaginationParams {\n  limit?: number;\n  offset?: number;\n  job_id?: number;\n  status?: string;\n}\n\ninterface JobApplication {\n  application_id: number;\n  job_id: number;\n  hiring_manager_id: number;\n  candidate_id: number;\n  candidate_name: string;\n  ai_decision: string;\n  ai_reason: string;\n  status?: string;\n  created_ts: string;\n}\n\ninterface JobApplicationResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication[];\n  pagination: {\n    limit: number;\n    offset: number;\n    totalCount: number;\n    hasMore: boolean;\n  };\n}\n\ninterface ChangeApplicationStatusParams {\n  job_id: number;\n  candidate_id: number;\n  hiring_manager_id: number;\n  status: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];\n  hiring_manager_reason: string;\n}\n\ninterface ChangeApplicationStatusResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication;\n}\n/**\n * Upload resume and assessment files and get presigned URLs\n * @param file - The file to upload (resume or assessment)\n * @returns Promise with presigned URL response\n */\nexport const getPresignedUrl = async (file: File): Promise<ApiResponse> => {\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  formData.append(\"fileType\", file.type);\n  formData.append(\"fileName\", file.name);\n\n  return http.post(endpoint.resumeScreen.GET_PRESIGNED_URL, formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n};\n\n/**\n * Upload file to S3 using presigned URL\n * @param presignedUrl - The presigned URL for S3 upload\n * @param file - The file to upload\n * @returns Promise with upload response\n */\nexport const uploadToS3 = async (presignedUrl: string, file: File): Promise<Response> => {\n  return fetch(presignedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\n/**\n * Process the file upload to get presigned URL and upload to S3\n * @param file - The file to upload\n * @returns Object with file URL and parsed text\n */\nexport const processFileUpload = async (file: File): Promise<{ fileUrl: string; fileText: string; presignedUrl: string }> => {\n  try {\n    // Get presigned URL\n    const presignedUrlResponse = await getPresignedUrl(file);\n\n    if (!presignedUrlResponse.data) {\n      throw new Error(\"Failed to get presigned URL\");\n    }\n    const responseData = presignedUrlResponse.data;\n\n    // The response might have data nested inside another data property\n    const urlData = responseData.data;\n\n    if (!urlData.presignedUrl || !urlData.fileUrl) {\n      console.error(\"Missing URL information in response:\", urlData);\n      throw new Error(\"Missing URL information in response\");\n    }\n\n    const { presignedUrl, fileUrl, fileText } = urlData;\n\n    // Upload file to S3\n    const uploadResponse = await uploadToS3(presignedUrl, file);\n    if (!uploadResponse.ok) {\n      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);\n    }\n    // Return the file URL and flag for backend extraction\n    return {\n      fileUrl,\n      fileText: fileText, // Special flag to indicate backend should extract text\n      presignedUrl,\n    };\n  } catch (error) {\n    console.error(\"Error processing file upload:\", error);\n    // Include error details in the console for debugging\n    if (error instanceof Error) {\n      console.error(\"Error message:\", error.message);\n      console.error(\"Error stack:\", error.stack);\n    }\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidate data with resume and assessment\n * @param data - The form values with candidate information\n * @returns Promise with API response\n */\n/**\n * Get all job applications with pagination (not just pending)\n * @param params - Pagination parameters (limit, offset, filters)\n * @returns Promise with job applications response\n */\nexport const getAllPendingJobApplications = async (params: PaginationParams): Promise<ApiResponse<JobApplicationResponse>> => {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    if (params.limit) queryParams.append(\"limit\", params.limit.toString());\n    // Always include offset parameter, even when it's 0\n    queryParams.append(\"offset\", params.offset !== undefined ? params.offset.toString() : \"0\");\n    if (params.job_id) queryParams.append(\"job_id\", params.job_id.toString());\n    if (params.status) queryParams.append(\"status\", params.status);\n\n    // Make API request\n    const url = `${endpoint.resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;\n    return http.get(url);\n  } catch (error) {\n    console.error(\"Error fetching job applications:\", error);\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidates to the backend for processing\n *\n * This function sends candidate data to the backend API for manual candidate upload.\n * The candidates should already have their files uploaded to S3 and contain URLs\n * instead of File objects.\n *\n * @param {Object} uploadManualCandidateData - The data object containing candidates and job information\n * @param {IUploadManualCandidate[]} uploadManualCandidateData.candidates - Array of candidate objects with uploaded file URLs\n * @param {number} uploadManualCandidateData.jobId - The ID of the job to associate candidates with\n *\n * @returns {Promise<ApiResponse>} Promise that resolves to the API response containing upload results\n *\n * @example\n * ```typescript\n * const candidateData = {\n *   candidates: [\n *     {\n *       name: \"John Doe\",\n *       email: \"<EMAIL>\",\n *       gender: \"male\",\n *       resume: \"https://cdn.example.com/resume.pdf\",\n *       assessment: \"https://cdn.example.com/assessment.pdf\",\n *       additionalInfo: \"Experienced developer\"\n *     }\n *   ],\n *   jobId: 123\n * };\n *\n * const response = await uploadManualCandidate(candidateData);\n * ```\n */\nexport const uploadManualCandidate = async (uploadManualCandidateData: {\n  candidates: IUploadManualCandidate[];\n  job_id: number;\n}): Promise<ApiResponse> => {\n  return http.post(endpoint.resumeScreen.MANUAL_CANDIDATE_UPLOAD, uploadManualCandidateData);\n};\n\n/**\n * Change the status of a job application (Approve, Reject, or Hold)\n * @param params - Parameters containing job_id, candidate_id, hiring_manager_id, and status\n * @param data - Data containing hiring_manager_reason\n * @returns Promise with API response\n */\nexport const changeApplicationStatus = async (data: ChangeApplicationStatusParams): Promise<ApiResponse<ChangeApplicationStatusResponse>> => {\n  return http.post(endpoint.resumeScreen.CHANGE_APPLICATION_STATUS, data);\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAuDO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IACrC,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IAErC,OAAO,uHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE,UAAU;QAClE,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAQO,MAAM,aAAa,OAAO,cAAsB;IACrD,OAAO,MAAM,cAAc;QACzB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,oBAAoB;QACpB,MAAM,uBAAuB,MAAM,gBAAgB;QAEnD,IAAI,CAAC,qBAAqB,IAAI,EAAE;YAC9B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,qBAAqB,IAAI;QAE9C,mEAAmE;QACnE,MAAM,UAAU,aAAa,IAAI;QAEjC,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,OAAO,EAAE;YAC7C,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;QAE5C,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,WAAW,cAAc;QACtD,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,eAAe,MAAM,EAAE;QACzE;QACA,sDAAsD;QACtD,OAAO;YACL;YACA,UAAU;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,qDAAqD;QACrD,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;YAC7C,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C;QACA,MAAM;IACR;AACF;AAYO,MAAM,+BAA+B,OAAO;IACjD,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACnE,oDAAoD;QACpD,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,CAAC,QAAQ,KAAK;QACtF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAE7D,mBAAmB;QACnB,MAAM,MAAM,GAAG,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;QACjG,OAAO,uHAAA,CAAA,OAAI,CAAC,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAkCO,MAAM,wBAAwB,OAAO;IAI1C,OAAO,uHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAE;AAClE;AAQO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,uHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,yBAAyB,EAAE;AACpE", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC,iKAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;KAtBwB;AAwBjB,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP;MARgB", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Select.tsx"], "sourcesContent": ["import { HTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\nimport Loader from \"../loader/Loader\";\n\ninterface SelectProps<T extends FieldValues> extends HTMLAttributes<HTMLSelectElement> {\n  name: Path<T>;\n  placeholder?: string;\n  disabled?: boolean;\n  control: Control<T>;\n  options: Array<{ label: string; value: string | number }>;\n  isLoading?: boolean;\n}\n\nexport default function Select<T extends FieldValues>({ options, name, control, disabled, placeholder, isLoading, ...props }: SelectProps<T>) {\n  return (\n    <Controller\n      name={name}\n      control={control}\n      render={({ field }) => (\n        <select {...props} disabled={disabled} value={field.value} onChange={field.onChange} aria-label=\"\">\n          <option value=\"\">{placeholder}</option>\n          {isLoading ? (\n            <option value=\"0000\">\n              <Loader />\n            </option>\n          ) : (\n            options.map((data) => (\n              <option key={data.value} value={data.value}>\n                {data.label}\n              </option>\n            ))\n          )}\n        </select>\n      )}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAWe,SAAS,OAA8B,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAuB;IAC1I,qBACE,6LAAC,iKAAA,CAAA,aAAU;QACT,MAAM;QACN,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;gBAAQ,GAAG,KAAK;gBAAE,UAAU;gBAAU,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;kCAC9F,6LAAC;wBAAO,OAAM;kCAAI;;;;;;oBACjB,0BACC,6LAAC;wBAAO,OAAM;kCACZ,cAAA,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;iCAGT,QAAQ,GAAG,CAAC,CAAC,qBACX,6LAAC;4BAAwB,OAAO,KAAK,KAAK;sCACvC,KAAK,KAAK;2BADA,KAAK,KAAK;;;;;;;;;;;;;;;;AASrC;KAvBwB", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,6LAAC,iKAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,6LAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB;KATwB", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/resume/ManualUploadResume.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useSelector } from \"react-redux\";\r\n\r\nimport { useForm, useFieldArray, Controller, Resolver, UseFormSetError } from \"react-hook-form\";\r\nimport { yupResolver } from \"@hookform/resolvers/yup\";\r\n\r\nimport { formSchemaValidation } from \"@/validations/screenResumeValidations\";\r\n\r\nimport ROUTES from \"@/constants/routes\";\r\nimport { GENDER_OPTIONS } from \"@/constants/screenResumeConstant\";\r\n\r\nimport { IFormValues } from \"@/interfaces/screenResumeInterfaces\";\r\nimport { RootState } from \"@/redux/store\";\r\n\r\nimport Loader from \"@/components/loader/Loader\";\r\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\r\nimport HoldIcon from \"@/components/svgComponents/HoldIcon\";\r\nimport Button from \"@/components/formElements/Button\";\r\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\r\nimport DeleteIcon from \"@/components/svgComponents/DeleteIcon\";\r\nimport InfoIcon from \"@/components/svgComponents/InfoIcon\";\r\n\r\nimport { toastMessageSuccess, toastMessageError, uploadFileOnS3, dismissAllToasts } from \"@/utils/helper\";\r\n\r\nimport style from \"@/styles/commonPage.module.scss\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { PDF_FILE_SIZE_LIMIT, PDF_FILE_TYPE, PLAN_CONFIG, PlanConfigType, PLAN_NAMES } from \"@/constants/commonConstants\";\r\nimport { uploadManualCandidate } from \"@/services/screenResumeServices\";\r\nimport Textbox from \"@/components/formElements/Textbox\";\r\nimport Select from \"@/components/formElements/Select\";\r\nimport Textarea from \"@/components/formElements/Textarea\";\r\n\r\n/**\r\n * ManualUploadResume Component\r\n *\r\n * Allows hiring managers to manually upload candidate resumes and assessments.\r\n * Supports adding multiple candidates (up to 5) with validation for required fields.\r\n *\r\n * @returns {JSX.Element} The rendered ManualUploadResume component\r\n */\r\n\r\nfunction ManualUploadResume({\r\n  params,\r\n  searchParams,\r\n}: {\r\n  params: Promise<{ jobId: string }>;\r\n  searchParams: Promise<{ title: string; jobUniqueId: string }>;\r\n}) {\r\n  const router = useRouter();\r\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const paramsPromise = React.use(params);\r\n  const searchParamsPromise = React.use(searchParams);\r\n  const t = useTranslations();\r\n\r\n  // Get current plan from Redux store\r\n  const currentPlan = useSelector((state: RootState) => state.auth.currentPlan);\r\n  console.log(\"currentPlan\", currentPlan);\r\n  // Get current plan name (default to 'free' if no plan)\r\n  const currentPlanName = currentPlan?.subscriptionPlanName || PLAN_NAMES.FREE;\r\n\r\n  // Get plan configuration based on current plan\r\n  const getCurrentPlanConfig = (): PlanConfigType => {\r\n    if (currentPlanName.includes(PLAN_NAMES.PRO)) return PLAN_CONFIG.pro;\r\n    if (currentPlanName.includes(PLAN_NAMES.GROWTH)) return PLAN_CONFIG.growth;\r\n    if (currentPlanName.includes(PLAN_NAMES.ENTERPRISE)) return PLAN_CONFIG.enterprise;\r\n    return PLAN_CONFIG.free; // Default to free plan\r\n  };\r\n\r\n  const { showAddButton } = getCurrentPlanConfig();\r\n  // Initialize form with validation schema\r\n  const {\r\n    control,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    setError,\r\n    reset,\r\n  } = useForm<IFormValues>({\r\n    resolver: yupResolver(formSchemaValidation(t)) as unknown as Resolver<IFormValues>,\r\n    defaultValues: {\r\n      candidates: [{ name: \"\", email: \"\", gender: \"\", resume: null, assessment: null, additionalInfo: \"\" }],\r\n    },\r\n  });\r\n\r\n  // Initialize field array for dynamic candidates\r\n  const { fields, append, remove } = useFieldArray({\r\n    control,\r\n    name: \"candidates\",\r\n  });\r\n\r\n  // State for loading status during form submission\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [candidateErrors, setCandidateErrors] = useState<Record<string, string>>({});\r\n  const [candidateSuccess, setCandidateSuccess] = useState<Record<string, boolean>>({});\r\n  const resumeFileRef = useRef<HTMLInputElement>(null);\r\n  const assessmentFileRef = useRef<HTMLInputElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {\r\n      router.push(ROUTES.JOBS.ACTIVE_JOBS);\r\n    }\r\n  }, [paramsPromise.jobId, searchParamsPromise?.title]);\r\n\r\n  /**\r\n   * Checks for duplicate email addresses within the list of candidates.\r\n   * Sets custom errors on the 'email' field of duplicate entries using RHF's setError.\r\n   *\r\n   * @param candidates Array of candidate objects from the form (IFormValues[\"candidates\"]).\r\n   * @param setError RHF's setError function from useForm. // Updated type hint in JSDoc\r\n   * @param t Translation function.\r\n   * @returns boolean - True if duplicates are found, false otherwise.\r\n   */\r\n  const checkForDuplicateEmails = (\r\n    candidates: IFormValues[\"candidates\"],\r\n    setError: UseFormSetError<IFormValues>, // Use the specific RHF type\r\n    t: (key: string) => string\r\n  ): boolean => {\r\n    const emailMap = new Map<string, number[]>(); // Map email to list of indices\r\n    let hasDuplicates = false;\r\n\r\n    // 1. Collect indices for each email\r\n    candidates.forEach((candidate, index) => {\r\n      const email = candidate.email?.trim().toLowerCase(); // Normalize email\r\n      if (email) {\r\n        // Only check non-empty emails\r\n        if (!emailMap.has(email)) {\r\n          emailMap.set(email, []);\r\n        }\r\n        emailMap.get(email)!.push(index); // Non-null assertion as we just set it\r\n      }\r\n    });\r\n\r\n    // 2. Identify duplicates and set errors\r\n    emailMap.forEach((indices) => {\r\n      if (indices.length > 1) {\r\n        hasDuplicates = true;\r\n        // Set error for all occurrences of the duplicate email\r\n        indices.forEach((index) => {\r\n          // Use template literal type for the field name to satisfy TS\r\n          setError(`candidates.${index}.email` as const, {\r\n            type: \"manual\", // Indicate it's a manual/custom error\r\n            message: t(\"email_duplicate_in_form\"), // Add this key to your translations\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    return hasDuplicates;\r\n  };\r\n  /**\r\n   * Handles form submission for candidate uploads\r\n   *\r\n   * Processes the submitted form data, uploads files to S3, transforms File objects to URLs,\r\n   * calls the API to upload candidates, shows success/error messages, and redirects on success.\r\n   *\r\n   * @param {IFormValues} data - The validated form data containing candidate information\r\n   * @returns {Promise<void>}\r\n   */\r\n  const onSubmit = async (data: IFormValues) => {\r\n    // Prevent multiple simultaneous submissions\r\n    if (isSubmitting) {\r\n      return;\r\n    }\r\n    const hasDuplicates = checkForDuplicateEmails(data.candidates, setError, t);\r\n    if (hasDuplicates) {\r\n      // Scroll to the first error or top of the form\r\n      setTimeout(() => {\r\n        window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n        // Optionally, find the first element with an error and focus/scroll to it\r\n        // const firstErrorElement = document.querySelector('.auth-msg.error'); // Example\r\n        // if (firstErrorElement) firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n      }, 100); // Small delay to let DOM update\r\n      return; // Stop submission if duplicates found\r\n    }\r\n    try {\r\n      setIsSubmitting(true);\r\n      // Clear any existing toast notifications before showing new ones\r\n      dismissAllToasts();\r\n      setCandidateErrors({}); // Clear previous errors\r\n      // Note: Keep candidateSuccess states to preserve successful candidates\r\n\r\n      // Transform candidates by uploading files to S3 and converting File objects to URLs\r\n      // Filter out candidates that were already successfully processed\r\n      const transformedCandidates = [];\r\n      // Use string keys (field.id) for errors\r\n      const uploadErrors: Record<string, string> = {};\r\n\r\n      // Process candidates sequentially to handle errors properly\r\n      for (let index = 0; index < data.candidates.length; index++) {\r\n        const candidate = data.candidates[index];\r\n        // --- CHANGE 1: Get the field.id for the current index ---\r\n        const fieldId = fields[index]?.id; // Get the unique ID for this candidate's field\r\n\r\n        // --- CHANGE 2: Check success using field.id ---\r\n        if (candidateSuccess[fieldId] === true) {\r\n          continue;\r\n        }\r\n\r\n        let hasUploadError = false;\r\n        let errorMessage = \"\";\r\n\r\n        try {\r\n          // Generate unique file paths for each candidate's files\r\n          const timestamp = Date.now() + index; // Add index to ensure uniqueness\r\n\r\n          // Process resume file (required)\r\n          let resumeUrl: string = \"\";\r\n          const candidateNameReplace = candidate.name.split(\" \")[0];\r\n          console.log(\"candidateNameReplace======>\", candidateNameReplace);\r\n          if (candidate.resume) {\r\n            const resumeFilePath = `${paramsPromise.jobId}/resumes/${candidateNameReplace}-resume-${timestamp}.pdf`;\r\n            const uploadResult = await uploadFileOnS3(candidate.resume, resumeFilePath);\r\n            if (!uploadResult) {\r\n              hasUploadError = true;\r\n              errorMessage = t(\"resume_upload_failed\");\r\n            } else {\r\n              resumeUrl = uploadResult;\r\n            }\r\n          } else {\r\n            hasUploadError = true;\r\n            errorMessage = t(\"resume_file_required\");\r\n          }\r\n\r\n          // Process assessment file (optional) - only if resume upload succeeded\r\n          let assessmentUrl: string = \"\";\r\n          if (!hasUploadError && candidate.assessment) {\r\n            const assessmentFilePath = `${paramsPromise.jobId}/assessments/${candidateNameReplace}-assessment-${timestamp}.pdf`;\r\n            const uploadResult = await uploadFileOnS3(candidate.assessment, assessmentFilePath);\r\n            console.log(\"assessmentUrl=========>\", uploadResult);\r\n            if (!uploadResult) {\r\n              hasUploadError = true;\r\n              errorMessage = t(\"assessment_file_upload_failed\");\r\n            } else {\r\n              assessmentUrl = uploadResult;\r\n            }\r\n          }\r\n\r\n          // Only add to transformedCandidates if no upload errors occurred\r\n          if (!hasUploadError) {\r\n            transformedCandidates.push({\r\n              name: candidate.name,\r\n              email: candidate.email,\r\n              gender: candidate.gender,\r\n              resume_file: resumeUrl,\r\n              assessment_file: assessmentUrl,\r\n              additional_details: candidate.additionalInfo,\r\n            });\r\n          } else {\r\n            // --- CHANGE 3: Store the upload error using field.id ---\r\n            if (fieldId) {\r\n              // Ensure fieldId exists before storing\r\n              uploadErrors[fieldId] = errorMessage;\r\n            }\r\n          }\r\n        } catch (fileUploadError) {\r\n          console.error(`Error uploading files for candidate ${index + 1}:`, fileUploadError);\r\n          // --- CHANGE 4: Store caught error using field.id ---\r\n          if (fieldId) {\r\n            // Ensure fieldId exists before storing\r\n            uploadErrors[fieldId] = (fileUploadError as Error)?.message || t(\"failed_to_upload_files\");\r\n          }\r\n        }\r\n      }\r\n\r\n      // Set upload errors immediately on the UI\r\n      if (Object.keys(uploadErrors).length > 0) {\r\n        setCandidateErrors(uploadErrors); // Now keys are field IDs\r\n      }\r\n\r\n      const jobId = Number(paramsPromise.jobId);\r\n\r\n      // Only proceed with backend submission if we have candidates with successful uploads\r\n      if (transformedCandidates.length > 0) {\r\n        // Upload candidates with transformed data (URLs instead of File objects)\r\n        const response = await uploadManualCandidate({ candidates: transformedCandidates, job_id: jobId });\r\n\r\n        if (response.data && response.data.results && response.data.results.length > 0) {\r\n          // Process results to show per-candidate status\r\n          // --- CHANGE 5: Initialize result state objects with string keys ---\r\n          const errors: Record<string, string> = { ...uploadErrors }; // Start with upload errors (keys are field IDs now)\r\n          const success: Record<string, boolean> = { ...candidateSuccess }; // Preserve existing success states (keys are field IDs)\r\n          let successCount = Object.values(candidateSuccess).filter(Boolean).length; // Count existing successes\r\n          let errorCount = Object.keys(uploadErrors).length; // Count upload errors\r\n\r\n          // Create mapping from transformed candidates back to original indices\r\n          let transformedIndex = 0;\r\n          for (let originalIndex = 0; originalIndex < data.candidates.length; originalIndex++) {\r\n            // --- CHANGE 6: Get the field.id for the current original index ---\r\n            const originalFieldId = fields[originalIndex]?.id;\r\n\r\n            // Skip candidates that had upload errors (check using field.id)\r\n            if (uploadErrors[originalFieldId]) {\r\n              continue;\r\n            }\r\n\r\n            // Skip candidates that were already successfully processed (not sent to backend) (check using field.id)\r\n            if (candidateSuccess[originalFieldId] === true) {\r\n              continue;\r\n            }\r\n\r\n            // Process backend result for this candidate\r\n            if (transformedIndex < response.data.results.length) {\r\n              const result = response.data.results[transformedIndex];\r\n              if (!result.success && result.error) {\r\n                // --- CHANGE 7: Store backend error using field.id ---\r\n                if (originalFieldId) {\r\n                  // Ensure fieldId exists\r\n                  errors[originalFieldId] = result.error;\r\n                  success[originalFieldId] = false; // Explicitly mark as not successful\r\n                  errorCount++;\r\n                }\r\n              } else if (result.success) {\r\n                // --- CHANGE 8: Store backend success using field.id ---\r\n                if (originalFieldId) {\r\n                  // Ensure fieldId exists\r\n                  success[originalFieldId] = true;\r\n                  successCount++;\r\n                }\r\n              }\r\n              transformedIndex++;\r\n            }\r\n          }\r\n\r\n          // Update the states (preserve all candidates in UI, using field IDs)\r\n          setCandidateErrors(errors); // Keys are now field IDs\r\n          setCandidateSuccess(success); // Keys are now field IDs\r\n\r\n          const skippedCount = Object.values(candidateSuccess).filter(Boolean).length; // Recalculate based on updated state if needed, or use previous count\r\n\r\n          // Show consolidated toast messages\r\n          const successMessages: string[] = [];\r\n          const errorMessages: string[] = [];\r\n\r\n          // Add skip message if applicable\r\n          if (skippedCount > 0) {\r\n            const skipKey = skippedCount === 1 ? \"candidate_skipped_success\" : \"candidates_skipped_success\";\r\n            successMessages.push(`${skippedCount} ${t(skipKey)}`);\r\n          }\r\n\r\n          // Add processing results to appropriate message arrays\r\n          if (successCount > 0) {\r\n            const candidateKey = successCount === 1 ? \"candidate_small\" : \"candidates_small\";\r\n            successMessages.push(`${successCount} ${t(candidateKey)} ${t(\"processed_successfully\")}`);\r\n          }\r\n\r\n          if (errorCount > 0) {\r\n            const errorKey = errorCount === 1 ? \"candidate_failed_to_process\" : \"candidates_failed_to_process\";\r\n            errorMessages.push(`${errorCount} ${t(errorKey)}`);\r\n          }\r\n\r\n          // Display toast messages - show success and error toasts separately when both exist\r\n          if (successMessages.length > 0) {\r\n            toastMessageSuccess(successMessages.join(\", \"));\r\n          }\r\n          if (errorMessages.length > 0) {\r\n            toastMessageError(errorMessages.join(\", \"));\r\n          }\r\n\r\n          // No redirect - user stays on the same page\r\n        } else {\r\n          toastMessageError(t(response.data?.message) || t(\"something_went_wrong\"));\r\n        }\r\n      } else {\r\n        // No candidates were sent to backend - could be upload errors or all already successful\r\n        const totalErrors = Object.keys(uploadErrors).length;\r\n        const totalSuccessful = Object.values(candidateSuccess).filter(Boolean).length;\r\n        if (totalErrors > 0) {\r\n          const uploadErrorKey = totalErrors === 1 ? \"candidate_failed_to_upload_files\" : \"candidates_failed_to_upload_files\";\r\n          const messagePrefix = totalErrors === 1 ? \"\" : `${t(\"all\")} `;\r\n          toastMessageError(`${messagePrefix} ${totalErrors} ${t(uploadErrorKey)}`);\r\n        } else if (totalSuccessful === data.candidates.length) {\r\n          const processedKey = totalSuccessful === 1 ? \"candidate_already_processed\" : \"candidates_already_processed\";\r\n          const messagePrefix = totalSuccessful === 1 ? \"\" : `${t(\"all\")} `;\r\n          toastMessageSuccess(`${messagePrefix} ${totalSuccessful} ${t(processedKey)}`);\r\n        } else {\r\n          toastMessageError(t(\"no_candidates_processed\"));\r\n        }\r\n        // Set upload errors if any (already done above)\r\n        // if (totalErrors > 0) {\r\n        //   setCandidateErrors(uploadErrors); // Already set\r\n        // }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error uploading candidates:\", error);\r\n      toastMessageError(t(\"something_went_wrong\"));\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n  /**\r\n   * Validates if a file meets PDF requirements and processes filename\r\n   * @param file - The file to validate\r\n   * @returns boolean - True if validation passes, False otherwise\r\n   */\r\n  const handleFileChange = (file: File | null): boolean => {\r\n    if (!file) {\r\n      return false;\r\n    }\r\n    if (file.size > PDF_FILE_SIZE_LIMIT) {\r\n      toastMessageError(t(\"pdf_size_five_mb\"));\r\n      return false;\r\n    }\r\n    // Validate file name contains PDF_FILE_NAME\r\n    if (!file.name.includes(\".pdf\")) {\r\n      toastMessageError(t(\"unsupported_file_type\"));\r\n      return false;\r\n    }\r\n    // Validate file type (PDF only)\r\n    if (!PDF_FILE_TYPE.includes(file.type)) {\r\n      toastMessageError(t(\"pdf_only\"));\r\n      return false;\r\n    }\r\n    // Extract filename without extension for validation\r\n    const fileNameWithoutExt = file.name.replace(/\\.pdf$/i, \"\");\r\n    // Validate filename length (excluding .pdf extension)\r\n    if (file.name.length > 50) {\r\n      toastMessageError(t(\"pdf_name\"));\r\n      return false;\r\n    }\r\n\r\n    // Validate filename for special characters\r\n    // Allow only letters, numbers, spaces, hyphens, and underscores\r\n    const validFilenameRegex = /^[a-zA-Z0-9\\s\\-_]+$/;\r\n    if (!validFilenameRegex.test(fileNameWithoutExt)) {\r\n      toastMessageError(t(\"pdf_name_special_chars\"));\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  return (\r\n    <div ref={scrollContainerRef} className={`${style.resume_page} ${style.manual_upload_resume}`}>\r\n      <div className=\"container\">\r\n        <div className={style.inner_page}>\r\n          <div className=\"common-page-header\">\r\n            <div className=\"common-page-head-section\">\r\n              <div className=\"main-heading\">\r\n                <h2>\r\n                  <BackArrowIcon onClick={() => router.push(`${ROUTES.JOBS.ACTIVE_JOBS}`)} />\r\n                  {t(\"manual_upload_resume\")} <span>{searchParamsPromise?.title}</span>\r\n                </h2>\r\n                <Button\r\n                  className=\"clear-btn p-0 color-primary\"\r\n                  onClick={() =>\r\n                    router.push(\r\n                      `${ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${paramsPromise.jobId}` +\r\n                        `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`\r\n                    )\r\n                  }\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <HoldIcon className=\"me-2 p-1\" PrimaryColor /> {t(\"view_panding_action\")}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <form\r\n            onSubmit={handleSubmit((data) => {\r\n              setTimeout(() => {\r\n                window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n              }, 50);\r\n              onSubmit(data);\r\n            })}\r\n          >\r\n            {fields.map((field, index) => (\r\n              <div key={field.id} className={style.candidate_card}>\r\n                <div className={`d-flex align-items-center justify-content-between ${style.candidate_card_header}`}>\r\n                  <h3 className=\"d-flex align-items-center gap-2\">\r\n                    {t(\"candidate\")} 0{index + 1}\r\n                    {candidateSuccess[field.id] && (\r\n                      <p className=\"mb-0 font14 \">\r\n                        (<strong className=\"text-success\">{t(\"candidate_upload_succ\")}</strong>)\r\n                      </p>\r\n                    )}\r\n                    {candidateErrors[field.id] && (\r\n                      <p className=\"mb-0 font14\">\r\n                        (<strong className=\"text-danger\">{candidateErrors[field.id]}</strong>)\r\n                      </p>\r\n                    )}\r\n                  </h3>\r\n                  <div className=\"d-flex align-items-center gap-2\">\r\n                    {index > 0 && (\r\n                      <Button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          remove(index);\r\n                          // Clear status for this candidate when removed\r\n                          setCandidateErrors((prev) => {\r\n                            const newState = { ...prev };\r\n                            delete newState[field.id]; // Use field.id\r\n                            return newState;\r\n                          });\r\n                          setCandidateSuccess((prev) => {\r\n                            const newState = { ...prev };\r\n                            delete newState[field.id]; // Use field.id\r\n                            return newState;\r\n                          });\r\n                        }}\r\n                        className=\"clear-btn p-0\"\r\n                        disabled={isSubmitting}\r\n                      >\r\n                        <DeleteIcon className=\"p-1\" />\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                <div className={style.candidate_card_body}>\r\n                  <div className=\"row\">\r\n                    {/* Name Field */}\r\n                    <div className=\"col-md-6\">\r\n                      <InputWrapper>\r\n                        <InputWrapper.Label htmlFor={`candidates.${index}.name`} required>\r\n                          {t(\"candidate_name\")}\r\n                        </InputWrapper.Label>\r\n                        <InfoIcon tooltip=\"Enter the candidate’s full name.\" id={`name-info-${index}`} place=\"right\" />\r\n                        <Textbox\r\n                          control={control}\r\n                          name={`candidates.${index}.name`}\r\n                          placeholder={t(\"enter_candidate_name\")}\r\n                          className=\"form-control\"\r\n                        />\r\n                        {errors.candidates?.[index]?.name && <div className=\"auth-msg error\">{errors.candidates[index]?.name?.message}</div>}\r\n                      </InputWrapper>\r\n                    </div>\r\n\r\n                    {/* Email Field */}\r\n                    <div className=\"col-md-6\">\r\n                      <InputWrapper>\r\n                        <InputWrapper.Label htmlFor={`candidates.${index}.email`} required>\r\n                          {t(\"email\")}\r\n                        </InputWrapper.Label>\r\n                        <InfoIcon\r\n                          tooltip=\"Provide a valid email address to contact the candidate regarding job-related updates.\"\r\n                          id={`email-info-${index}`}\r\n                          place=\"right\"\r\n                        />\r\n                        <Textbox\r\n                          control={control}\r\n                          name={`candidates.${index}.email`}\r\n                          placeholder={t(\"please_enter_candidate_email\")}\r\n                          className=\"form-control\"\r\n                        />\r\n                        {errors.candidates?.[index]?.email && <div className=\"auth-msg error\">{errors.candidates[index]?.email?.message}</div>}\r\n                      </InputWrapper>\r\n                    </div>\r\n\r\n                    {/* Gender Field */}\r\n                    <div className=\"col-md-6\">\r\n                      <InputWrapper>\r\n                        <InputWrapper.Label htmlFor={`candidates.${index}.gender`} required>\r\n                          {t(\"gender\")}\r\n                        </InputWrapper.Label>\r\n                        <InfoIcon\r\n                          tooltip=\"Select the candidate’s gender to support inclusive hiring analytics and reporting.\"\r\n                          id={`gender-info-${index}`}\r\n                          place=\"right\"\r\n                        />\r\n                        <Select\r\n                          control={control}\r\n                          name={`candidates.${index}.gender`}\r\n                          options={GENDER_OPTIONS}\r\n                          className=\"w-100\"\r\n                          placeholder={t(\"select_gender\")}\r\n                        />\r\n                        {errors.candidates?.[index]?.gender && <div className=\"auth-msg error\">{errors.candidates[index]?.gender?.message}</div>}\r\n                      </InputWrapper>\r\n                    </div>\r\n\r\n                    {/* Resume Upload Field */}\r\n                    <div className=\"col-md-6\">\r\n                      <InputWrapper>\r\n                        <InputWrapper.Label htmlFor={`candidates.${index}.resume`} required>\r\n                          {t(\"upload_resume\")}\r\n                        </InputWrapper.Label>\r\n                        <InfoIcon\r\n                          tooltip=\"Upload the candidate’s latest resume in PDF format for evaluation\"\r\n                          id={`resume-info-${index}`}\r\n                          place=\"right\"\r\n                        />\r\n                        <div className={style.input_type_file}>\r\n                          <Controller\r\n                            name={`candidates.${index}.resume`}\r\n                            control={control}\r\n                            render={({ field }) => (\r\n                              <input\r\n                                id={`candidates.${index}.resume`}\r\n                                type=\"file\"\r\n                                ref={resumeFileRef}\r\n                                accept=\".pdf\"\r\n                                onChange={(e) => {\r\n                                  const file = e.target.files?.[0] || null;\r\n                                  if (file) {\r\n                                    if (handleFileChange(file)) {\r\n                                      field.onChange(file);\r\n                                    } else {\r\n                                      e.target.value = \"\";\r\n                                      field.onChange(null);\r\n                                    }\r\n                                  } else {\r\n                                    field.onChange(null);\r\n                                  }\r\n                                }}\r\n                              />\r\n                            )}\r\n                          />\r\n                        </div>\r\n                        {errors.candidates?.[index]?.resume && <div className=\"auth-msg error\">{errors.candidates[index]?.resume?.message}</div>}\r\n                      </InputWrapper>\r\n                    </div>\r\n\r\n                    {/* Assessment Upload Field */}\r\n                    <div className=\"col-md-6\">\r\n                      <InputWrapper>\r\n                        <InputWrapper.Label htmlFor={`candidates.${index}.assessment`}>\r\n                          {t(\"upload_assesment\")}{\" \"}\r\n                          <InfoIcon\r\n                            tooltip=\"Attach any completed assessments or test results relevant to the job role.\"\r\n                            id={`assessment-info-${index}`}\r\n                            place=\"right\"\r\n                          />\r\n                        </InputWrapper.Label>\r\n                        <div className={style.input_type_file}>\r\n                          <Controller\r\n                            name={`candidates.${index}.assessment`}\r\n                            control={control}\r\n                            render={({ field }) => (\r\n                              <input\r\n                                id={`candidates.${index}.assessment`}\r\n                                type=\"file\"\r\n                                accept=\".pdf\"\r\n                                ref={assessmentFileRef}\r\n                                onChange={(e) => {\r\n                                  const file = e.target.files?.[0] || null;\r\n                                  if (file) {\r\n                                    if (handleFileChange(file)) {\r\n                                      field.onChange(file);\r\n                                    } else {\r\n                                      e.target.value = \"\";\r\n                                      field.onChange(null);\r\n                                    }\r\n                                  } else {\r\n                                    field.onChange(null);\r\n                                  }\r\n                                }}\r\n                              />\r\n                            )}\r\n                          />\r\n                        </div>\r\n                        {errors.candidates?.[index]?.assessment && (\r\n                          <div className=\"auth-msg error\">{errors.candidates[index]?.assessment?.message}</div>\r\n                        )}\r\n                      </InputWrapper>\r\n                    </div>\r\n\r\n                    {/* Additional Information Field */}\r\n                    <div className=\"col-md-12\">\r\n                      <InputWrapper>\r\n                        <InputWrapper.Label htmlFor={`candidates.${index}.additionalInfo`}>\r\n                          {t(\"additional_info\")}{\" \"}\r\n                          <InfoIcon\r\n                            tooltip=\"Add any extra details about the candidate like experience, availability, or preferences.\"\r\n                            id={`additional-info-${index}`}\r\n                            place=\"right\"\r\n                          />\r\n                        </InputWrapper.Label>\r\n                        <Textarea\r\n                          control={control}\r\n                          name={`candidates.${index}.additionalInfo`}\r\n                          rows={6}\r\n                          placeholder={t(\"enter_additional_info_about_candidate\")}\r\n                          className=\"form-control\"\r\n                        />\r\n                        {errors.candidates?.[index]?.additionalInfo && (\r\n                          <div className=\"auth-msg error\">{errors.candidates[index]?.additionalInfo?.message}</div>\r\n                        )}\r\n                      </InputWrapper>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n\r\n            {showAddButton && (\r\n              <div className={style.add_another_candidate_link}>\r\n                <Button\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    /**\r\n                     * Handles adding another candidate to the form\r\n                     *\r\n                     * Checks if maximum limit (5) is reached before adding a new candidate entry.\r\n                     * Shows a message if the limit is reached.\r\n                     */\r\n                    if (fields.length < 5) {\r\n                      append({ name: \"\", email: \"\", gender: \"\", resume: null, assessment: null, additionalInfo: \"\" });\r\n                    } else {\r\n                      toastMessageSuccess(t(\"max_5_candidates\"));\r\n                    }\r\n                  }}\r\n                  className=\"clear-btn p-0 color-primary\"\r\n                  disabled={fields.length >= 5}\r\n                >\r\n                  {t(\"add_candidates_resume\")} {fields.length >= 5 ? t(\"max_5_candidates\") : \"\"}\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"button-align py-5\">\r\n              <Button type=\"submit\" className=\"primary-btn rounded-md minWidth\" disabled={isSubmitting}>\r\n                {t(\"analyze\")} {isSubmitting && <Loader />}\r\n              </Button>\r\n              <Button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  /**\r\n                   * Resets the form to its default state\r\n                   *\r\n                   * Clears all form fields, errors, and success states, returning the form to its initial state\r\n                   * with a single empty candidate entry.\r\n                   */\r\n                  reset();\r\n                  setCandidateErrors({});\r\n                  setCandidateSuccess({});\r\n                }}\r\n                className=\"dark-outline-btn rounded-md minWidth\"\r\n                disabled={isSubmitting}\r\n              >\r\n                {t(\"reset\")}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ManualUploadResume;\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;;;;;CAOC,GAED,SAAS,mBAAmB,EAC1B,MAAM,EACN,YAAY,EAIb;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAChC,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IACtC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,QAAqB,MAAM,IAAI,CAAC,WAAW;;IAC5E,QAAQ,GAAG,CAAC,eAAe;IAC3B,uDAAuD;IACvD,MAAM,kBAAkB,aAAa,wBAAwB,sIAAA,CAAA,aAAU,CAAC,IAAI;IAE5E,+CAA+C;IAC/C,MAAM,uBAAuB;QAC3B,IAAI,gBAAgB,QAAQ,CAAC,sIAAA,CAAA,aAAU,CAAC,GAAG,GAAG,OAAO,sIAAA,CAAA,cAAW,CAAC,GAAG;QACpE,IAAI,gBAAgB,QAAQ,CAAC,sIAAA,CAAA,aAAU,CAAC,MAAM,GAAG,OAAO,sIAAA,CAAA,cAAW,CAAC,MAAM;QAC1E,IAAI,gBAAgB,QAAQ,CAAC,sIAAA,CAAA,aAAU,CAAC,UAAU,GAAG,OAAO,sIAAA,CAAA,cAAW,CAAC,UAAU;QAClF,OAAO,sIAAA,CAAA,cAAW,CAAC,IAAI,EAAE,uBAAuB;IAClD;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG;IAC1B,yCAAyC;IACzC,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD,EAAE;QAC3C,eAAe;YACb,YAAY;gBAAC;oBAAE,MAAM;oBAAI,OAAO;oBAAI,QAAQ;oBAAI,QAAQ;oBAAM,YAAY;oBAAM,gBAAgB;gBAAG;aAAE;QACvG;IACF;IAEA,gDAAgD;IAChD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,kDAAkD;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACnF,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,OAAO,cAAc,KAAK,KAAK,CAAC,qBAAqB,SAAS,qBAAqB,MAAM,WAAW,GAAG;gBAC1G,OAAO,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;YACrC;QACF;uCAAG;QAAC,cAAc,KAAK;QAAE,qBAAqB;KAAM;IAEpD;;;;;;;;GAQC,GACD,MAAM,0BAA0B,CAC9B,YACA,UACA;QAEA,MAAM,WAAW,IAAI,OAAyB,+BAA+B;QAC7E,IAAI,gBAAgB;QAEpB,oCAAoC;QACpC,WAAW,OAAO,CAAC,CAAC,WAAW;YAC7B,MAAM,QAAQ,UAAU,KAAK,EAAE,OAAO,eAAe,kBAAkB;YACvE,IAAI,OAAO;gBACT,8BAA8B;gBAC9B,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ;oBACxB,SAAS,GAAG,CAAC,OAAO,EAAE;gBACxB;gBACA,SAAS,GAAG,CAAC,OAAQ,IAAI,CAAC,QAAQ,uCAAuC;YAC3E;QACF;QAEA,wCAAwC;QACxC,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,gBAAgB;gBAChB,uDAAuD;gBACvD,QAAQ,OAAO,CAAC,CAAC;oBACf,6DAA6D;oBAC7D,SAAS,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC,EAAW;wBAC7C,MAAM;wBACN,SAAS,EAAE;oBACb;gBACF;YACF;QACF;QAEA,OAAO;IACT;IACA;;;;;;;;GAQC,GACD,MAAM,WAAW,OAAO;QACtB,4CAA4C;QAC5C,IAAI,cAAc;YAChB;QACF;QACA,MAAM,gBAAgB,wBAAwB,KAAK,UAAU,EAAE,UAAU;QACzE,IAAI,eAAe;YACjB,+CAA+C;YAC/C,WAAW;gBACT,OAAO,QAAQ,CAAC;oBAAE,KAAK;oBAAG,UAAU;gBAAS;YAC7C,0EAA0E;YAC1E,kFAAkF;YAClF,oGAAoG;YACtG,GAAG,MAAM,gCAAgC;YACzC,QAAQ,sCAAsC;QAChD;QACA,IAAI;YACF,gBAAgB;YAChB,iEAAiE;YACjE,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;YACf,mBAAmB,CAAC,IAAI,wBAAwB;YAChD,uEAAuE;YAEvE,oFAAoF;YACpF,iEAAiE;YACjE,MAAM,wBAAwB,EAAE;YAChC,wCAAwC;YACxC,MAAM,eAAuC,CAAC;YAE9C,4DAA4D;YAC5D,IAAK,IAAI,QAAQ,GAAG,QAAQ,KAAK,UAAU,CAAC,MAAM,EAAE,QAAS;gBAC3D,MAAM,YAAY,KAAK,UAAU,CAAC,MAAM;gBACxC,2DAA2D;gBAC3D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,IAAI,+CAA+C;gBAElF,iDAAiD;gBACjD,IAAI,gBAAgB,CAAC,QAAQ,KAAK,MAAM;oBACtC;gBACF;gBAEA,IAAI,iBAAiB;gBACrB,IAAI,eAAe;gBAEnB,IAAI;oBACF,wDAAwD;oBACxD,MAAM,YAAY,KAAK,GAAG,KAAK,OAAO,iCAAiC;oBAEvE,iCAAiC;oBACjC,IAAI,YAAoB;oBACxB,MAAM,uBAAuB,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACzD,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,IAAI,UAAU,MAAM,EAAE;wBACpB,MAAM,iBAAiB,GAAG,cAAc,KAAK,CAAC,SAAS,EAAE,qBAAqB,QAAQ,EAAE,UAAU,IAAI,CAAC;wBACvG,MAAM,eAAe,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,MAAM,EAAE;wBAC5D,IAAI,CAAC,cAAc;4BACjB,iBAAiB;4BACjB,eAAe,EAAE;wBACnB,OAAO;4BACL,YAAY;wBACd;oBACF,OAAO;wBACL,iBAAiB;wBACjB,eAAe,EAAE;oBACnB;oBAEA,uEAAuE;oBACvE,IAAI,gBAAwB;oBAC5B,IAAI,CAAC,kBAAkB,UAAU,UAAU,EAAE;wBAC3C,MAAM,qBAAqB,GAAG,cAAc,KAAK,CAAC,aAAa,EAAE,qBAAqB,YAAY,EAAE,UAAU,IAAI,CAAC;wBACnH,MAAM,eAAe,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,UAAU,EAAE;wBAChE,QAAQ,GAAG,CAAC,2BAA2B;wBACvC,IAAI,CAAC,cAAc;4BACjB,iBAAiB;4BACjB,eAAe,EAAE;wBACnB,OAAO;4BACL,gBAAgB;wBAClB;oBACF;oBAEA,iEAAiE;oBACjE,IAAI,CAAC,gBAAgB;wBACnB,sBAAsB,IAAI,CAAC;4BACzB,MAAM,UAAU,IAAI;4BACpB,OAAO,UAAU,KAAK;4BACtB,QAAQ,UAAU,MAAM;4BACxB,aAAa;4BACb,iBAAiB;4BACjB,oBAAoB,UAAU,cAAc;wBAC9C;oBACF,OAAO;wBACL,0DAA0D;wBAC1D,IAAI,SAAS;4BACX,uCAAuC;4BACvC,YAAY,CAAC,QAAQ,GAAG;wBAC1B;oBACF;gBACF,EAAE,OAAO,iBAAiB;oBACxB,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;oBACnE,sDAAsD;oBACtD,IAAI,SAAS;wBACX,uCAAuC;wBACvC,YAAY,CAAC,QAAQ,GAAG,AAAC,iBAA2B,WAAW,EAAE;oBACnE;gBACF;YACF;YAEA,0CAA0C;YAC1C,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,GAAG;gBACxC,mBAAmB,eAAe,yBAAyB;YAC7D;YAEA,MAAM,QAAQ,OAAO,cAAc,KAAK;YAExC,qFAAqF;YACrF,IAAI,sBAAsB,MAAM,GAAG,GAAG;gBACpC,yEAAyE;gBACzE,MAAM,WAAW,MAAM,CAAA,GAAA,0IAAA,CAAA,wBAAqB,AAAD,EAAE;oBAAE,YAAY;oBAAuB,QAAQ;gBAAM;gBAEhG,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;oBAC9E,+CAA+C;oBAC/C,qEAAqE;oBACrE,MAAM,SAAiC;wBAAE,GAAG,YAAY;oBAAC,GAAG,oDAAoD;oBAChH,MAAM,UAAmC;wBAAE,GAAG,gBAAgB;oBAAC,GAAG,wDAAwD;oBAC1H,IAAI,eAAe,OAAO,MAAM,CAAC,kBAAkB,MAAM,CAAC,SAAS,MAAM,EAAE,2BAA2B;oBACtG,IAAI,aAAa,OAAO,IAAI,CAAC,cAAc,MAAM,EAAE,sBAAsB;oBAEzE,sEAAsE;oBACtE,IAAI,mBAAmB;oBACvB,IAAK,IAAI,gBAAgB,GAAG,gBAAgB,KAAK,UAAU,CAAC,MAAM,EAAE,gBAAiB;wBACnF,oEAAoE;wBACpE,MAAM,kBAAkB,MAAM,CAAC,cAAc,EAAE;wBAE/C,gEAAgE;wBAChE,IAAI,YAAY,CAAC,gBAAgB,EAAE;4BACjC;wBACF;wBAEA,wGAAwG;wBACxG,IAAI,gBAAgB,CAAC,gBAAgB,KAAK,MAAM;4BAC9C;wBACF;wBAEA,4CAA4C;wBAC5C,IAAI,mBAAmB,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;4BACnD,MAAM,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,iBAAiB;4BACtD,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gCACnC,uDAAuD;gCACvD,IAAI,iBAAiB;oCACnB,wBAAwB;oCACxB,MAAM,CAAC,gBAAgB,GAAG,OAAO,KAAK;oCACtC,OAAO,CAAC,gBAAgB,GAAG,OAAO,oCAAoC;oCACtE;gCACF;4BACF,OAAO,IAAI,OAAO,OAAO,EAAE;gCACzB,yDAAyD;gCACzD,IAAI,iBAAiB;oCACnB,wBAAwB;oCACxB,OAAO,CAAC,gBAAgB,GAAG;oCAC3B;gCACF;4BACF;4BACA;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,mBAAmB,SAAS,yBAAyB;oBACrD,oBAAoB,UAAU,yBAAyB;oBAEvD,MAAM,eAAe,OAAO,MAAM,CAAC,kBAAkB,MAAM,CAAC,SAAS,MAAM,EAAE,sEAAsE;oBAEnJ,mCAAmC;oBACnC,MAAM,kBAA4B,EAAE;oBACpC,MAAM,gBAA0B,EAAE;oBAElC,iCAAiC;oBACjC,IAAI,eAAe,GAAG;wBACpB,MAAM,UAAU,iBAAiB,IAAI,8BAA8B;wBACnE,gBAAgB,IAAI,CAAC,GAAG,aAAa,CAAC,EAAE,EAAE,UAAU;oBACtD;oBAEA,uDAAuD;oBACvD,IAAI,eAAe,GAAG;wBACpB,MAAM,eAAe,iBAAiB,IAAI,oBAAoB;wBAC9D,gBAAgB,IAAI,CAAC,GAAG,aAAa,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,2BAA2B;oBAC1F;oBAEA,IAAI,aAAa,GAAG;wBAClB,MAAM,WAAW,eAAe,IAAI,gCAAgC;wBACpE,cAAc,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE,WAAW;oBACnD;oBAEA,oFAAoF;oBACpF,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,IAAI,CAAC;oBAC3C;oBACA,IAAI,cAAc,MAAM,GAAG,GAAG;wBAC5B,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,IAAI,CAAC;oBACvC;gBAEA,4CAA4C;gBAC9C,OAAO;oBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,SAAS,IAAI,EAAE,YAAY,EAAE;gBACnD;YACF,OAAO;gBACL,wFAAwF;gBACxF,MAAM,cAAc,OAAO,IAAI,CAAC,cAAc,MAAM;gBACpD,MAAM,kBAAkB,OAAO,MAAM,CAAC,kBAAkB,MAAM,CAAC,SAAS,MAAM;gBAC9E,IAAI,cAAc,GAAG;oBACnB,MAAM,iBAAiB,gBAAgB,IAAI,qCAAqC;oBAChF,MAAM,gBAAgB,gBAAgB,IAAI,KAAK,GAAG,EAAE,OAAO,CAAC,CAAC;oBAC7D,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,cAAc,CAAC,EAAE,YAAY,CAAC,EAAE,EAAE,iBAAiB;gBAC1E,OAAO,IAAI,oBAAoB,KAAK,UAAU,CAAC,MAAM,EAAE;oBACrD,MAAM,eAAe,oBAAoB,IAAI,gCAAgC;oBAC7E,MAAM,gBAAgB,oBAAoB,IAAI,KAAK,GAAG,EAAE,OAAO,CAAC,CAAC;oBACjE,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,GAAG,cAAc,CAAC,EAAE,gBAAgB,CAAC,EAAE,EAAE,eAAe;gBAC9E,OAAO;oBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;gBACtB;YACA,gDAAgD;YAChD,yBAAyB;YACzB,qDAAqD;YACrD,IAAI;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,gBAAgB;QAClB;IACF;IACA;;;;GAIC,GACD,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,IAAI,KAAK,IAAI,GAAG,sIAAA,CAAA,sBAAmB,EAAE;YACnC,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QACA,4CAA4C;QAC5C,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC/B,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QACA,gCAAgC;QAChC,IAAI,CAAC,sIAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACtC,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QACA,oDAAoD;QACpD,MAAM,qBAAqB,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW;QACxD,sDAAsD;QACtD,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI;YACzB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QAEA,2CAA2C;QAC3C,gEAAgE;QAChE,MAAM,qBAAqB;QAC3B,IAAI,CAAC,mBAAmB,IAAI,CAAC,qBAAqB;YAChD,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAoB,WAAW,GAAG,4JAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,4JAAA,CAAA,UAAK,CAAC,oBAAoB,EAAE;kBAC3F,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,UAAU;;kCAC9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,uJAAA,CAAA,UAAa;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC,GAAG,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,EAAE;;;;;;4CACrE,EAAE;4CAAwB;0DAAC,6LAAC;0DAAM,qBAAqB;;;;;;;;;;;;kDAE1D,6LAAC,+IAAA,CAAA,UAAM;wCACL,WAAU;wCACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE,GACtE,CAAC,OAAO,EAAE,qBAAqB,MAAM,aAAa,EAAE,qBAAqB,aAAa;wCAG5F,UAAU;;0DAEV,6LAAC,kJAAA,CAAA,UAAQ;gDAAC,WAAU;gDAAW,YAAY;;;;;;4CAAG;4CAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAM1D,6LAAC;wBACC,UAAU,aAAa,CAAC;4BACtB,WAAW;gCACT,OAAO,QAAQ,CAAC;oCAAE,KAAK;oCAAG,UAAU;gCAAS;4BAC/C,GAAG;4BACH,SAAS;wBACX;;4BAEC,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;oCAAmB,WAAW,4JAAA,CAAA,UAAK,CAAC,cAAc;;sDACjD,6LAAC;4CAAI,WAAW,CAAC,kDAAkD,EAAE,4JAAA,CAAA,UAAK,CAAC,qBAAqB,EAAE;;8DAChG,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDAAa;wDAAG,QAAQ;wDAC1B,gBAAgB,CAAC,MAAM,EAAE,CAAC,kBACzB,6LAAC;4DAAE,WAAU;;gEAAe;8EACzB,6LAAC;oEAAO,WAAU;8EAAgB,EAAE;;;;;;gEAAkC;;;;;;;wDAG1E,eAAe,CAAC,MAAM,EAAE,CAAC,kBACxB,6LAAC;4DAAE,WAAU;;gEAAc;8EACxB,6LAAC;oEAAO,WAAU;8EAAe,eAAe,CAAC,MAAM,EAAE,CAAC;;;;;;gEAAU;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,mBACP,6LAAC,+IAAA,CAAA,UAAM;wDACL,MAAK;wDACL,SAAS;4DACP,OAAO;4DACP,+CAA+C;4DAC/C,mBAAmB,CAAC;gEAClB,MAAM,WAAW;oEAAE,GAAG,IAAI;gEAAC;gEAC3B,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,eAAe;gEAC1C,OAAO;4DACT;4DACA,oBAAoB,CAAC;gEACnB,MAAM,WAAW;oEAAE,GAAG,IAAI;gEAAC;gEAC3B,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,eAAe;gEAC1C,OAAO;4DACT;wDACF;wDACA,WAAU;wDACV,UAAU;kEAEV,cAAA,6LAAC,oJAAA,CAAA,UAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,mBAAmB;sDACvC,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;;8EACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC;oEAAE,QAAQ;8EAC9D,EAAE;;;;;;8EAEL,6LAAC,kJAAA,CAAA,UAAQ;oEAAC,SAAQ;oEAAmC,IAAI,CAAC,UAAU,EAAE,OAAO;oEAAE,OAAM;;;;;;8EACrF,6LAAC,gJAAA,CAAA,UAAO;oEACN,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC;oEAChC,aAAa,EAAE;oEACf,WAAU;;;;;;gEAEX,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,sBAAQ,6LAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;kEAK1G,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;;8EACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC;oEAAE,QAAQ;8EAC/D,EAAE;;;;;;8EAEL,6LAAC,kJAAA,CAAA,UAAQ;oEACP,SAAQ;oEACR,IAAI,CAAC,WAAW,EAAE,OAAO;oEACzB,OAAM;;;;;;8EAER,6LAAC,gJAAA,CAAA,UAAO;oEACN,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC;oEACjC,aAAa,EAAE;oEACf,WAAU;;;;;;gEAEX,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,uBAAS,6LAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;kEAK5G,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;;8EACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;oEAAE,QAAQ;8EAChE,EAAE;;;;;;8EAEL,6LAAC,kJAAA,CAAA,UAAQ;oEACP,SAAQ;oEACR,IAAI,CAAC,YAAY,EAAE,OAAO;oEAC1B,OAAM;;;;;;8EAER,6LAAC,+IAAA,CAAA,UAAM;oEACL,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;oEAClC,SAAS,2IAAA,CAAA,iBAAc;oEACvB,WAAU;oEACV,aAAa,EAAE;;;;;;gEAEhB,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,wBAAU,6LAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;kEAK9G,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;;8EACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;oEAAE,QAAQ;8EAChE,EAAE;;;;;;8EAEL,6LAAC,kJAAA,CAAA,UAAQ;oEACP,SAAQ;oEACR,IAAI,CAAC,YAAY,EAAE,OAAO;oEAC1B,OAAM;;;;;;8EAER,6LAAC;oEAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,eAAe;8EACnC,cAAA,6LAAC,iKAAA,CAAA,aAAU;wEACT,MAAM,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;wEAClC,SAAS;wEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;gFACC,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;gFAChC,MAAK;gFACL,KAAK;gFACL,QAAO;gFACP,UAAU,CAAC;oFACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;oFACpC,IAAI,MAAM;wFACR,IAAI,iBAAiB,OAAO;4FAC1B,MAAM,QAAQ,CAAC;wFACjB,OAAO;4FACL,EAAE,MAAM,CAAC,KAAK,GAAG;4FACjB,MAAM,QAAQ,CAAC;wFACjB;oFACF,OAAO;wFACL,MAAM,QAAQ,CAAC;oFACjB;gFACF;;;;;;;;;;;;;;;;gEAKP,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,wBAAU,6LAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;kEAK9G,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;;8EACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,WAAW,CAAC;;wEAC1D,EAAE;wEAAqB;sFACxB,6LAAC,kJAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAI,CAAC,gBAAgB,EAAE,OAAO;4EAC9B,OAAM;;;;;;;;;;;;8EAGV,6LAAC;oEAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,eAAe;8EACnC,cAAA,6LAAC,iKAAA,CAAA,aAAU;wEACT,MAAM,CAAC,WAAW,EAAE,MAAM,WAAW,CAAC;wEACtC,SAAS;wEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;gFACC,IAAI,CAAC,WAAW,EAAE,MAAM,WAAW,CAAC;gFACpC,MAAK;gFACL,QAAO;gFACP,KAAK;gFACL,UAAU,CAAC;oFACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;oFACpC,IAAI,MAAM;wFACR,IAAI,iBAAiB,OAAO;4FAC1B,MAAM,QAAQ,CAAC;wFACjB,OAAO;4FACL,EAAE,MAAM,CAAC,KAAK,GAAG;4FACjB,MAAM,QAAQ,CAAC;wFACjB;oFACF,OAAO;wFACL,MAAM,QAAQ,CAAC;oFACjB;gFACF;;;;;;;;;;;;;;;;gEAKP,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,4BAC3B,6LAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,YAAY;;;;;;;;;;;;;;;;;kEAM7E,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qJAAA,CAAA,UAAY;;8EACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,eAAe,CAAC;;wEAC9D,EAAE;wEAAoB;sFACvB,6LAAC,kJAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAI,CAAC,gBAAgB,EAAE,OAAO;4EAC9B,OAAM;;;;;;;;;;;;8EAGV,6LAAC,iJAAA,CAAA,UAAQ;oEACP,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,eAAe,CAAC;oEAC1C,MAAM;oEACN,aAAa,EAAE;oEACf,WAAU;;;;;;gEAEX,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,gCAC3B,6LAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhN7E,MAAM,EAAE;;;;;4BAyNnB,+BACC,6LAAC;gCAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,0BAA0B;0CAC9C,cAAA,6LAAC,+IAAA,CAAA,UAAM;oCACL,MAAK;oCACL,SAAS;wCACP;;;;;qBAKC,GACD,IAAI,OAAO,MAAM,GAAG,GAAG;4CACrB,OAAO;gDAAE,MAAM;gDAAI,OAAO;gDAAI,QAAQ;gDAAI,QAAQ;gDAAM,YAAY;gDAAM,gBAAgB;4CAAG;wCAC/F,OAAO;4CACL,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE;wCACxB;oCACF;oCACA,WAAU;oCACV,UAAU,OAAO,MAAM,IAAI;;wCAE1B,EAAE;wCAAyB;wCAAE,OAAO,MAAM,IAAI,IAAI,EAAE,sBAAsB;;;;;;;;;;;;0CAKjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+IAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAkC,UAAU;;4CACzE,EAAE;4CAAW;4CAAE,8BAAgB,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;kDAEzC,6LAAC,+IAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAS;4CACP;;;;;mBAKC,GACD;4CACA,mBAAmB,CAAC;4CACpB,oBAAoB,CAAC;wCACvB;wCACA,WAAU;wCACV,UAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAvrBS;;QAOQ,qIAAA,CAAA,YAAS;QAKd,yMAAA,CAAA,kBAAe;QAGL,4JAAA,CAAA,cAAW;QAqB3B,iKAAA,CAAA,UAAO;QAQwB,iKAAA,CAAA,gBAAa;;;KA5CzC;uCAyrBM", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1981, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/manual-upload-resume/%5BjobId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport ManualUploadResume from \"@/components/views/resume/ManualUploadResume\";\nimport React from \"react\";\n\nconst page = ({ params, searchParams }: { params: Promise<{ jobId: string }>; searchParams: Promise<{ title: string; jobUniqueId: string }> }) => {\n  return (\n    <div>\n      <ManualUploadResume params={params} searchParams={searchParams} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAyG;IAC3I,qBACE,6LAAC;kBACC,cAAA,6LAAC,8JAAA,CAAA,UAAkB;YAAC,QAAQ;YAAQ,cAAc;;;;;;;;;;;AAGxD;uCAEe", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}