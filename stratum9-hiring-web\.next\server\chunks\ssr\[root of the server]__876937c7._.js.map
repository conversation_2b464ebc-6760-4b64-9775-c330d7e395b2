{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobSkillsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { ISkillData, JobSkillsState } from \"@/interfaces/jobRequirementesInterfaces\";\n\n// Define the initial state using that type\nconst initialState: JobSkillsState = {\n  careerSkills: [],\n  roleSpecificSkills: [],\n  cultureSpecificSkills: [],\n};\n\nexport const jobSkillsSlice = createSlice({\n  name: \"jobSkills\",\n  initialState,\n  reducers: {\n    setSkillsData: (\n      state,\n      action: PayloadAction<{\n        careerSkills?: ISkillData[];\n        roleSpecificSkills?: ISkillData[];\n        cultureSpecificSkills?: ISkillData[];\n      }>\n    ) => {\n      if (action.payload.careerSkills) {\n        state.careerSkills = action.payload.careerSkills;\n      }\n      if (action.payload.roleSpecificSkills) {\n        state.roleSpecificSkills = action.payload.roleSpecificSkills;\n      }\n      if (action.payload.cultureSpecificSkills) {\n        state.cultureSpecificSkills = action.payload.cultureSpecificSkills;\n      }\n    },\n    clearSkillsData: (state) => {\n      state.careerSkills = [];\n      state.roleSpecificSkills = [];\n      state.cultureSpecificSkills = [];\n    },\n  },\n});\n\nexport const { setSkillsData, clearSkillsData } = jobSkillsSlice.actions;\n\n// Simple selectors to use directly with useSelector\nexport const selectJobSkillsState = (state: { jobSkills: JobSkillsState }) => state.jobSkills;\nexport const selectCareerSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.careerSkills;\nexport const selectRoleSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.roleSpecificSkills;\nexport const selectCultureSpecificSkills = (state: { jobSkills: JobSkillsState }) => state.jobSkills.cultureSpecificSkills;\n\n// Export the reducer directly for easier import in the store\nexport default jobSkillsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,2CAA2C;AAC3C,MAAM,eAA+B;IACnC,cAAc,EAAE;IAChB,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;AAC3B;AAEO,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,eAAe,CACb,OACA;YAMA,IAAI,OAAO,OAAO,CAAC,YAAY,EAAE;gBAC/B,MAAM,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY;YAClD;YACA,IAAI,OAAO,OAAO,CAAC,kBAAkB,EAAE;gBACrC,MAAM,kBAAkB,GAAG,OAAO,OAAO,CAAC,kBAAkB;YAC9D;YACA,IAAI,OAAO,OAAO,CAAC,qBAAqB,EAAE;gBACxC,MAAM,qBAAqB,GAAG,OAAO,OAAO,CAAC,qBAAqB;YACpE;QACF;QACA,iBAAiB,CAAC;YAChB,MAAM,YAAY,GAAG,EAAE;YACvB,MAAM,kBAAkB,GAAG,EAAE;YAC7B,MAAM,qBAAqB,GAAG,EAAE;QAClC;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,eAAe,OAAO;AAGjE,MAAM,uBAAuB,CAAC,QAAyC,MAAM,SAAS;AACtF,MAAM,qBAAqB,CAAC,QAAyC,MAAM,SAAS,CAAC,YAAY;AACjG,MAAM,2BAA2B,CAAC,QAAyC,MAAM,SAAS,CAAC,kBAAkB;AAC7G,MAAM,8BAA8B,CAAC,QAAyC,MAAM,SAAS,CAAC,qBAAqB;uCAG3G,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/commonConstants.ts"], "sourcesContent": ["import { ExtendedFormValues } from \"@/types/types\";\n\nexport const ACCESS_TOKEN_KEY = \"__ATK__\";\n\nexport const EMAIL_REGEX = /^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$/;\n\nexport const PASSWORD_REGEX = /^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\\s).{8,16}$/;\nexport const NAME_REGEX = /^[a-zA-Z0-9\\s.'-]+$/;\n\nexport const MAX_IMAGE_SIZE = 5242880;\n\nexport const ScheduleInterviewFormSubmissionType = {\n  SCHEDULE: \"schedule\",\n  UPDATE: \"update\",\n};\n\nexport const S3_PATHS = {\n  PROFILE_IMAGE: \"profile-images/:path\",\n};\n\nexport const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [\n  \"Arrive at the interview location on time with a government-issued ID.\",\n  \"Ensure your phone is on silent mode and distractions are minimized.\",\n  \"Bring a printed copy of your resume and any supporting documents.\",\n  \"Dress professionally and maintain proper body language.\",\n  \"Listen carefully, answer honestly, and ask for clarification if needed.\",\n  \"Respect the interview flow and do not interrupt the interviewer.\",\n  \"Take brief notes if necessary, but focus on active conversation.\",\n  \"If you need assistance or face any issues, notify the interview coordinator.\",\n];\n\nexport const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [\n  \"Join the interview on time using the link provided.\",\n  \"Ensure a stable internet connection and a quiet, well-lit space.\",\n  \"Test your camera, microphone, and audio settings in advance.\",\n  \"Keep your video on unless instructed otherwise by the interviewer.\",\n  \"Minimize background noise and avoid multitasking during the session.\",\n  \"Use headphones if possible for better audio clarity.\",\n  \"Be attentive, respond clearly, and maintain professional posture.\",\n  \"Contact support if you face technical difficulties before or during the interview.\",\n];\n\n/**\n * Permission Constants\n */\nexport const PERMISSION = {\n  CREATE_OR_EDIT_JOB_POST: \"create-or-edit-job-post\",\n  SCHEDULE_CONDUCT_INTERVIEWS: \"schedule-conduct-interviews\",\n  VIEW_HIRED_CANDIDATES: \"view-hired-candidates\",\n  ARCHIVE_RESTORE_CANDIDATES: \"archive-restore-candidates\",\n  ARCHIVE_RESTORE_JOB_POSTS: \"archive-restore-job-posts\",\n  MANUAL_RESUME_SCREENING: \"manual-resume-screening\",\n  EDIT_SCHEDULED_INTERVIEWS: \"edit-scheduled-interviews\",\n  ADD_ADDITIONAL_CANDIDATE_INFO: \"add-additional-candidate-info\",\n  ADD_OR_EDIT_INTERVIEW_NOTES: \"add-or-edit-interview-notes\",\n  MANAGE_TOP_CANDIDATES: \"manage-top-candidates\",\n  MANAGE_PRE_INTERVIEW_QUESTIONS: \"manage-pre-interview-questions\",\n  CREATE_FINAL_ASSESSMENT: \"create-final-assessment\",\n  VIEW_FINAL_ASSESSMENT: \"view-final-assessment\",\n  VIEW_CANDIDATE_PROFILE_SUMMARY: \"view-candidate-profile-summary\",\n  HIRE_CANDIDATE: \"hire-candidate\",\n  CREATE_NEW_ROLE: \"create-new-role\",\n  MANAGE_USER_PERMISSIONS: \"manage-user-permissions\",\n  CREATE_NEW_DEPARTMENT: \"create-new-department\",\n  ADD_INTERVIEW_PARTICIPANTS: \"add-interview-participants\",\n  VIEW_SUBSCRIPTION_PLAN: \"view-subscription-plan\",\n  MANAGE_SUBSCRIPTIONS: \"manage-subscriptions\",\n  VIEW_AUDIT_LOGS_UPCOMING: \"view-audit-logs-upcoming\",\n  VIEW_ALL_SCHEDULED_INTERVIEWS: \"view-all-scheduled-interviews\",\n};\n\n/**\n * Plan Configuration Constants\n */\nexport const PLAN_CONFIG = {\n  free: {\n    maxCandidates: 1,\n    showAddButton: false,\n    planDisplayName: \"Free\",\n  },\n  pro: {\n    maxCandidates: 1,\n    showAddButton: false,\n    planDisplayName: \"Pro\",\n  },\n  growth: {\n    maxCandidates: 5,\n    showAddButton: true,\n    planDisplayName: \"Growth\",\n  },\n  enterprise: {\n    maxCandidates: 5,\n    showAddButton: true,\n    planDisplayName: \"Enterprise\",\n  },\n} as const;\n\n/**\n * Plan Configuration Type\n */\nexport type PlanConfigType = (typeof PLAN_CONFIG)[keyof typeof PLAN_CONFIG];\n\n/**\n * Plan Name Constants\n */\nexport const PLAN_NAMES = {\n  FREE: \"Free\",\n  PRO: \"Pro\",\n  GROWTH: \"Growth\",\n  ENTERPRISE: \"Enterprise\",\n} as const;\n\n/**\n * Skill Constants\n */\nexport const SKILL_CONSTANTS = {\n  REQUIRED_ROLE_SKILLS: 10,\n  REQUIRED_CULTURE_SKILLS: 5,\n};\nexport const commonConstants = {\n  finalAssessmentId: \"finalAssessmentId\",\n  token: \"token\",\n  isShared: \"isShared\",\n  isSubmitted: \"isSubmitted\",\n  jobId: \"jobId\",\n  jobApplicationId: \"jobApplicationId\",\n};\n\nexport const QuestionType = {\n  MCQ: \"mcq\",\n  TRUE_FALSE: \"true_false\",\n};\n\n// Constants for option IDs\nexport const OPTION_ID = {\n  A: \"A\",\n  B: \"B\",\n  C: \"C\",\n  D: \"D\",\n  TRUE: \"true\",\n  FALSE: \"false\",\n} as const;\n\n// Constants for question types\nexport const QUESTION_TYPE = {\n  MCQ: \"mcq\" as const,\n  TRUE_FALSE: \"true_false\" as const,\n};\n\n// Constants for default options\nexport const DEFAULT_MCQ_OPTIONS = [\n  { id: OPTION_ID.A, text: \"\" },\n  { id: OPTION_ID.B, text: \"\" },\n  { id: OPTION_ID.C, text: \"\" },\n  { id: OPTION_ID.D, text: \"\" },\n];\n\nexport const DEFAULT_TRUE_FALSE_OPTIONS = [\n  { id: OPTION_ID.TRUE, text: \"True\" },\n  { id: OPTION_ID.FALSE, text: \"False\" },\n];\n\nexport const INTERVIEW_SCHEDULE_ROUND_TYPE = [\n  {\n    label: \"One-On-One\",\n    value: \"One-On-One\",\n  },\n  {\n    label: \"Video Call\",\n    value: \"Video Call\",\n  },\n];\n\n/**\n * Interview Question Types\n */\nexport const QUESTION_TYPES = {\n  ROLE_SPECIFIC: \"role_specific\",\n  CULTURE_SPECIFIC: \"culture_specific\",\n  CAREER_BASED: \"career_based\",\n} as const;\n\nexport type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];\n/**\n * Empty Content Patterns\n */\nexport const EMPTY_CONTENT_PATTERNS = [\"<p><br></p>\", \"<p></p>\", \"<div><br></div>\", \"<div></div>\", \"<p>&nbsp;</p>\"];\n\n// Define the initial state using FormValues type\nexport const initialState: ExtendedFormValues = {\n  title: \"\",\n  employment_type: \"\",\n  department_id: \"\",\n  salary_range: \"\",\n  salary_cycle: \"\",\n  location_type: \"\",\n  state: \"\",\n  city: \"\",\n  role_overview: \"\",\n  experience_level: \"\",\n  responsibilities: \"\",\n  educations_requirement: \"\",\n  certifications: undefined,\n  skills_and_software_expertise: \"\",\n  experience_required: \"\",\n  ideal_candidate_traits: \"\",\n  about_company: \"\",\n  perks_benefits: undefined,\n  tone_style: \"\",\n  additional_info: undefined,\n  compliance_statement: [],\n  show_compliance: false,\n  hiring_type: \"\",\n};\n\n// Define the skill item interface\nexport interface ISkillItem {\n  id: number;\n  title: string;\n  description: string;\n  short_description: string;\n}\n\n// Define a skill category interface\nexport interface ISkillCategory {\n  type: string;\n  items: ISkillItem[];\n}\n\n// Define the slice state type\nexport interface AllSkillsState {\n  categories: ISkillCategory[];\n  loading: boolean;\n  error: string | null;\n}\n\nexport const FILE_EXTENSION = [\n  \"pdf\",\n  \"plain\",\n  \"csv\",\n  \"vnd.ms-excel.sheet.macroEnabled.12\",\n  \"vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n  \"vnd.openxmlformats-officedocument.wordprocessingml.document\",\n  \"vnd.openxmlformats-officedocument.presentationml.presentation\",\n];\n\nexport const ACTIVE = \"active\";\nexport const TOKEN_EXPIRED = \"Session Expired! Please log in again.\";\nexport const DEFAULT_LIMIT = 15;\nexport const STANDARD_LIMIT = 18;\nexport const DEFAULT_OFFSET = 0;\n\nexport enum MessageType {\n  success = \"success\",\n  error = \"error\",\n}\n\nexport const IMAGE_EXTENSIONS = [\"png\", \"jpg\", \"jpeg\", \"gif\", \"webp\"];\n\nexport const ASSESSMENT_INSTRUCTIONS = {\n  instructions: [\n    \"Do not refresh or close the browser\",\n    \"Check your internet connection\",\n    \"Ensure a distraction-free environment\",\n    \"Click 'Submit' only once when finished\",\n    \"Read each question carefully\",\n    \"Manage your time efficiently\",\n    \"Avoid any form of plagiarism\",\n    \"Reach out to support if needed\",\n  ],\n};\nexport const PERMISSIONS_COOKIES_KEY = \"permissions_data\";\n\nexport const PDF_FILE_NAME = \"pdf\";\nexport const PDF_FILE_TYPE = \"application/pdf\";\nexport const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;\nexport const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAmB;AAEzB,MAAM,cAAc;AAEpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AAEnB,MAAM,iBAAiB;AAEvB,MAAM,sCAAsC;IACjD,UAAU;IACV,QAAQ;AACV;AAEO,MAAM,WAAW;IACtB,eAAe;AACjB;AAEO,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,oCAAoC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAKM,MAAM,aAAa;IACxB,yBAAyB;IACzB,6BAA6B;IAC7B,uBAAuB;IACvB,4BAA4B;IAC5B,2BAA2B;IAC3B,yBAAyB;IACzB,2BAA2B;IAC3B,+BAA+B;IAC/B,6BAA6B;IAC7B,uBAAuB;IACvB,gCAAgC;IAChC,yBAAyB;IACzB,uBAAuB;IACvB,gCAAgC;IAChC,gBAAgB;IAChB,iBAAiB;IACjB,yBAAyB;IACzB,uBAAuB;IACvB,4BAA4B;IAC5B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,+BAA+B;AACjC;AAKO,MAAM,cAAc;IACzB,MAAM;QACJ,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;IACA,KAAK;QACH,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;IACA,QAAQ;QACN,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;IACA,YAAY;QACV,eAAe;QACf,eAAe;QACf,iBAAiB;IACnB;AACF;AAUO,MAAM,aAAa;IACxB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,YAAY;AACd;AAKO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,yBAAyB;AAC3B;AACO,MAAM,kBAAkB;IAC7B,mBAAmB;IACnB,OAAO;IACP,UAAU;IACV,aAAa;IACb,OAAO;IACP,kBAAkB;AACpB;AAEO,MAAM,eAAe;IAC1B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,YAAY;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM;IACN,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,KAAK;IACL,YAAY;AACd;AAGO,MAAM,sBAAsB;IACjC;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;IAC5B;QAAE,IAAI,UAAU,CAAC;QAAE,MAAM;IAAG;CAC7B;AAEM,MAAM,6BAA6B;IACxC;QAAE,IAAI,UAAU,IAAI;QAAE,MAAM;IAAO;IACnC;QAAE,IAAI,UAAU,KAAK;QAAE,MAAM;IAAQ;CACtC;AAEM,MAAM,gCAAgC;IAC3C;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAKM,MAAM,iBAAiB;IAC5B,eAAe;IACf,kBAAkB;IAClB,cAAc;AAChB;AAMO,MAAM,yBAAyB;IAAC;IAAe;IAAW;IAAmB;IAAe;CAAgB;AAG5G,MAAM,eAAmC;IAC9C,OAAO;IACP,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,cAAc;IACd,eAAe;IACf,OAAO;IACP,MAAM;IACN,eAAe;IACf,kBAAkB;IAClB,kBAAkB;IAClB,wBAAwB;IACxB,gBAAgB;IAChB,+BAA+B;IAC/B,qBAAqB;IACrB,wBAAwB;IACxB,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,sBAAsB,EAAE;IACxB,iBAAiB;IACjB,aAAa;AACf;AAuBO,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AAEvB,IAAA,AAAK,qCAAA;;;WAAA;;AAKL,MAAM,mBAAmB;IAAC;IAAO;IAAO;IAAQ;IAAO;CAAO;AAE9D,MAAM,0BAA0B;IACrC,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AACO,MAAM,0BAA0B;AAEhC,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,sBAAsB,IAAI,OAAO;AACvC,MAAM,kCAAkC", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobDetailsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { ExtendedFormValues } from \"@/types/types\";\nimport { initialState } from \"@/constants/commonConstants\";\n\nexport const jobDetailsSlice = createSlice({\n  name: \"jobDetails\",\n  initialState,\n  reducers: {\n    // Set all job details at once\n    setJobDetails: (state, action: PayloadAction<ExtendedFormValues>) => {\n      return { ...state, ...action.payload };\n    },\n    // Clear job details\n    clearJobDetails: () => {\n      return initialState;\n    },\n    // Update a specific field in job details\n    updateJobDetail: <T extends keyof ExtendedFormValues>(\n      state: ExtendedFormValues,\n      action: PayloadAction<{ field: T; value: ExtendedFormValues[T] }>\n    ) => {\n      const { field, value } = action.payload;\n      state[field] = value;\n    },\n  },\n});\n\nexport const { setJobDetails, clearJobDetails, updateJobDetail } = jobDetailsSlice.actions;\n\n// Simple selectors to use directly with useSelector\nexport const selectJobDetails = (state: { jobDetails: ExtendedFormValues }) => state.jobDetails;\n\n// Export the reducer directly for easier import in the store\nexport default jobDetailsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAEA;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACzC,MAAM;IACN,cAAA,mIAAA,CAAA,eAAY;IACZ,UAAU;QACR,8BAA8B;QAC9B,eAAe,CAAC,OAAO;YACrB,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,OAAO,OAAO;YAAC;QACvC;QACA,oBAAoB;QACpB,iBAAiB;YACf,OAAO,mIAAA,CAAA,eAAY;QACrB;QACA,yCAAyC;QACzC,iBAAiB,CACf,OACA;YAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;YACvC,KAAK,CAAC,MAAM,GAAG;QACjB;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,gBAAgB,OAAO;AAGnF,MAAM,mBAAmB,CAAC,QAA8C,MAAM,UAAU;uCAGhF,gBAAgB,OAAO", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/allSkillsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { RootState } from \"../store\";\nimport { AllSkillsState, ISkillCategory, ISkillItem } from \"@/interfaces/jobRequirementesInterfaces\";\n\n// Define the initial state\nconst initialState: AllSkillsState = {\n  categories: [],\n  loading: false,\n  error: null,\n};\n\n// Create the slice\nexport const allSkillsSlice = createSlice({\n  name: \"allSkills\",\n  initialState,\n  reducers: {\n    fetchSkillsStart: (state: AllSkillsState) => {\n      state.loading = true;\n      state.error = null;\n    },\n    fetchSkillsSuccess: (state: AllSkillsState, action: PayloadAction<ISkillCategory[]>) => {\n      state.categories = action.payload;\n      state.loading = false;\n      state.error = null;\n    },\n    fetchSkillsFailure: (state: AllSkillsState, action: PayloadAction<string>) => {\n      state.loading = false;\n      state.error = action.payload;\n    },\n    updateSkillItem: (\n      state: AllSkillsState,\n      action: PayloadAction<{\n        categoryType: string;\n        skillId: number;\n        updatedSkill: Partial<ISkillItem>;\n      }>\n    ) => {\n      const { categoryType, skillId, updatedSkill } = action.payload;\n      const categoryIndex = state.categories.findIndex((cat) => cat.type === categoryType);\n\n      if (categoryIndex !== -1) {\n        const skillIndex = state.categories[categoryIndex].items.findIndex((item) => item.id === skillId);\n\n        if (skillIndex !== -1) {\n          state.categories[categoryIndex].items[skillIndex] = {\n            ...state.categories[categoryIndex].items[skillIndex],\n            ...updatedSkill,\n          };\n        }\n      }\n    },\n  },\n});\n\n// Export actions\nexport const { fetchSkillsStart, fetchSkillsSuccess, fetchSkillsFailure, updateSkillItem } = allSkillsSlice.actions;\n\n// Define selectors\nexport const selectAllSkills = (state: RootState) => state.allSkills.categories;\nexport const selectSkillsLoading = (state: RootState) => state.allSkills.loading;\nexport const selectSkillsError = (state: RootState) => state.allSkills.error;\n\n// Export specific category selector\nexport const selectSkillsByCategory = (categoryType: string) => (state: RootState) =>\n  state.allSkills.categories.find((cat: ISkillCategory) => cat.type === categoryType)?.items || [];\n\n// Export reducer\nexport default allSkillsSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAIA,2BAA2B;AAC3B,MAAM,eAA+B;IACnC,YAAY,EAAE;IACd,SAAS;IACT,OAAO;AACT;AAGO,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,kBAAkB,CAAC;YACjB,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAuB;YAC1C,MAAM,UAAU,GAAG,OAAO,OAAO;YACjC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC,OAAuB;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QACA,iBAAiB,CACf,OACA;YAMA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,OAAO,OAAO;YAC9D,MAAM,gBAAgB,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;YAEvE,IAAI,kBAAkB,CAAC,GAAG;gBACxB,MAAM,aAAa,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAEzF,IAAI,eAAe,CAAC,GAAG;oBACrB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,GAAG;wBAClD,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW;wBACpD,GAAG,YAAY;oBACjB;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,eAAe,OAAO;AAG5G,MAAM,kBAAkB,CAAC,QAAqB,MAAM,SAAS,CAAC,UAAU;AACxE,MAAM,sBAAsB,CAAC,QAAqB,MAAM,SAAS,CAAC,OAAO;AACzE,MAAM,oBAAoB,CAAC,QAAqB,MAAM,SAAS,CAAC,KAAK;AAGrE,MAAM,yBAAyB,CAAC,eAAyB,CAAC,QAC/D,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAwB,IAAI,IAAI,KAAK,eAAe,SAAS,EAAE;uCAGnF,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/authSlice.ts"], "sourcesContent": ["import { Permission, Role, IUserData, IDepartment } from \"@/interfaces/authInterfaces\";\nimport { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\n\n// interface IPlanFeature {\n//   slug: string;\n//   text: string;\n//   value: number | string | boolean;\n//   feature: string;\n//   is_active: boolean;\n//   description: string;\n// }\n\nexport interface ICurrentPlan {\n  orgSubscriptionId?: number;\n  startDate?: string;\n  expiryDate?: string;\n  nextBillingDate?: string;\n  status?: string;\n  subscriptionPlanId?: number;\n  subscriptionPlanName?: string;\n  subscriptionPlanDescription?: string;\n  pricingId?: number;\n  price?: number;\n  subscriptionPlanPaymentType?: string;\n}\n\nexport interface AuthState {\n  authData: IUserData | null;\n  department: IDepartment | null;\n  role: Role | null;\n  permissions: Permission[];\n  currentPlan: ICurrentPlan | null;\n}\n\nconst initialState: AuthState = {\n  authData: {\n    id: -1,\n    account_type: \"\",\n    email: \"\",\n    isVerified: false,\n    sms_notification: false,\n    allow_notification: false,\n    is_deleted: false,\n    image: \"\",\n    orgId: -1,\n    departmentId: -1,\n    organizationName: \"\",\n    organizationCode: \"\",\n    createdTs: \"\",\n    first_name: \"\",\n    last_name: \"\",\n  },\n  department: null,\n  role: null,\n  permissions: [],\n  currentPlan: null,\n};\n\nexport const authSlice = createSlice({\n  name: \"auth\",\n  initialState,\n  reducers: {\n    setAuthData: (state, action) => {\n      state.authData = action.payload;\n    },\n    setRole: (state, action) => {\n      state.role = action.payload;\n    },\n    setDepartment: (state, action) => {\n      state.department = action.payload;\n    },\n    setPermissions: (state, action) => {\n      state.permissions = action.payload;\n    },\n    setCurrentPlan: (state, action) => {\n      state.currentPlan = action.payload;\n    },\n    updateUserProfileData: (\n      state,\n      action: PayloadAction<{\n        first_name?: string;\n        last_name?: string;\n        image?: string | null;\n      }>\n    ) => {\n      if (state.authData) {\n        const { first_name, last_name, image } = action.payload;\n\n        // Update firstName and lastName separately\n        if (first_name !== undefined) {\n          state.authData.first_name = first_name;\n        }\n\n        if (last_name !== undefined) {\n          state.authData.last_name = last_name;\n        }\n\n        // Update image if provided\n        if (image !== undefined) {\n          state.authData.image = image;\n        }\n      }\n    },\n  },\n});\n\nexport const selectRole = (state: { auth: AuthState }) => state.auth.role;\nexport const selectDepartment = (state: { auth: AuthState }) => state.auth.department;\nexport const selectPermissions = (state: { auth: AuthState }) => state.auth.permissions;\nexport const selectProfileData = (state: { auth: AuthState }) => state.auth.authData;\nexport const selectCurrentPlan = (state: { auth: AuthState }) => state.auth.currentPlan;\n\nexport const { setAuthData, setRole, setDepartment, setPermissions, updateUserProfileData, setCurrentPlan } = authSlice.actions;\n\nexport default authSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;;AAiCA,MAAM,eAA0B;IAC9B,UAAU;QACR,IAAI,CAAC;QACL,cAAc;QACd,OAAO;QACP,YAAY;QACZ,kBAAkB;QAClB,oBAAoB;QACpB,YAAY;QACZ,OAAO;QACP,OAAO,CAAC;QACR,cAAc,CAAC;QACf,kBAAkB;QAClB,kBAAkB;QAClB,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,YAAY;IACZ,MAAM;IACN,aAAa,EAAE;IACf,aAAa;AACf;AAEO,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM;IACN;IACA,UAAU;QACR,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;QAC7B;QACA,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG,OAAO,OAAO;QACnC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,uBAAuB,CACrB,OACA;YAMA,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;gBAEvD,2CAA2C;gBAC3C,IAAI,eAAe,WAAW;oBAC5B,MAAM,QAAQ,CAAC,UAAU,GAAG;gBAC9B;gBAEA,IAAI,cAAc,WAAW;oBAC3B,MAAM,QAAQ,CAAC,SAAS,GAAG;gBAC7B;gBAEA,2BAA2B;gBAC3B,IAAI,UAAU,WAAW;oBACvB,MAAM,QAAQ,CAAC,KAAK,GAAG;gBACzB;YACF;QACF;IACF;AACF;AAEO,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI;AAClE,MAAM,mBAAmB,CAAC,QAA+B,MAAM,IAAI,CAAC,UAAU;AAC9E,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW;AAChF,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;AAC7E,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW;AAEhF,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB,EAAE,cAAc,EAAE,GAAG,UAAU,OAAO;uCAEhH,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/jobRequirementSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { JobRequirementState } from \"@/interfaces/jobRequirementesInterfaces\";\n\nconst initialState: JobRequirementState = {\n  content: \"\",\n  isGenerated: false,\n  generatedAt: null,\n};\n\nexport const jobRequirementSlice = createSlice({\n  name: \"jobRequirement\",\n  initialState,\n  reducers: {\n    // Set job requirement content\n    setJobRequirement: (state, action: PayloadAction<string>) => {\n      state.content = action.payload;\n      state.isGenerated = true;\n      state.generatedAt = new Date().toISOString();\n    },\n    // Clear job requirement data\n    clearJobRequirement: (state) => {\n      state.content = \"\";\n      state.isGenerated = false;\n      state.generatedAt = null;\n    },\n  },\n});\n\nexport const { setJobRequirement, clearJobRequirement } = jobRequirementSlice.actions;\n\n// Selector to use with useSelector\nexport const selectJobRequirement = (state: { jobRequirement: JobRequirementState }) => state.jobRequirement;\n\nexport default jobRequirementSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGA,MAAM,eAAoC;IACxC,SAAS;IACT,aAAa;IACb,aAAa;AACf;AAEO,MAAM,sBAAsB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7C,MAAM;IACN;IACA,UAAU;QACR,8BAA8B;QAC9B,mBAAmB,CAAC,OAAO;YACzB,MAAM,OAAO,GAAG,OAAO,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,MAAM,WAAW,GAAG,IAAI,OAAO,WAAW;QAC5C;QACA,6BAA6B;QAC7B,qBAAqB,CAAC;YACpB,MAAM,OAAO,GAAG;YAChB,MAAM,WAAW,GAAG;YACpB,MAAM,WAAW,GAAG;QACtB;IACF;AACF;AAEO,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,GAAG,oBAAoB,OAAO;AAG9E,MAAM,uBAAuB,CAAC,QAAmD,MAAM,cAAc;uCAE7F,oBAAoB,OAAO", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/interviewSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { QUESTION_TYPES, QuestionType } from \"@/constants/commonConstants\";\nimport { IGetInterviewSkillQuestionsResponse, IInterviewStaticInformation } from \"@/interfaces/interviewInterfaces\";\n\nexport interface IQuestionAnswer {\n  questionId: number;\n  answer: string;\n}\n\nexport interface IUpdateQuestionAnswerPayload {\n  questionType: QuestionType;\n  questionAnswers: IQuestionAnswer[];\n  stratumScore: number;\n  category?: string;\n  interviewerName?: string;\n}\n\nconst initialState: IGetInterviewSkillQuestionsResponse & { interviewStaticInformation: IInterviewStaticInformation } = {\n  roleSpecificQuestions: {},\n  cultureSpecificQuestions: {},\n  careerBasedQuestions: {\n    questions: [],\n    score: 0,\n  },\n  interviewStaticInformation: {\n    oneToOneInterviewInstructions: [],\n    videoCallInterviewInstructions: [],\n    startumDescription: [],\n  },\n};\n\nexport const interviewSlice = createSlice({\n  name: \"interview\",\n  initialState,\n  reducers: {\n    setInterviewQuestions: (state, action: PayloadAction<IGetInterviewSkillQuestionsResponse>) => {\n      // Handle role-specific questions\n      if (action.payload.roleSpecificQuestions !== undefined) {\n        state.roleSpecificQuestions = action.payload.roleSpecificQuestions;\n      }\n\n      // Handle culture-specific questions\n      if (action.payload.cultureSpecificQuestions !== undefined) {\n        state.cultureSpecificQuestions = action.payload.cultureSpecificQuestions;\n      }\n\n      // Handle career-based questions\n      if (action.payload.careerBasedQuestions !== undefined) {\n        state.careerBasedQuestions = action.payload.careerBasedQuestions;\n      }\n    },\n\n    setInterviewStaticInformation: (state, action: PayloadAction<IInterviewStaticInformation>) => {\n      state.interviewStaticInformation = action.payload;\n    },\n\n    updateQuestionAnswer: (state, action: PayloadAction<IUpdateQuestionAnswerPayload>) => {\n      const { questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;\n\n      // Create a Map for O(1) lookups\n      const answerMap = new Map(questionAnswers.map((qa) => [qa.questionId, qa.answer]));\n\n      switch (questionType) {\n        case QUESTION_TYPES.CAREER_BASED:\n          // Update answers\n          state.careerBasedQuestions.questions = state.careerBasedQuestions.questions.map((question) => {\n            const answer = answerMap.get(question.id);\n            if (answer !== undefined) {\n              return { ...question, answer };\n            }\n            return question;\n          });\n\n          // Update score\n          state.careerBasedQuestions.score = stratumScore;\n          break;\n\n        case QUESTION_TYPES.ROLE_SPECIFIC:\n          if (category) {\n            // Initialize category if it doesn't exist\n            if (!state.roleSpecificQuestions[category]) {\n              state.roleSpecificQuestions[category] = {\n                questions: [],\n                score: 0,\n              };\n            }\n\n            // Update answers\n            state.roleSpecificQuestions[category].questions = state.roleSpecificQuestions[category].questions.map((question) => {\n              const answer = answerMap.get(question.id);\n              if (answer !== undefined) {\n                return { ...question, answer };\n              }\n              return question;\n            });\n\n            // Update score and interviewer name\n            state.roleSpecificQuestions[category].score = stratumScore;\n            state.roleSpecificQuestions[category].interviewerName = interviewerName;\n          }\n          break;\n\n        case QUESTION_TYPES.CULTURE_SPECIFIC:\n          if (category) {\n            // Initialize category if it doesn't exist\n            if (!state.cultureSpecificQuestions[category]) {\n              state.cultureSpecificQuestions[category] = {\n                questions: [],\n                score: 0,\n              };\n            }\n\n            // Update answers\n            state.cultureSpecificQuestions[category].questions = state.cultureSpecificQuestions[category].questions.map((question) => {\n              const answer = answerMap.get(question.id);\n              if (answer !== undefined) {\n                return { ...question, answer };\n              }\n              return question;\n            });\n\n            // Update score and interviewer name\n            state.cultureSpecificQuestions[category].score = stratumScore;\n            state.cultureSpecificQuestions[category].interviewerName = interviewerName;\n          }\n          break;\n      }\n    },\n\n    clearInterview: (state) => {\n      // Reset state to initial values\n      state.roleSpecificQuestions = initialState.roleSpecificQuestions;\n      state.cultureSpecificQuestions = initialState.cultureSpecificQuestions;\n      state.careerBasedQuestions = initialState.careerBasedQuestions;\n    },\n  },\n});\n\nexport const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation } = interviewSlice.actions;\n\nexport default interviewSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAgBA,MAAM,eAAkH;IACtH,uBAAuB,CAAC;IACxB,0BAA0B,CAAC;IAC3B,sBAAsB;QACpB,WAAW,EAAE;QACb,OAAO;IACT;IACA,4BAA4B;QAC1B,+BAA+B,EAAE;QACjC,gCAAgC,EAAE;QAClC,oBAAoB,EAAE;IACxB;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACxC,MAAM;IACN;IACA,UAAU;QACR,uBAAuB,CAAC,OAAO;YAC7B,iCAAiC;YACjC,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,WAAW;gBACtD,MAAM,qBAAqB,GAAG,OAAO,OAAO,CAAC,qBAAqB;YACpE;YAEA,oCAAoC;YACpC,IAAI,OAAO,OAAO,CAAC,wBAAwB,KAAK,WAAW;gBACzD,MAAM,wBAAwB,GAAG,OAAO,OAAO,CAAC,wBAAwB;YAC1E;YAEA,gCAAgC;YAChC,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,WAAW;gBACrD,MAAM,oBAAoB,GAAG,OAAO,OAAO,CAAC,oBAAoB;YAClE;QACF;QAEA,+BAA+B,CAAC,OAAO;YACrC,MAAM,0BAA0B,GAAG,OAAO,OAAO;QACnD;QAEA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,OAAO,OAAO;YAEjG,gCAAgC;YAChC,MAAM,YAAY,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAC,KAAO;oBAAC,GAAG,UAAU;oBAAE,GAAG,MAAM;iBAAC;YAEhF,OAAQ;gBACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;oBAC9B,iBAAiB;oBACjB,MAAM,oBAAoB,CAAC,SAAS,GAAG,MAAM,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBAC/E,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;wBACxC,IAAI,WAAW,WAAW;4BACxB,OAAO;gCAAE,GAAG,QAAQ;gCAAE;4BAAO;wBAC/B;wBACA,OAAO;oBACT;oBAEA,eAAe;oBACf,MAAM,oBAAoB,CAAC,KAAK,GAAG;oBACnC;gBAEF,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;oBAC/B,IAAI,UAAU;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,MAAM,qBAAqB,CAAC,SAAS,EAAE;4BAC1C,MAAM,qBAAqB,CAAC,SAAS,GAAG;gCACtC,WAAW,EAAE;gCACb,OAAO;4BACT;wBACF;wBAEA,iBAAiB;wBACjB,MAAM,qBAAqB,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,qBAAqB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BACrG,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;4BACxC,IAAI,WAAW,WAAW;gCACxB,OAAO;oCAAE,GAAG,QAAQ;oCAAE;gCAAO;4BAC/B;4BACA,OAAO;wBACT;wBAEA,oCAAoC;wBACpC,MAAM,qBAAqB,CAAC,SAAS,CAAC,KAAK,GAAG;wBAC9C,MAAM,qBAAqB,CAAC,SAAS,CAAC,eAAe,GAAG;oBAC1D;oBACA;gBAEF,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;oBAClC,IAAI,UAAU;wBACZ,0CAA0C;wBAC1C,IAAI,CAAC,MAAM,wBAAwB,CAAC,SAAS,EAAE;4BAC7C,MAAM,wBAAwB,CAAC,SAAS,GAAG;gCACzC,WAAW,EAAE;gCACb,OAAO;4BACT;wBACF;wBAEA,iBAAiB;wBACjB,MAAM,wBAAwB,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,wBAAwB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC3G,MAAM,SAAS,UAAU,GAAG,CAAC,SAAS,EAAE;4BACxC,IAAI,WAAW,WAAW;gCACxB,OAAO;oCAAE,GAAG,QAAQ;oCAAE;gCAAO;4BAC/B;4BACA,OAAO;wBACT;wBAEA,oCAAoC;wBACpC,MAAM,wBAAwB,CAAC,SAAS,CAAC,KAAK,GAAG;wBACjD,MAAM,wBAAwB,CAAC,SAAS,CAAC,eAAe,GAAG;oBAC7D;oBACA;YACJ;QACF;QAEA,gBAAgB,CAAC;YACf,gCAAgC;YAChC,MAAM,qBAAqB,GAAG,aAAa,qBAAqB;YAChE,MAAM,wBAAwB,GAAG,aAAa,wBAAwB;YACtE,MAAM,oBAAoB,GAAG,aAAa,oBAAoB;QAChE;IACF;AACF;AAEO,MAAM,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,cAAc,EAAE,6BAA6B,EAAE,GAAG,eAAe,OAAO;uCAErH,eAAe,OAAO", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/slices/notificationSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\nimport { NotificationItem } from \"@/interfaces/notificationInterface\";\n\nconst initialState: {\n  notifications: NotificationItem[];\n  hasUnreadNotifications: boolean;\n} = {\n  notifications: [],\n  hasUnreadNotifications: false,\n};\n\nexport const notificationSlice = createSlice({\n  name: \"notification\",\n  initialState,\n  reducers: {\n    setNotificationsData: (state, action: PayloadAction<NotificationItem[]>) => {\n      state.notifications = action.payload;\n    },\n    setHasUnreadNotification: (state, action: PayloadAction<boolean>) => {\n      state.hasUnreadNotifications = action.payload;\n    },\n  },\n});\n\nexport const { setNotificationsData, setHasUnreadNotification } = notificationSlice.actions;\nexport default notificationSlice.reducer;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,eAGF;IACF,eAAe,EAAE;IACjB,wBAAwB;AAC1B;AAEO,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC3C,MAAM;IACN;IACA,UAAU;QACR,sBAAsB,CAAC,OAAO;YAC5B,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QACA,0BAA0B,CAAC,OAAO;YAChC,MAAM,sBAAsB,GAAG,OAAO,OAAO;QAC/C;IACF;AACF;AAEO,MAAM,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,GAAG,kBAAkB,OAAO;uCAC5E,kBAAkB,OAAO", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/store.ts"], "sourcesContent": ["import { configureStore } from \"@reduxjs/toolkit\";\nimport { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from \"redux-persist\";\nimport storage from \"redux-persist/lib/storage\"; // defaults to localStorage for web\n\n// Import the reducers directly to avoid circular dependency\nimport jobSkillsReducer from \"./slices/jobSkillsSlice\";\nimport jobDetailsReducer from \"./slices/jobDetailsSlice\";\nimport allSkillsReducer from \"./slices/allSkillsSlice\";\nimport authReducer from \"./slices/authSlice\";\nimport jobRequirementReducer from \"./slices/jobRequirementSlice\";\nimport interviewReducer from \"./slices/interviewSlice\";\nimport notificationReducer from \"./slices/notificationSlice\";\n\n// Configure persist options for job skills slice\nconst jobSkillsPersistConfig = {\n  key: \"jobSkills\",\n  storage,\n};\n\n// Configure persist options for job details slice\nconst jobDetailsPersistConfig = {\n  key: \"jobDetails\",\n  storage,\n};\n\n// Configure persist options for all skills slice\nconst allSkillsPersistConfig = {\n  key: \"allSkills\",\n  storage,\n  blacklist: [\"loading\", \"error\"], // Don't persist loading and error states\n};\n\n// Configure persist options for auth slice\nconst authPersistConfig = {\n  key: \"auth\",\n  storage,\n};\n// Configure persist options for job requirement slice\nconst jobRequirementPersistConfig = {\n  key: \"jobRequirement\",\n  storage,\n};\n\n// Configure persist options for interview questions slice\nconst interviewPersistConfig = {\n  key: \"interview\",\n  storage,\n};\n\n// Configure persist options for notification slice\nconst notificationPersistConfig = {\n  key: \"notification\",\n  storage,\n};\n\n// Create persisted reducers\nconst persistedJobSkillsReducer = persistReducer(jobSkillsPersistConfig, jobSkillsReducer);\nconst persistedJobDetailsReducer = persistReducer(jobDetailsPersistConfig, jobDetailsReducer);\nconst persistedAllSkillsReducer = persistReducer(allSkillsPersistConfig, allSkillsReducer);\nconst persistedAuthReducer = persistReducer(authPersistConfig, authReducer);\nconst persistedJobRequirementReducer = persistReducer(jobRequirementPersistConfig, jobRequirementReducer);\nconst persistedInterviewReducer = persistReducer(interviewPersistConfig, interviewReducer);\nconst persistedNotificationReducer = persistReducer(notificationPersistConfig, notificationReducer);\n\n// Create store with the persisted reducers\nexport const store = configureStore({\n  reducer: {\n    jobSkills: persistedJobSkillsReducer,\n    jobDetails: persistedJobDetailsReducer,\n    allSkills: persistedAllSkillsReducer,\n    auth: persistedAuthReducer,\n    jobRequirement: persistedJobRequirementReducer,\n    interview: persistedInterviewReducer,\n    notification: persistedNotificationReducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\n      },\n    }),\n});\n\n// Create persistor\nexport const persistor = persistStore(store);\n\n// Infer the `RootState` and `AppDispatch` types from the store itself\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA,+QAAiD,mCAAmC;AAEpF,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,iDAAiD;AACjD,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,kDAAkD;AAClD,MAAM,0BAA0B;IAC9B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;IACP,WAAW;QAAC;QAAW;KAAQ;AACjC;AAEA,2CAA2C;AAC3C,MAAM,oBAAoB;IACxB,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AACA,sDAAsD;AACtD,MAAM,8BAA8B;IAClC,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,0DAA0D;AAC1D,MAAM,yBAAyB;IAC7B,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,mDAAmD;AACnD,MAAM,4BAA4B;IAChC,KAAK;IACL,SAAA,2JAAA,CAAA,UAAO;AACT;AAEA,4BAA4B;AAC5B,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,wIAAA,CAAA,UAAgB;AACzF,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,yBAAyB,yIAAA,CAAA,UAAiB;AAC5F,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,wIAAA,CAAA,UAAgB;AACzF,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,mIAAA,CAAA,UAAW;AAC1E,MAAM,iCAAiC,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,6BAA6B,6IAAA,CAAA,UAAqB;AACxG,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,wIAAA,CAAA,UAAgB;AACzF,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,2BAA2B,2IAAA,CAAA,UAAmB;AAG3F,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,WAAW;QACX,YAAY;QACZ,WAAW;QACX,MAAM;QACN,gBAAgB;QAChB,WAAW;QACX,cAAc;IAChB;IACA,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC,mJAAA,CAAA,QAAK;oBAAE,mJAAA,CAAA,YAAS;oBAAE,mJAAA,CAAA,QAAK;oBAAE,mJAAA,CAAA,UAAO;oBAAE,mJAAA,CAAA,QAAK;oBAAE,mJAAA,CAAA,WAAQ;iBAAC;YACrE;QACF;AACJ;AAGO,MAAM,YAAY,CAAA,GAAA,iMAAA,CAAA,eAAY,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/redux/ReduxProvider.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { Provider } from \"react-redux\";\nimport { PersistGate } from \"redux-persist/integration/react\";\nimport { store, persistor } from \"./store\";\n\ninterface ReduxProviderProps {\n  children: React.ReactNode;\n}\n\nconst ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {\n  return (\n    <Provider store={store}>\n      <PersistGate persistor={persistor}>{children}</PersistGate>\n    </Provider>\n  );\n};\n\nexport default ReduxProvider;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IAC/D,qBACE,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,qHAAA,CAAA,QAAK;kBACpB,cAAA,8OAAC,8JAAA,CAAA,cAAW;YAAC,WAAW,qHAAA,CAAA,YAAS;sBAAG;;;;;;;;;;;AAG1C;uCAEe", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/syncReduxToCookies.ts"], "sourcesContent": ["import { PERMISSIONS_COOKIES_KEY } from \"@/constants/commonConstants\";\nimport { Permission } from \"@/interfaces/authInterfaces\";\nimport { store } from \"@/redux/store\";\nimport Cookies from \"js-cookie\";\n\n// Serialize specific parts of Redux state to cookies\nexport const syncReduxStateToCookies = (permissions?: Permission[], forceSync = false) => {\n  try {\n    const permissionData = Cookies.get(PERMISSIONS_COOKIES_KEY);\n    if (!forceSync && permissionData) {\n      return;\n    }\n    const state = store.getState();\n\n    // Sync auth state to cookies (permissions are in auth state)\n    if (state.auth) {\n      Cookies.set(PERMISSIONS_COOKIES_KEY, JSON.stringify(permissions?.length ? permissions : state.auth.permissions), {\n        expires: 4, // 4 day\n        path: \"/\",\n        sameSite: \"strict\",\n      });\n    }\n  } catch (error) {\n    console.error(\"Error syncing Redux state to cookies:\", error);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAGO,MAAM,0BAA0B,CAAC,aAA4B,YAAY,KAAK;IACnF,IAAI;QACF,MAAM,iBAAiB,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,0BAAuB;QAC1D,IAAI,CAAC,aAAa,gBAAgB;YAChC;QACF;QACA,MAAM,QAAQ,qHAAA,CAAA,QAAK,CAAC,QAAQ;QAE5B,6DAA6D;QAC7D,IAAI,MAAM,IAAI,EAAE;YACd,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,0BAAuB,EAAE,KAAK,SAAS,CAAC,aAAa,SAAS,cAAc,MAAM,IAAI,CAAC,WAAW,GAAG;gBAC/G,SAAS;gBACT,MAAM;gBACN,UAAU;YACZ;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACzD;AACF", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/logo.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 257, height: 70, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/down-arrow.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 15, height: 8, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAG,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/user.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 51, height: 50, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AAICAgEzMzM5iIiHuaqckfWjl472i4qKvDU1NT4CAgICAC8vLzSXl5fQq6ai/tCpjv/HpIr/q6el/piYmNYyMjI5AH9/f6qsrKz+rqqn/82hiP/UqIz/sKuo/6ysrP6FhYWxAKWlpeaoqav/k5ah/6SIfP+wkH//kZSf/6eoq/+pqansAKSkpeWCip3/UGGI/25sfP93cn//U2KJ/4CInf+np6fsAHt8fqhlcpH+SlqC/0xbgv9HWID/SluF/2hzkf5/gIKyACUnKzNLWXrQc3KC/qWWj/+Cf4z/UmCG/lZhfdYsLS86AAEBAgEWGyc6P0ZduZ2BcPWQfHH2PUdhvRkdJz4BAQICbTiKQnEKIQsAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsd,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/header.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"header-module-scss-module__4lyeFq__active\",\n  \"admin_info\": \"header-module-scss-module__4lyeFq__admin_info\",\n  \"align_header_search\": \"header-module-scss-module__4lyeFq__align_header_search\",\n  \"circle_img\": \"header-module-scss-module__4lyeFq__circle_img\",\n  \"dropdown_menu\": \"header-module-scss-module__4lyeFq__dropdown_menu\",\n  \"header\": \"header-module-scss-module__4lyeFq__header\",\n  \"header_buttons\": \"header-module-scss-module__4lyeFq__header_buttons\",\n  \"header_right\": \"header-module-scss-module__4lyeFq__header_right\",\n  \"hidden\": \"header-module-scss-module__4lyeFq__hidden\",\n  \"logo\": \"header-module-scss-module__4lyeFq__logo\",\n  \"navbar_content\": \"header-module-scss-module__4lyeFq__navbar_content\",\n  \"navbar_links\": \"header-module-scss-module__4lyeFq__navbar_links\",\n  \"open\": \"header-module-scss-module__4lyeFq__open\",\n  \"searchBar\": \"header-module-scss-module__4lyeFq__searchBar\",\n  \"searchButton\": \"header-module-scss-module__4lyeFq__searchButton\",\n  \"searchContainer\": \"header-module-scss-module__4lyeFq__searchContainer\",\n  \"searchIcon\": \"header-module-scss-module__4lyeFq__searchIcon\",\n  \"searchInput\": \"header-module-scss-module__4lyeFq__searchInput\",\n  \"search_wrapper\": \"header-module-scss-module__4lyeFq__search_wrapper\",\n  \"show\": \"header-module-scss-module__4lyeFq__show\",\n  \"sidebar_sub_menu\": \"header-module-scss-module__4lyeFq__sidebar_sub_menu\",\n  \"sub_menu_list\": \"header-module-scss-module__4lyeFq__sub_menu_list\",\n  \"sub_menubar\": \"header-module-scss-module__4lyeFq__sub_menubar\",\n  \"user_drop\": \"header-module-scss-module__4lyeFq__user_drop\",\n  \"user_drop_btn\": \"header-module-scss-module__4lyeFq__user_drop_btn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/Notification.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface NotificationIconProps extends React.SVGProps<SVGSVGElement> {\n  hasNotification?: boolean;\n  onClick?: React.MouseEventHandler<SVGSVGElement>; // Added this line\n}\n\nfunction NotificationIcon(props: NotificationIconProps) {\n  const { hasNotification, ...restProps } = props;\n  return (\n    <svg {...restProps} className=\"cursor-pointer\" xmlns=\"http://www.w3.org/2000/svg\" width=\"25\" height=\"24\" viewBox=\"0 0 33 32\" fill=\"none\">\n      <path\n        d=\"M27.458 22.9624C26.251 20.8511 25.6133 18.4492 25.6133 16.0166C25.6133 16.0166 25.6133 14.0014 25.6133 14C25.6133 10.1198 22.9443 6.54149 19.2454 5.40924C19.4725 4.98712 19.6133 4.51202 19.6133 4C19.6133 2.3457 18.2676 1 16.6133 1C14.959 1 13.6133 2.3457 13.6133 4C13.6133 4.51233 13.7544 4.98767 13.9817 5.40997C10.2878 6.57581 7.61332 10.1457 7.61332 14.3071V16.0166C7.61332 18.4492 6.97562 20.8511 5.76811 22.9629C5.1221 24.0927 4.75006 25.2737 5.46489 26.5054C6.00736 27.4414 6.97758 28 8.05961 28H12.6133C12.6133 30.2056 14.4077 32 16.6133 32C18.8189 32 20.6133 30.2056 20.6133 28H25.167C26.249 28 27.2193 27.4414 27.7617 26.5054C28.4522 25.3141 28.0953 24.0784 27.458 22.9624ZM16.6133 3C17.1646 3 17.6133 3.44873 17.6133 4C17.6133 4.55127 17.1646 5 16.6133 5C16.062 5 15.6133 4.55127 15.6133 4C15.6133 3.44873 16.062 3 16.6133 3ZM16.6133 30C15.5103 30 14.6133 29.103 14.6133 28H18.6133C18.6133 29.103 17.7163 30 16.6133 30ZM26.0323 25.5019C25.9453 25.6514 25.687 26 25.167 26H8.05961C7.53967 26 7.28136 25.6515 7.19441 25.502C6.87823 24.9586 7.23496 24.428 7.50492 23.9546C8.88432 21.542 9.61332 18.7969 9.61332 16.0166C9.61332 16.0166 9.61332 14.3081 9.61332 14.3071C9.61332 10.5303 12.7077 7.00054 16.602 7.00049C20.3752 7.00044 23.6133 10.2392 23.6133 14V16.0166C23.6133 18.7968 24.3423 21.5419 25.7212 23.954C26.0017 24.4448 26.3567 24.9391 26.0323 25.5019Z\"\n        fill=\"#333333\"\n      />\n      {hasNotification && <circle cx=\"24.6136\" cy=\"10.6654\" r=\"4.83333\" fill=\"#D4000D\" stroke=\"white\" />}\n    </svg>\n  );\n}\n\nexport default NotificationIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,iBAAiB,KAA4B;IACpD,MAAM,EAAE,eAAe,EAAE,GAAG,WAAW,GAAG;IAC1C,qBACE,8OAAC;QAAK,GAAG,SAAS;QAAE,WAAU;QAAiB,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAChI,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;YAEN,iCAAmB,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAU,GAAE;gBAAU,MAAK;gBAAU,QAAO;;;;;;;;;;;;AAG9F;uCAEe", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/storage.ts"], "sourcesContent": ["import SecureLS from \"secure-ls\";\n\nconst ls = new SecureLS();\n\ninterface Storage {\n  set: (key: string, data: unknown) => void;\n  get: (key: string) => unknown;\n  remove: (key: string) => void;\n  removeAll: () => void;\n  getAllKeys: () => string[];\n}\n\nconst storage: Storage = {\n  set: (key, data) => {\n    if (typeof window !== \"undefined\") {\n      ls.set(key, JSON.stringify(data));\n    }\n  },\n\n  get: (key) => {\n    if (typeof window !== \"undefined\") {\n      const data = ls.get(key);\n      if (data) {\n        return data ? JSON.parse(data) : null;\n      }\n    }\n    return null;\n  },\n\n  remove: (key) => {\n    if (typeof window !== \"undefined\") {\n      ls.remove(key);\n    }\n  },\n\n  removeAll: () => {\n    ls.removeAll();\n    if (typeof window !== \"undefined\") {\n      localStorage.clear();\n    }\n  },\n\n  getAllKeys: () => {\n    if (typeof window !== \"undefined\") {\n      const keys: string[] = ls.getAllKeys();\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          keys.push(key);\n        }\n      }\n      return keys;\n    }\n    return [];\n  },\n};\n\nexport default storage;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,KAAK,IAAI,oJAAA,CAAA,UAAQ;AAUvB,MAAM,UAAmB;IACvB,KAAK,CAAC,KAAK;QACT,uCAAmC;;QAEnC;IACF;IAEA,KAAK,CAAC;QACJ,uCAAmC;;QAKnC;QACA,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAEnC;IACF;IAEA,WAAW;QACT,GAAG,SAAS;QACZ,uCAAmC;;QAEnC;IACF;IAEA,YAAY;QACV,uCAAmC;;QASnC;QACA,OAAO,EAAE;IACX;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1145, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/endpoint.ts"], "sourcesContent": ["// import config from \"@/config/config\";\n\nconst URL = process.env.NEXT_PUBLIC_BASE_URL;\n\nconst endpoint = {\n  auth: {\n    SIGNIN: `${URL}/auth/sign-in`,\n    VERIFY_OTP: `${URL}/auth/verify-otp`,\n    RESEND_OTP: `${URL}/auth/resend-otp`,\n    FORGOT_PASSWORD: `${URL}/auth/forgot-password`,\n    RESET_PASSWORD: `${URL}/auth/reset-password`,\n    DELETE_SESSION: `${URL}/auth/delete-session`,\n    UPDATE_TIMEZONE: `${URL}/auth/update-timezone`,\n  },\n  interview: {\n    UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,\n    GET_INTERVIEWS: `${URL}/interview/get-interviews`,\n    GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,\n    GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,\n    UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,\n\n    GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,\n    GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,\n    UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,\n    ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,\n    GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,\n\n    GET_JOB_LIST: `${URL}/interview/get-job-list`,\n    GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,\n    END_INTERVIEW: `${URL}/interview/end-interview`,\n    CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`,\n  },\n  common: {\n    REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,\n    GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`,\n  },\n  jobRequirements: {\n    GENERATE_SKILL: `${URL}/jobs/generate-skills`,\n    UPLOAD_URL: `${URL}/jobs/upload-url`,\n    PARSE_PDF: `${URL}/jobs/parse-pdf`,\n    GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,\n    GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,\n    SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,\n    GET_JOBS_META: `${URL}/jobs/get-jobs-meta`, // <-- Full URL here\n    UPDATE_JOB_STATUS: \"/jobs/updateJob\",\n    GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,\n    UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,\n    GENERATE_PDF: `${URL}/jobs/generate-pdf`,\n  },\n  Dashboard: {\n    GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`,\n  },\n  resumeScreen: {\n    MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,\n    GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,\n    GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,\n    CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`,\n  },\n  employee: {\n    ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,\n    GET_EMPLOYEES: `${URL}/employee-management`,\n    GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,\n    UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,\n    UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,\n    // this task is for future use\n    // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original\n    DELETE_EMPLOYEE: `${URL}/employee-management/dummy`, //dummy\n\n    UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`,\n  },\n  userprofile: {\n    GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,\n    UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`,\n  },\n\n  roles: {\n    GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,\n    GET_ROLES: `${URL}/access-management/user-roles`,\n    ADD_USER_ROLE: `${URL}/access-management/add-user-role`,\n    UPDATE_USER_ROLE: `${URL}/access-management/user-role`,\n    DELETE_USER_ROLE: `${URL}/access-management/user-role`,\n    GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,\n    GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,\n    UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,\n    USER_PERMISSIONS: `${URL}/access-management/user-permissions`,\n  },\n  notification: {\n    UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,\n    GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,\n    DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,\n    GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`,\n  },\n\n  departments: {\n    GET_DEPARTMENTS: `${URL}/employee-management/departments`,\n    ADD_DEPARTMENT: `${URL}/employee-management/add-department`,\n    UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,\n    DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`,\n  },\n\n  assessment: {\n    CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,\n    GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,\n    CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,\n    SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,\n    SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,\n    GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,\n    SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,\n    GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,\n    VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,\n    GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`,\n  },\n  candidatesApplication: {\n    ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,\n    PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`, // Base URL for candidates-related endpoints\n    GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,\n    GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,\n    ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,\n    GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`, // New endpoint for individual candidate details\n    UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`, // Endpoint for updating job application status\n    GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:applicationId`,\n    GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:jobApplicationId`,\n    GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:jobApplicationId`,\n    GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`, // Endpoint for generating final summary\n  },\n  subscription: {\n    GET_ALL_PLANS: `${URL}/subscription/all`,\n    GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,\n    CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,\n    GET_TRANSACTIONS: `${URL}/subscription/transactions`,\n    BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`,\n    JOB_POSTING_QUOTA: `${URL}/subscription/job-posting-quota`,\n  },\n};\n\nexport default endpoint;\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AAExC,MAAM;AAEN,MAAM,WAAW;IACf,MAAM;QACJ,QAAQ,GAAG,IAAI,aAAa,CAAC;QAC7B,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;QAC9C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;IAChD;IACA,WAAW;QACT,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,gBAAgB,GAAG,IAAI,yBAAyB,CAAC;QACjD,kBAAkB,GAAG,IAAI,2BAA2B,CAAC;QACrD,mBAAmB,GAAG,IAAI,4BAA4B,CAAC;QACvD,0BAA0B,GAAG,IAAI,mCAAmC,CAAC;QAErE,gCAAgC,GAAG,IAAI,0CAA0C,CAAC;QAClF,+BAA+B,GAAG,IAAI,wCAAwC,CAAC;QAC/E,iCAAiC,GAAG,IAAI,0CAA0C,CAAC;QACnF,8BAA8B,GAAG,IAAI,uCAAuC,CAAC;QAC7E,sBAAsB,GAAG,IAAI,+BAA+B,CAAC;QAE7D,cAAc,GAAG,IAAI,uBAAuB,CAAC;QAC7C,oBAAoB,GAAG,IAAI,6BAA6B,CAAC;QACzD,eAAe,GAAG,IAAI,wBAAwB,CAAC;QAC/C,sCAAsC,GAAG,IAAI,+CAA+C,CAAC;IAC/F;IACA,QAAQ;QACN,4BAA4B,GAAG,IAAI,2BAA2B,CAAC;QAC/D,wBAAwB,GAAG,IAAI,sBAAsB,CAAC;IACxD;IACA,iBAAiB;QACf,gBAAgB,GAAG,IAAI,qBAAqB,CAAC;QAC7C,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,WAAW,GAAG,IAAI,eAAe,CAAC;QAClC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,kBAAkB,GAAG,IAAI,sBAAsB,CAAC;QAChD,eAAe,GAAG,IAAI,mBAAmB,CAAC;QAC1C,mBAAmB;QACnB,0BAA0B,GAAG,IAAI,8BAA8B,CAAC;QAChE,wBAAwB,GAAG,IAAI,4BAA4B,CAAC;QAC5D,cAAc,GAAG,IAAI,kBAAkB,CAAC;IAC1C;IACA,WAAW;QACT,sBAAsB,GAAG,IAAI,sBAAsB,CAAC;IACtD;IACA,cAAc;QACZ,yBAAyB,GAAG,IAAI,sCAAsC,CAAC;QACvE,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,kCAAkC,GAAG,IAAI,+CAA+C,CAAC;QACzF,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;IAC7E;IACA,UAAU;QACR,eAAe,GAAG,IAAI,wCAAwC,CAAC;QAC/D,eAAe,GAAG,IAAI,oBAAoB,CAAC;QAC3C,6BAA6B,GAAG,IAAI,8BAA8B,CAAC;QACnE,sBAAsB,GAAG,IAAI,8CAA8C,CAAC;QAC5E,wBAAwB,GAAG,IAAI,uDAAuD,CAAC;QACvF,8BAA8B;QAC9B,kFAAkF;QAClF,iBAAiB,GAAG,IAAI,0BAA0B,CAAC;QAEnD,iCAAiC,GAAG,IAAI,yDAAyD,CAAC;IACpG;IACA,aAAa;QACX,gBAAgB,GAAG,IAAI,4BAA4B,CAAC;QACpD,mBAAmB,GAAG,IAAI,+BAA+B,CAAC;IAC5D;IAEA,OAAO;QACL,2BAA2B,GAAG,IAAI,wCAAwC,CAAC;QAC3E,WAAW,GAAG,IAAI,6BAA6B,CAAC;QAChD,eAAe,GAAG,IAAI,gCAAgC,CAAC;QACvD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,kBAAkB,GAAG,IAAI,4BAA4B,CAAC;QACtD,sBAAsB,GAAG,IAAI,mCAAmC,CAAC;QACjE,4BAA4B,GAAG,IAAI,2CAA2C,CAAC;QAC/E,yBAAyB,GAAG,IAAI,2CAA2C,CAAC;QAC5E,kBAAkB,GAAG,IAAI,mCAAmC,CAAC;IAC/D;IACA,cAAc;QACZ,qBAAqB,GAAG,IAAI,8BAA8B,CAAC;QAC3D,mBAAmB,GAAG,IAAI,gCAAgC,CAAC;QAC3D,0BAA0B,GAAG,IAAI,6CAA6C,CAAC;QAC/E,gCAAgC,GAAG,IAAI,6CAA6C,CAAC;IACvF;IAEA,aAAa;QACX,iBAAiB,GAAG,IAAI,gCAAgC,CAAC;QACzD,gBAAgB,GAAG,IAAI,mCAAmC,CAAC;QAC3D,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;QAC/E,mBAAmB,GAAG,IAAI,oDAAoD,CAAC;IACjF;IAEA,YAAY;QACV,yBAAyB,GAAG,IAAI,yCAAyC,CAAC;QAC1E,gCAAgC,GAAG,IAAI,sCAAsC,CAAC;QAC9E,4BAA4B,GAAG,IAAI,4CAA4C,CAAC;QAChF,0BAA0B,GAAG,IAAI,+CAA+C,CAAC;QACjF,kBAAkB,GAAG,IAAI,kCAAkC,CAAC;QAC5D,mCAAmC,GAAG,IAAI,sCAAsC,CAAC;QACjF,mBAAmB,GAAG,IAAI,6CAA6C,CAAC;QACxE,uBAAuB,GAAG,IAAI,mCAAmC,CAAC;QAClE,wBAAwB,GAAG,IAAI,wCAAwC,CAAC;QACxE,2BAA2B,GAAG,IAAI,2CAA2C,CAAC;IAChF;IACA,uBAAuB;QACrB,iBAAiB,GAAG,IAAI,yCAAyC,CAAC;QAClE,0BAA0B,GAAG,IAAI,wCAAwC,CAAC;QAC1E,sCAAsC,GAAG,IAAI,0BAA0B,CAAC;QACxE,kCAAkC,GAAG,IAAI,0BAA0B,CAAC;QACpE,4BAA4B,GAAG,IAAI,qDAAqD,CAAC;QACzF,uBAAuB,GAAG,IAAI,iCAAiC,CAAC;QAChE,+BAA+B,GAAG,IAAI,2DAA2D,CAAC;QAClG,iCAAiC,GAAG,IAAI,0DAA0D,CAAC;QACnG,+BAA+B,GAAG,IAAI,uDAAuD,CAAC;QAC9F,kCAAkC,GAAG,IAAI,0DAA0D,CAAC;QACpG,wBAAwB,GAAG,IAAI,kCAAkC,CAAC;IACpE;IACA,cAAc;QACZ,eAAe,GAAG,IAAI,iBAAiB,CAAC;QACxC,0BAA0B,GAAG,IAAI,qBAAqB,CAAC;QACvD,qBAAqB,GAAG,IAAI,oBAAoB,CAAC;QACjD,kBAAkB,GAAG,IAAI,0BAA0B,CAAC;QACpD,kBAAkB,GAAG,IAAI,8BAA8B,CAAC;QACxD,mBAAmB,GAAG,IAAI,+BAA+B,CAAC;IAC5D;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/config/config.ts"], "sourcesContent": ["const config = {\n  env: process.env.NODE_ENV,\n  apiBaseUrl: process.env.NEXT_PUBLIC_BASE_URL,\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;IACb,GAAG;IACH,UAAU;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/api.ts"], "sourcesContent": ["import { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { TApiState } from \"@/types/types\";\n\nconst { toString } = Object.prototype;\n\nexport const isObject = <T>(arg: T): boolean => toString.call(arg) === \"[object Object]\";\n\nexport const withError = <T extends TApiState>(arg: T): ApiResponse => {\n  if (isObject(arg)) {\n    return {\n      data: null,\n      error: {\n        ...arg,\n      },\n    };\n  }\n\n  return {\n    data: null,\n    error: {\n      message: arg,\n    },\n  };\n};\n\nexport const withData = <T extends TApiState>(data: T): ApiResponse => ({\n  error: null,\n  data,\n});\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,SAAS;AAE9B,MAAM,WAAW,CAAI,MAAoB,SAAS,IAAI,CAAC,SAAS;AAEhE,MAAM,YAAY,CAAsB;IAC7C,IAAI,SAAS,MAAM;QACjB,OAAO;YACL,MAAM;YACN,OAAO;gBACL,GAAG,GAAG;YACR;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,OAAO;YACL,SAAS;QACX;IACF;AACF;AAEO,MAAM,WAAW,CAAsB,OAAyB,CAAC;QACtE,OAAO;QACP;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1426, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/helper.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\n\nimport storage from \"./storage\";\n\nimport { ACCESS_TOKEN_KEY, PERMISSIONS_COOKIES_KEY } from \"@/constants/commonConstants\";\nimport { deleteSession } from \"@/services/authServices\";\nimport { getSignedUrl } from \"@/services/commonService\";\nimport { FilePath } from \"@/interfaces/commonInterfaces\";\n\nexport const getAccessToken = () => {\n  return storage.get(ACCESS_TOKEN_KEY);\n};\n\nexport const clearStorage = () => {\n  return storage.removeAll();\n};\n\nexport const setAccessToken = (accessToken: string) => {\n  storage.set(ACCESS_TOKEN_KEY, accessToken);\n};\n\n/**\n * Toast style object\n */\nconst style = {\n  fontSize: \"16px\",\n};\n\n/**\n * Toast success message\n * @param message - The message to display\n */\nexport const toastMessageSuccess = (message: string) => {\n  toast.success(message, {\n    style,\n  });\n};\n\n/**\n * Toast success message with icon\n * @param message - The message to display\n * @param icon - The icon to display\n */\nexport const toastMessageWithIcon = (message: string, icon: string) => {\n  toast.success(message, {\n    style,\n    icon,\n  });\n};\n\n/**\n * Toast error message\n * @param message - The message to display\n */\nexport const toastMessageError = (message: string) => {\n  toast.error(message, {\n    style,\n  });\n};\n\n/**\n * Dismiss all existing toast notifications\n */\nexport const dismissAllToasts = () => {\n  toast.dismiss();\n};\n\nexport const logout = async (userId?: number) => {\n  try {\n    deleteSession(userId);\n    await signOut({ redirect: false });\n    clearStorage();\n\n    // Delete permissions_data cookies when user logs out\n    Cookies.remove(PERMISSIONS_COOKIES_KEY, { path: \"/\" });\n  } catch (error) {\n    console.error(\"Error in logout:\", error);\n  }\n};\n\n/**\n *  get presignedUrl for image upload\n */\nexport const uploadFileOnS3 = async (file: Blob, filePath: string) => {\n  let body: FilePath = {\n    filePath: \"\",\n    fileFormat: \"\",\n  };\n  body = {\n    filePath,\n    fileFormat: file.type as string,\n  };\n  let signedUrl;\n  const presignedUrl = await getSignedUrl(body);\n  if (presignedUrl && presignedUrl.data) {\n    const response = await pushFileToS3(presignedUrl.data.data, file);\n    if (response?.url) {\n      signedUrl = response?.url.split(\"?\")?.[0];\n    }\n  }\n\n  return signedUrl?.replace(`${process.env.NEXT_PUBLIC_S3_URL}`, `${process.env.NEXT_PUBLIC_S3_CDN_URL}`);\n};\n\n/**\n *  Upload file on presignedUrl of S3\n */\nexport const pushFileToS3 = async (signedUrl: string, file: Blob): Promise<Response> => {\n  return fetch(signedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\nexport const formatDate = (dateString: string) => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  });\n};\n// Format times as HH:MM for time inputs\nexport const formatTimeForInput = (date: Date) => {\n  const hours = date.getHours().toString().padStart(2, \"0\");\n  const minutes = date.getMinutes().toString().padStart(2, \"0\");\n  return `${hours}:${minutes}`;\n};\n\nexport const toTitleCase = (name: string) => {\n  if (!name) return \"\";\n  return name\n    .toLowerCase()\n    .split(\" \")\n    .filter((word) => word) // remove extra spaces\n    .map((word) => word[0].toUpperCase() + word.slice(1))\n    .join(\" \");\n};\n\n// Normalize spaces (replace multiple spaces with a single space)\nexport const normalizeSpaces = (text: string): string => {\n  return text.trim().replace(/\\s+/g, \" \");\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;;;;;;;;AAGO,MAAM,iBAAiB;IAC5B,OAAO,uHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,mBAAgB;AACrC;AAEO,MAAM,eAAe;IAC1B,OAAO,uHAAA,CAAA,UAAO,CAAC,SAAS;AAC1B;AAEO,MAAM,iBAAiB,CAAC;IAC7B,uHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mIAAA,CAAA,mBAAgB,EAAE;AAChC;AAEA;;CAEC,GACD,MAAM,QAAQ;IACZ,UAAU;AACZ;AAMO,MAAM,sBAAsB,CAAC;IAClC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;QACrB;IACF;AACF;AAOO,MAAM,uBAAuB,CAAC,SAAiB;IACpD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;QACrB;QACA;IACF;AACF;AAMO,MAAM,oBAAoB,CAAC;IAChC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS;QACnB;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,uJAAA,CAAA,UAAK,CAAC,OAAO;AACf;AAEO,MAAM,SAAS,OAAO;IAC3B,IAAI;QACF,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE;QACd,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC;QAEA,qDAAqD;QACrD,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,mIAAA,CAAA,0BAAuB,EAAE;YAAE,MAAM;QAAI;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAKO,MAAM,iBAAiB,OAAO,MAAY;IAC/C,IAAI,OAAiB;QACnB,UAAU;QACV,YAAY;IACd;IACA,OAAO;QACL;QACA,YAAY,KAAK,IAAI;IACvB;IACA,IAAI;IACJ,MAAM,eAAe,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;IACxC,IAAI,gBAAgB,aAAa,IAAI,EAAE;QACrC,MAAM,WAAW,MAAM,aAAa,aAAa,IAAI,CAAC,IAAI,EAAE;QAC5D,IAAI,UAAU,KAAK;YACjB,YAAY,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE;QAC3C;IACF;IAEA,OAAO,WAAW,QAAQ,gGAAmC,EAAE,8EAAuC;AACxG;AAKO,MAAM,eAAe,OAAO,WAAmB;IACpD,OAAO,MAAM,WAAW;QACtB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACzD,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,WAAW,GACX,KAAK,CAAC,KACN,MAAM,CAAC,CAAC,OAAS,MAAM,sBAAsB;KAC7C,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,KAAK,CAAC,IACjD,IAAI,CAAC;AACV;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ;AACrC", "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/http.ts"], "sourcesContent": ["// src/utils/http.ts\nimport axios, { AxiosResponse } from \"axios\";\nimport { getSession } from \"next-auth/react\";\n\nimport env from \"@/config/config\";\nimport { withData, withError } from \"@/utils/api\";\nimport { ISession } from \"@/interfaces/commonInterfaces\";\nimport { logout, toastMessageError } from \"./helper\";\nimport { TOKEN_EXPIRED } from \"@/constants/commonConstants\";\n\nexport const http = axios.create({\n  baseURL: env.apiBaseUrl,\n  headers: { \"Content-Type\": \"application/json\" },\n});\n\nhttp.interceptors.request.use(async (req) => {\n  const session = (await getSession()) as unknown as ISession;\n  const accessToken = session?.user?.data?.token;\n  if (accessToken) {\n    req.headers.authorization = `Bearer ${accessToken}`;\n  }\n  req.headers[\"ngrok-skip-browser-warning\"] = \"fjdlkghjsk\";\n  return req;\n});\n\n// Flag to prevent multiple logout calls\nlet isLoggingOut = false;\n\nhttp.interceptors.response.use(\n  (res) => withData(res.data) as AxiosResponse,\n  async (err) => {\n    const session = (await getSession()) as unknown as ISession;\n    const userId = session?.user?.data?.authData?.userData?.id;\n    const accessToken = session?.user?.data?.token;\n\n    if (err?.response?.status === 401 && !isLoggingOut && accessToken) {\n      isLoggingOut = true;\n      try {\n        await logout(userId);\n        toastMessageError(TOKEN_EXPIRED);\n\n        if (typeof window !== \"undefined\") {\n          window.location.reload();\n        }\n      } catch (error) {\n        console.error(\"Session cleanup error:\", error);\n      } finally {\n        isLoggingOut = false;\n      }\n    } else if (err?.response?.status === 403) {\n      // Show toast message for forbidden access (403)\n      toastMessageError(err?.response?.data?.message);\n    }\n    return withError(err?.response?.data?.error);\n  }\n);\n\nexport function get<P, R>(url: string, params?: P): Promise<R> {\n  return http({\n    method: \"get\",\n    url,\n    params,\n  });\n}\n\nexport function post<D, P, R>(url: string, data: D, params?: P): Promise<R> {\n  return http({\n    method: \"post\",\n    url,\n    data,\n    params,\n  });\n}\n\nexport function postFile<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {\n  return http({\n    method: \"post\",\n    url,\n    data,\n    params,\n    headers: { \"Content-Type\": \"multipart/form-data\" },\n  });\n}\nexport function put<D, P, R>(url: string, data: D, params?: P): Promise<R> {\n  return http({\n    method: \"put\",\n    url,\n    data,\n    params,\n  });\n}\n\nexport function patch<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {\n  return http({\n    method: \"patch\",\n    url,\n    data,\n    params,\n  });\n}\nexport function remove<P, R>(url: string, params?: P): Promise<R> {\n  return http({\n    method: \"delete\",\n    url,\n    params,\n  });\n}\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;AACpB;AACA;AAEA;AACA;AAEA;AACA;;;;;;;AAEO,MAAM,OAAO,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,uHAAA,CAAA,UAAG,CAAC,UAAU;IACvB,SAAS;QAAE,gBAAgB;IAAmB;AAChD;AAEA,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO;IACnC,MAAM,UAAW,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAChC,MAAM,cAAc,SAAS,MAAM,MAAM;IACzC,IAAI,aAAa;QACf,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;IACrD;IACA,IAAI,OAAO,CAAC,6BAA6B,GAAG;IAC5C,OAAO;AACT;AAEA,wCAAwC;AACxC,IAAI,eAAe;AAEnB,KAAK,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC5B,CAAC,MAAQ,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,GAC1B,OAAO;IACL,MAAM,UAAW,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAChC,MAAM,SAAS,SAAS,MAAM,MAAM,UAAU,UAAU;IACxD,MAAM,cAAc,SAAS,MAAM,MAAM;IAEzC,IAAI,KAAK,UAAU,WAAW,OAAO,CAAC,gBAAgB,aAAa;QACjE,eAAe;QACf,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;YACb,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,mIAAA,CAAA,gBAAa;YAE/B,uCAAmC;;YAEnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,eAAe;QACjB;IACF,OAAO,IAAI,KAAK,UAAU,WAAW,KAAK;QACxC,gDAAgD;QAChD,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,UAAU,MAAM;IACzC;IACA,OAAO,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,MAAM;AACxC;AAGK,SAAS,IAAU,GAAW,EAAE,MAAU;IAC/C,OAAO,KAAK;QACV,QAAQ;QACR;QACA;IACF;AACF;AAEO,SAAS,KAAc,GAAW,EAAE,IAAO,EAAE,MAAU;IAC5D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AAEO,SAAS,SAAkB,GAAW,EAAE,IAAO,EAAE,MAAU;IAChE,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;QACA,SAAS;YAAE,gBAAgB;QAAsB;IACnD;AACF;AACO,SAAS,IAAa,GAAW,EAAE,IAAO,EAAE,MAAU;IAC3D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AAEO,SAAS,MAAe,GAAW,EAAE,IAAO,EAAE,MAAU;IAC7D,OAAO,KAAK;QACV,QAAQ;QACR;QACA;QACA;IACF;AACF;AACO,SAAS,OAAa,GAAW,EAAE,MAAU;IAClD,OAAO,KAAK;QACV,QAAQ;QACR;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1682, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/authServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { IForgotPassword, ILogin, IResendOTP, IResetPassword, IVerifyOTP, UserPermissionsResponse } from \"@/interfaces/authInterfaces\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse, IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { AxiosResponse } from \"axios\";\n\n// Using UserPermissionsResponse interface from authInterfaces.ts\n\nexport const logIn = (data: ILogin): Promise<ApiResponse> => {\n  return http.post(endpoint.auth.SIGNIN, data);\n};\n\nexport const verifyOTP = (data: IVerifyOTP): Promise<IApiResponseCommonInterface<string>> => {\n  return http.post(endpoint.auth.VERIFY_OTP, data);\n};\n\nexport const resendOTP = (data: IResendOTP): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.RESEND_OTP, data);\n};\n\nexport const forgotPassword = (data: IForgotPassword): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.FORGOT_PASSWORD, data);\n};\n\nexport const resetPassword = (data: IResetPassword): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.RESET_PASSWORD, data);\n};\n\nexport const deleteSession = (userId?: number): Promise<ApiResponse | AxiosResponse> => {\n  return http.remove(`${endpoint.auth.DELETE_SESSION}/${userId}`);\n};\n\nexport const updateTimezone = (data: { timezone: string }): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.auth.UPDATE_TIMEZONE, data);\n};\n\nexport const getUserPermissions = (): Promise<IApiResponseCommonInterface<UserPermissionsResponse>> => {\n  return http.get(endpoint.roles.USER_PERMISSIONS);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;AAMO,MAAM,QAAQ,CAAC;IACpB,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;AACzC;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;AAC7C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,cAAc,EAAE;AACjD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,SAAW,AAAD,EAAE,GAAG,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,QAAQ;AAChE;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD;AAEO,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB;AACjD", "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/commonService.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\nimport endpoint from \"@/constants/endpoint\";\nimport { ApiResponse, FilePath } from \"@/interfaces/commonInterfaces\";\n\nexport const removeAttachmentsFromS3 = (data: { fileUrlArray: string }): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.common.REMOVE_ATTACHMENTS_FROM_S3, data);\n};\n\nexport const getSignedUrl = (data: FilePath): Promise<ApiResponse<string | null>> => {\n  return http.post(endpoint.common.GENERATE_PRESIGNED_URL, data);\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,0BAA0B,EAAE;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,sBAAsB,EAAE;AAC3D", "debugId": null}}, {"offset": {"line": 1740, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/dataSecurityIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction dataSecurityIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"30\" height=\"30\" viewBox=\"0 0 43 42\" fill=\"none\">\n      <circle cx=\"21.5\" cy=\"21\" r=\"21\" fill=\"url(#paint0_linear_9593_1613)\" />\n      <path\n        d=\"M21.5091 9.28711H21.4886C18.2484 9.93022 15.0058 10.5721 11.7656 11.2139C11.7927 14.3154 11.8183 17.4181 11.8451 20.5211C11.8835 25.2709 14.6681 29.5713 18.9852 31.5523C19.8209 31.9347 20.6541 32.3187 21.4886 32.7014V32.7102C21.4924 32.709 21.4961 32.7064 21.499 32.7052C21.5015 32.7064 21.5053 32.709 21.5094 32.7102V32.7014C22.3439 32.3187 23.1771 31.935 24.0128 31.5523C28.3298 29.5716 31.1144 25.2709 31.1529 20.5211C31.1797 17.4184 31.2055 14.3154 31.2323 11.2139C27.9919 10.5721 24.7492 9.93022 21.5091 9.28711ZM29.4181 20.6065C29.3856 24.503 27.1019 28.0303 23.5604 29.6558C22.8757 29.9694 22.1919 30.2841 21.5072 30.5974V30.605C21.5043 30.604 21.5015 30.6021 21.4987 30.6012C21.4968 30.6021 21.4939 30.604 21.4911 30.605V30.5974C20.8064 30.2837 20.1226 29.9691 19.4379 29.6558C15.8964 28.0303 13.6127 24.503 13.5802 20.6065C13.5581 18.0621 13.537 15.5171 13.515 12.9727C16.1735 12.4462 18.8326 11.9198 21.4911 11.3924H21.5075C24.166 11.9198 26.8251 12.4462 29.4837 12.9727C29.4616 15.5171 29.4405 18.0621 29.4184 20.6065H29.4181Z\"\n        fill=\"white\"\n      />\n      <path\n        d=\"M25.0804 18.5382H24.734V17.2775C24.734 15.5752 23.3538 14.1953 21.6518 14.1953H21.3743C19.672 14.1953 18.2921 15.5752 18.2921 17.2775V18.5382H17.9457C17.6619 18.5382 17.4321 18.768 17.4321 19.0517V24.4369C17.4321 24.7206 17.6619 24.9517 17.9457 24.9517H25.0807C25.3645 24.9517 25.5955 24.7206 25.5955 24.4369V19.0517C25.5955 18.768 25.3645 18.5382 25.0807 18.5382H25.0804ZM21.7965 21.7077V23.3868C21.7965 23.4804 21.7195 23.5576 21.6243 23.5576H21.3996C21.3059 23.5576 21.2287 23.4807 21.2287 23.3868V21.7077C20.9525 21.5961 20.7561 21.3253 20.7561 21.0069C20.7561 20.5949 21.0875 20.2598 21.4982 20.2535C21.5033 20.2522 21.5074 20.2522 21.5124 20.2522C21.9282 20.2522 22.2671 20.5899 22.2671 21.0069C22.2671 21.3253 22.072 21.5961 21.7961 21.7077H21.7965ZM23.7554 18.5382H19.2136V17.1672C19.2136 15.967 20.1855 14.9938 21.3869 14.9938H21.5808C22.7822 14.9938 23.7554 15.967 23.7554 17.1672V18.5382Z\"\n        fill=\"white\"\n      />\n      <defs>\n        <linearGradient id=\"paint0_linear_9593_1613\" x1=\"-2.3\" y1=\"17.5\" x2=\"29.5828\" y2=\"-6.01022\" gradientUnits=\"userSpaceOnUse\">\n          <stop stopColor=\"#74A8FF\" />\n          <stop offset=\"0.474301\" stopColor=\"#AACAFF\" />\n          <stop offset=\"1\" stopColor=\"#5D86CC\" />\n        </linearGradient>\n      </defs>\n    </svg>\n  );\n}\n\nexport default dataSecurityIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,8OAAC;gBAAO,IAAG;gBAAO,IAAG;gBAAK,GAAE;gBAAK,MAAK;;;;;;0BACtC,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;oBAAe,IAAG;oBAA0B,IAAG;oBAAO,IAAG;oBAAO,IAAG;oBAAU,IAAG;oBAAW,eAAc;;sCACxG,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,QAAO;4BAAW,WAAU;;;;;;sCAClC,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;uCAEe", "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1842, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\n  LOGIN: \"/login\",\n  FORGOT_PASSWORD: \"/forgot-password\",\n  VERIFY: \"/verify\",\n  RESET_PASSWORD: \"/reset-password\",\n  CANDIDATE_ASSESSMENT: \"/candidate-assessment\",\n  DASHBOARD: \"/dashboard\",\n  HOME: \"/\",\n  BUY_SUBSCRIPTION: \"/buy-subscription\",\n  PROFILE: {\n    MY_PROFILE: \"/my-profile\",\n  },\n  SUBSCRIPTIONS: {\n    SUCCESS: \"/subscriptions/success\",\n    CANCEL: \"/subscriptions/cancel\",\n  },\n  JOBS: {\n    CAREER_BASED_SKILLS: \"/career-based-skills\",\n    ROLE_BASED_SKILLS: \"/role-based-skills\",\n    CULTURE_BASED_SKILLS: \"/culture-based-skills\",\n    GENERATE_JOB: \"/generate-job\",\n    EDIT_SKILLS: \"/edit-skills\",\n    HIRING_TYPE: \"/hiring-type\",\n    JOB_EDITOR: \"/job-editor\",\n    ACTIVE_JOBS: \"/active-jobs\",\n    CANDIDATE_PROFILE: \"/candidate-profile\",\n    ARCHIVE: \"/archive\",\n  },\n  SCREEN_RESUME: {\n    MANUAL_CANDIDATE_UPLOAD: \"/manual-upload-resume\",\n    CANDIDATE_QUALIFICATION: \"/candidate-qualification\",\n    CANDIDATE_LIST: \"/candidates-list\",\n    CANDIDATES: \"/candidates\",\n  },\n  INTERVIEW: {\n    ADD_CANDIDATE_INFO: \"/additional-submission\",\n    SCHEDULE_INTERVIEW: \"/schedule-interview\",\n    PRE_INTERVIEW_QUESTIONS_OVERVIEW: \"/pre-interview-questions-overview\",\n    INTERVIEW_QUESTION: \"/interview-question\",\n    CALENDAR: \"/calendar\",\n    INTERVIEW_SUMMARY: \"/interview-summary\",\n  },\n\n  ROLE_EMPLOYEES: {\n    ROLES_PERMISSIONS: \"/roles-permissions\",\n    EMPLOYEE_MANAGEMENT: \"/employee-management\",\n    EMPLOYEE_MANAGEMENT_DETAIL: \"/employee-management-detail\",\n    ADD_EMPLOYEE: \"/add-employees\",\n    ADD_DEPARTMENT: \"/add-department\",\n  },\n\n  FINAL_ASSESSMENT: {\n    FINAL_ASSESSMENT: \"/final-assessment\",\n  },\n};\n\nexport const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];\n\n// Routes that don't require permission checks for authenticated users\nexport const UNRESTRICTED_ROUTES = [ROUTES.SUBSCRIPTIONS.SUCCESS, ROUTES.SUBSCRIPTIONS.CANCEL];\n\nexport default ROUTES;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,SAAS;IACb,OAAO;IACP,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,sBAAsB;IACtB,WAAW;IACX,MAAM;IACN,kBAAkB;IAClB,SAAS;QACP,YAAY;IACd;IACA,eAAe;QACb,SAAS;QACT,QAAQ;IACV;IACA,MAAM;QACJ,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,aAAa;QACb,YAAY;QACZ,aAAa;QACb,mBAAmB;QACnB,SAAS;IACX;IACA,eAAe;QACb,yBAAyB;QACzB,yBAAyB;QACzB,gBAAgB;QAChB,YAAY;IACd;IACA,WAAW;QACT,oBAAoB;QACpB,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;QACpB,UAAU;QACV,mBAAmB;IACrB;IAEA,gBAAgB;QACd,mBAAmB;QACnB,qBAAqB;QACrB,4BAA4B;QAC5B,cAAc;QACd,gBAAgB;IAClB;IAEA,kBAAkB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,sBAAsB;IAAC,OAAO,KAAK;IAAE,OAAO,eAAe;IAAE,OAAO,MAAM;IAAE,OAAO,cAAc;IAAE,OAAO,oBAAoB;CAAC;AAGrI,MAAM,sBAAsB;IAAC,OAAO,aAAa,CAAC,OAAO;IAAE,OAAO,aAAa,CAAC,MAAM;CAAC;uCAE/E", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ProfileIcon.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\n\nconst ProfileIcon = () => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n      version=\"1.1\"\n      id=\"Layer_1\"\n      x=\"0px\"\n      y=\"0px\"\n      viewBox=\"0 0 512 512\"\n      xmlSpace=\"preserve\"\n      style={{ minWidth: \"20px\" }}\n      width=\"18\"\n      height=\"18\"\n    >\n      <g>\n        <g>\n          <path d=\"M256,0c-74.439,0-135,60.561-135,135s60.561,135,135,135s135-60.561,135-135S330.439,0,256,0z M256,240 c-57.897,0-105-47.103-105-105c0-57.897,47.103-105,105-105c57.897,0,105,47.103,105,105C361,192.897,313.897,240,256,240z\" />\n        </g>\n      </g>\n      <g>\n        <g>\n          <path d=\"M423.966,358.195C387.006,320.667,338.009,300,286,300h-60c-52.008,0-101.006,20.667-137.966,58.195 C51.255,395.539,31,444.833,31,497c0,8.284,6.716,15,15,15h420c8.284,0,15-6.716,15-15    C481,444.833,460.745,395.539,423.966,358.195z M61.66,482c7.515-85.086,78.351-152,164.34-152h60    c85.989,0,156.825,66.914,164.34,152H61.66z\" />\n        </g>\n      </g>\n    </svg>\n  );\n};\n\nexport default ProfileIcon;\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,cAAc;IAClB,qBACE,8OAAC;QACC,OAAM;QACN,YAAW;QACX,SAAQ;QACR,IAAG;QACH,GAAE;QACF,GAAE;QACF,SAAQ;QACR,UAAS;QACT,OAAO;YAAE,UAAU;QAAO;QAC1B,OAAM;QACN,QAAO;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;8BACC,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;0BAGZ,8OAAC;0BACC,cAAA,8OAAC;8BACC,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/LogoutIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction LogoutIcon({ className }: { className?: string }) {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n      style={{ minWidth: \"20px\", fill: \"#fff\" }}\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 32 33\"\n      fill=\"none\"\n    >\n      <g clipPath=\"url(#clip0_10037_3401)\">\n        <path\n          d=\"M18.6667 11.2415V8.57487C18.6667 7.86763 18.3857 7.18935 17.8856 6.68925C17.3855 6.18915 16.7072 5.9082 16 5.9082H6.66667C5.95942 5.9082 5.28115 6.18915 4.78105 6.68925C4.28095 7.18935 4 7.86763 4 8.57487V24.5749C4 25.2821 4.28095 25.9604 4.78105 26.4605C5.28115 26.9606 5.95942 27.2415 6.66667 27.2415H16C16.7072 27.2415 17.3855 26.9606 17.8856 26.4605C18.3857 25.9604 18.6667 25.2821 18.6667 24.5749V21.9082\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path d=\"M12 16.5762H28L24 12.5762\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n        <path d=\"M24 20.5762L28 16.5762\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_10037_3401\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(0 0.271484)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default LogoutIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,8OAAC;QACC,OAAM;QACN,WAAW;QACX,OAAO;YAAE,UAAU;YAAQ,MAAM;QAAO;QACxC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;;0BAEL,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBAAK,GAAE;wBAA4B,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;kCAC3F,8OAAC;wBAAK,GAAE;wBAAyB,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;;;;;;;0BAE1F,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 2080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/loader/Loader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTranslations } from \"next-intl\";\n\nconst Loader = ({ className }: { className?: string }) => {\n  const t = useTranslations();\n  return (\n    <div className={`spinner-border ${className}`} role=\"status\">\n      <span className=\"visually-hidden\">{t(\"loading\")}</span>\n    </div>\n  );\n};\n\nexport default Loader;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS,CAAC,EAAE,SAAS,EAA0B;IACnD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,WAAW;QAAE,MAAK;kBAClD,cAAA,8OAAC;YAAK,WAAU;sBAAmB,EAAE;;;;;;;;;;;AAG3C;uCAEe", "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2120, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Button.tsx"], "sourcesContent": ["import React, { ButtonHTMLAttributes, DetailedHTMLP<PERSON>, MouseEvent, ReactNode } from \"react\";\nimport Loader from \"@/components/loader/Loader\";\n\ninterface Props extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {\n  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;\n  disabled?: boolean;\n  children?: ReactNode;\n  loading?: boolean;\n}\n\nconst Button = ({ className, type, disabled = false, onClick, children, loading }: Props) => (\n  <button\n    type={type}\n    className={`theme-btn ${className}`}\n    onClick={(e) => {\n      if (onClick) {\n        onClick(e as unknown as MouseEvent<HTMLButtonElement>);\n      }\n    }}\n    disabled={disabled}\n    aria-label=\"\"\n  >\n    {children}\n    {loading ? <Loader /> : null}\n  </button>\n);\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,SAAS,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAS,iBACtF,8OAAC;QACC,MAAM;QACN,WAAW,CAAC,UAAU,EAAE,WAAW;QACnC,SAAS,CAAC;YACR,IAAI,SAAS;gBACX,QAAQ;YACV;QACF;QACA,UAAU;QACV,cAAW;;YAEV;YACA,wBAAU,8OAAC,sIAAA,CAAA,UAAM;;;;uBAAM;;;;;;;uCAIb", "debugId": null}}, {"offset": {"line": 2151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/notificationServices/notificationService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { NotificationItem } from \"@/interfaces/notificationInterface\";\n\nexport const getNotifications = (data: { offset: number; limit: number }): Promise<IApiResponseCommonInterface<NotificationItem[]>> => {\n  return http.get(endpoint.notification.GET_NOTIFICATIONS, data);\n};\n\nexport const deleteAllNotifications = (): Promise<IApiResponseCommonInterface<unknown>> => {\n  return http.remove(endpoint.notification.DELETE_ALL_NOTIFICATIONS);\n};\n\nexport const updateNotificationStatus = (): Promise<IApiResponseCommonInterface<unknown>> => {\n  return http.post(endpoint.notification.UPDATE_NOTIFICATION, {});\n};\n\nexport const getUnreadNotificationsCount = (): Promise<IApiResponseCommonInterface<{ count: number }>> => {\n  return http.get(endpoint.notification.GET_UNREAD_NOTIFICATIONS_COUNT);\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAIO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE;AAC3D;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAA,GAAA,oHAAA,CAAA,SAAW,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,wBAAwB;AACnE;AAEO,MAAM,2BAA2B;IACtC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;AAC/D;AAEO,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,8BAA8B;AACtE", "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2185, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/notification/Notifications.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\nimport Button from \"@/components/formElements/Button\";\nimport { DEFAULT_LIMIT } from \"@/constants/commonConstants\";\nimport { setHasUnreadNotification, setNotificationsData } from \"@/redux/slices/notificationSlice\";\nimport { getNotifications, deleteAllNotifications, updateNotificationStatus } from \"@/services/notificationServices/notificationService\";\nimport { toastMessageError } from \"@/utils/helper\";\nimport { useTranslations } from \"next-intl\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport InfiniteScroll from \"react-infinite-scroll-component\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { RootState } from \"@/redux/store\";\nimport Skeleton from \"react-loading-skeleton\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\n\ninterface NotificationsProps {\n  setIsNotificationOpen: (value: boolean) => void;\n}\n\nconst Notifications = ({ setIsNotificationOpen }: NotificationsProps) => {\n  const t = useTranslations();\n  const tCommon = useTranslations(\"common\");\n  const [loading, setLoading] = useState<boolean>(true);\n  const [offset, setOffset] = useState<number>(0);\n  const [hasMore, setHasMore] = useState<boolean>(true);\n  const notificationRef = useRef<HTMLDivElement>(null);\n  const dispatch = useDispatch();\n  const { notifications } = useSelector((state: RootState) => state.notification);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        notificationRef.current &&\n        !notificationRef.current.contains(event.target as Node) &&\n        (event.target as HTMLElement).id !== \"notification-icon-id\"\n      ) {\n        setIsNotificationOpen(false);\n        dispatch(setHasUnreadNotification(false));\n        dispatch(setNotificationsData(notifications.map((item) => ({ ...item, isWatched: 1 }))));\n        updateNotificationStatus();\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  useEffect(() => {\n    dispatch(setNotificationsData([]));\n    fetchNotifications(offset);\n  }, []);\n\n  const fetchNotifications = async (offset: number) => {\n    try {\n      setLoading(true);\n      const response = await getNotifications({ offset, limit: DEFAULT_LIMIT });\n\n      if (response.data?.success) {\n        console.log(\"Notifications fetched successfully:\", response.data.data);\n\n        dispatch(setNotificationsData([...notifications, ...response.data.data]));\n        setOffset((prevOffset) => prevOffset + DEFAULT_LIMIT);\n        setHasMore(response.data.data.length === DEFAULT_LIMIT);\n      } else {\n        toastMessageError(t(response?.data?.message));\n      }\n    } catch (error) {\n      console.log(error);\n\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClearAll = async () => {\n    try {\n      setLoading(true);\n      const res = await deleteAllNotifications();\n\n      if (res.data?.success) {\n        dispatch(setNotificationsData([])); // Clear UI\n      } else {\n        toastMessageError(t(\"failed_to_delete_notifications\"));\n      }\n    } catch {\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"notifications\" ref={notificationRef}>\n      <div className=\"header-content\">\n        <h3>{tCommon(\"notifications\")}</h3>\n        <Button onClick={notifications.length > 0 ? handleClearAll : undefined} className=\"clear-btn p-0\">\n          {tCommon(\"clear_all\")}\n        </Button>\n      </div>\n\n      <div className=\"notification-wrapper\" id=\"notification-scroll-container\">\n        <InfiniteScroll\n          dataLength={notifications.length}\n          next={() => fetchNotifications(offset)}\n          hasMore={hasMore}\n          loader={\n            <>\n              {[...Array(4)].map((_, rowIndex) => (\n                <div className=\"notification-item\" key={rowIndex}>\n                  <h4 style={{ margin: 0 }}>\n                    <Skeleton height={17} width=\"100%\" borderRadius={4} />\n                  </h4>\n                  <p>\n                    <Skeleton height={14} width=\"80%\" borderRadius={4} />\n                  </p>\n                  <p className=\"time\">\n                    <Skeleton height={10} width=\"20%\" borderRadius={4} />\n                  </p>\n                </div>\n              ))}\n            </>\n          }\n          scrollableTarget=\"notification-scroll-container\"\n          endMessage={\n            notifications.length > 0 && (\n              <p className=\"p-2 text-center\">\n                <strong>{tCommon(\"no_more_notifications\")}</strong>\n              </p>\n            )\n          }\n        >\n          {notifications.length === 0 && !loading ? (\n            <p className=\"p-2 text-center\">\n              <strong> {tCommon(\"no_notifications_found\")}</strong>\n            </p>\n          ) : (\n            notifications.map((item) => (\n              <div key={item.id} className={`notification-item ${item.isWatched === 0 ? \"unread\" : \"\"}`}>\n                <h4 style={{ margin: 0 }}>{item.title}</h4>\n                <p>{item.description}</p>\n                <p className=\"time\">\n                  {new Date(item.createdTs).toLocaleString(\"en-IN\", {\n                    dateStyle: \"medium\",\n                    timeStyle: \"short\",\n                  })}\n                </p>\n              </div>\n            ))\n          )}\n        </InfiniteScroll>\n      </div>\n    </div>\n  );\n};\n\nexport default React.memo(Notifications);\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;AAOA,MAAM,gBAAgB,CAAC,EAAE,qBAAqB,EAAsB;IAClE,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,YAAY;IAE9E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,gBAAgB,OAAO,IACvB,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC9C,AAAC,MAAM,MAAM,CAAiB,EAAE,KAAK,wBACrC;gBACA,sBAAsB;gBACtB,SAAS,CAAA,GAAA,2IAAA,CAAA,2BAAwB,AAAD,EAAE;gBAClC,SAAS,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc,GAAG,CAAC,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;oBAAE,CAAC;gBACpF,CAAA,GAAA,8JAAA,CAAA,2BAAwB,AAAD;YACzB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE,EAAE;QAChC,mBAAmB;IACrB,GAAG,EAAE;IAEL,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE;gBAAQ,OAAO,mIAAA,CAAA,gBAAa;YAAC;YAEvE,IAAI,SAAS,IAAI,EAAE,SAAS;gBAC1B,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI,CAAC,IAAI;gBAErE,SAAS,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE;uBAAI;uBAAkB,SAAS,IAAI,CAAC,IAAI;iBAAC;gBACvE,UAAU,CAAC,aAAe,aAAa,mIAAA,CAAA,gBAAa;gBACpD,WAAW,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,mIAAA,CAAA,gBAAa;YACxD,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,MAAM,MAAM,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD;YAEvC,IAAI,IAAI,IAAI,EAAE,SAAS;gBACrB,SAAS,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE,EAAE,IAAI,WAAW;YACjD,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACtB;QACF,EAAE,OAAM;YACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAgB,KAAK;;0BAClC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAI,QAAQ;;;;;;kCACb,8OAAC,4IAAA,CAAA,UAAM;wBAAC,SAAS,cAAc,MAAM,GAAG,IAAI,iBAAiB;wBAAW,WAAU;kCAC/E,QAAQ;;;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;gBAAuB,IAAG;0BACvC,cAAA,8OAAC,+KAAA,CAAA,UAAc;oBACb,YAAY,cAAc,MAAM;oBAChC,MAAM,IAAM,mBAAmB;oBAC/B,SAAS;oBACT,sBACE;kCACG;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,yBACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDACrB,cAAA,8OAAC,6JAAA,CAAA,UAAQ;4CAAC,QAAQ;4CAAI,OAAM;4CAAO,cAAc;;;;;;;;;;;kDAEnD,8OAAC;kDACC,cAAA,8OAAC,6JAAA,CAAA,UAAQ;4CAAC,QAAQ;4CAAI,OAAM;4CAAM,cAAc;;;;;;;;;;;kDAElD,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC,6JAAA,CAAA,UAAQ;4CAAC,QAAQ;4CAAI,OAAM;4CAAM,cAAc;;;;;;;;;;;;+BARZ;;;;;;oBAc9C,kBAAiB;oBACjB,YACE,cAAc,MAAM,GAAG,mBACrB,8OAAC;wBAAE,WAAU;kCACX,cAAA,8OAAC;sCAAQ,QAAQ;;;;;;;;;;;8BAKtB,cAAc,MAAM,KAAK,KAAK,CAAC,wBAC9B,8OAAC;wBAAE,WAAU;kCACX,cAAA,8OAAC;;gCAAO;gCAAE,QAAQ;;;;;;;;;;;+BAGpB,cAAc,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAAkB,WAAW,CAAC,kBAAkB,EAAE,KAAK,SAAS,KAAK,IAAI,WAAW,IAAI;;8CACvF,8OAAC;oCAAG,OAAO;wCAAE,QAAQ;oCAAE;8CAAI,KAAK,KAAK;;;;;;8CACrC,8OAAC;8CAAG,KAAK,WAAW;;;;;;8CACpB,8OAAC;oCAAE,WAAU;8CACV,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS;wCAChD,WAAW;wCACX,WAAW;oCACb;;;;;;;2BAPM,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAgB/B;qDAEe,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2465, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2471, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavCalendarIcon.tsx"], "sourcesContent": ["const NavCalendarIcon = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 25 27\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        style={{ width: \"24px\", height: \"22px\" }}\n        d=\"M23.2117 11.406H1.1439C0.631332 11.406 0.215332 10.99 0.215332 10.4774C0.215332 9.96483 0.631332 9.54883 1.1439 9.54883H23.2117C23.7243 9.54883 24.1403 9.96483 24.1403 10.4774C24.1403 10.99 23.7243 11.406 23.2117 11.406Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M17.6835 16.24C17.171 16.24 16.75 15.824 16.75 15.3114C16.75 14.7988 17.1598 14.3828 17.6724 14.3828H17.6835C18.1961 14.3828 18.6121 14.7988 18.6121 15.3114C18.6121 15.824 18.1961 16.24 17.6835 16.24Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.1899 16.24C11.6773 16.24 11.2563 15.824 11.2563 15.3114C11.2563 14.7988 11.6662 14.3828 12.1787 14.3828H12.1899C12.7024 14.3828 13.1184 14.7988 13.1184 15.3114C13.1184 15.824 12.7024 16.24 12.1899 16.24Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M6.68427 16.24C6.1717 16.24 5.74951 15.824 5.74951 15.3114C5.74951 14.7988 6.16056 14.3828 6.67313 14.3828H6.68427C7.19685 14.3828 7.61285 14.7988 7.61285 15.3114C7.61285 15.824 7.19685 16.24 6.68427 16.24Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M17.6835 21.0525C17.171 21.0525 16.75 20.6365 16.75 20.1239C16.75 19.6113 17.1598 19.1953 17.6724 19.1953H17.6835C18.1961 19.1953 18.6121 19.6113 18.6121 20.1239C18.6121 20.6365 18.1961 21.0525 17.6835 21.0525Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.1899 21.0525C11.6773 21.0525 11.2563 20.6365 11.2563 20.1239C11.2563 19.6113 11.6662 19.1953 12.1787 19.1953H12.1899C12.7024 19.1953 13.1184 19.6113 13.1184 20.1239C13.1184 20.6365 12.7024 21.0525 12.1899 21.0525Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M6.68427 21.0525C6.1717 21.0525 5.74951 20.6365 5.74951 20.1239C5.74951 19.6113 6.16056 19.1953 6.67313 19.1953H6.68427C7.19685 19.1953 7.61285 19.6113 7.61285 20.1239C7.61285 20.6365 7.19685 21.0525 6.68427 21.0525Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M17.1786 6.31257C16.666 6.31257 16.25 5.89657 16.25 5.384V1.30943C16.25 0.796859 16.666 0.380859 17.1786 0.380859C17.6911 0.380859 18.1071 0.796859 18.1071 1.30943V5.384C18.1071 5.89657 17.6911 6.31257 17.1786 6.31257Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M7.17711 6.31257C6.66454 6.31257 6.24854 5.89657 6.24854 5.384V1.30943C6.24854 0.796859 6.66454 0.380859 7.17711 0.380859C7.68968 0.380859 8.10568 0.796859 8.10568 1.30943V5.384C8.10568 5.89657 7.68968 6.31257 7.17711 6.31257Z\"\n        // fill=\"#333333\"\n      />\n      <mask id=\"mask0_15282_6458\" style={{ maskType: \"luminance\" }} maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"2\" width=\"25\" height=\"25\">\n        <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M0.101074 2.33594H24.2439V26.9999H0.101074V2.33594Z\" fill=\"white\" />\n      </mask>\n      <g>\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M6.93671 4.19289C3.72633 4.19289 1.95833 5.90518 1.95833 9.01404V20.2176C1.95833 23.3945 3.72633 25.1427 6.93671 25.1427H17.4085C20.6189 25.1427 22.3869 23.4267 22.3869 20.3117V9.01404C22.3919 7.48499 21.9808 6.29642 21.1649 5.47928C20.3255 4.63737 19.0317 4.19289 17.4197 4.19289H6.93671ZM17.4084 27H6.9366C2.72088 27 0.101074 24.4013 0.101074 20.2177V9.01422C0.101074 4.89384 2.72088 2.33594 6.9366 2.33594H17.4196C19.5355 2.33594 21.2849 2.96984 22.4796 4.16708C23.6397 5.33213 24.2501 7.00727 24.2439 9.0167V20.3118C24.2439 24.4372 21.6241 27 17.4084 27Z\"\n          // fill=\"#333333\"\n        />\n      </g>\n    </svg>\n  );\n};\n\nexport default NavCalendarIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;gBACvC,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBAAK,IAAG;gBAAmB,OAAO;oBAAE,UAAU;gBAAY;gBAAG,WAAU;gBAAiB,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;0BACrH,cAAA,8OAAC;oBAAK,UAAS;oBAAU,UAAS;oBAAU,GAAE;oBAAsD,MAAK;;;;;;;;;;;0BAE3G,8OAAC;0BACC,cAAA,8OAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 2615, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavCandidatesIcon.tsx"], "sourcesContent": ["const NavCandidatesIcon = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 21\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        style={{ width: \"24px\", height: \"25px\" }}\n        d=\"M13.5942 11.4094H13.6276C16.7476 11.4094 19.2845 8.87132 19.2845 5.75256C19.2845 2.6338 16.7476 0.0957031 13.6276 0.0957031C10.5089 0.0957031 7.97201 2.6338 7.97201 5.74885C7.96582 7.25561 8.54648 8.67447 9.60877 9.74294C10.6686 10.8127 12.0837 11.4045 13.5942 11.4094ZM9.82915 5.75256C9.82915 3.6577 11.5328 1.95285 13.6276 1.95285C15.7225 1.95285 17.4273 3.6577 17.4273 5.75256C17.4273 7.84866 15.7225 9.55227 13.6276 9.55227H13.5967C12.5864 9.54856 11.638 9.15113 10.9261 8.43427C10.2142 7.71742 9.82544 6.7678 9.82915 5.75256Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        d=\"M20.1376 9.38204C20.2032 9.84633 20.6007 10.1819 21.0563 10.1819C21.0984 10.1819 21.1417 10.1794 21.185 10.1732C23.3827 9.8649 25.0429 7.95823 25.0479 5.73585C25.0479 3.52833 23.4668 1.66623 21.2903 1.30842C20.7814 1.22795 20.3072 1.56719 20.223 2.07357C20.1401 2.57995 20.483 3.05785 20.9882 3.14081C22.2647 3.35004 23.1908 4.44204 23.1908 5.73338C23.1883 7.03585 22.2164 8.15262 20.9288 8.33338C20.4199 8.40395 20.067 8.87319 20.1376 9.38204Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        d=\"M23.8712 17.4887C24.0123 17.8588 24.3652 18.0867 24.7391 18.0867C24.8493 18.0867 24.9607 18.0668 25.0697 18.026C26.8402 17.3512 27.1051 16.1218 27.1051 15.4483C27.1051 14.3451 26.4749 12.9362 23.475 12.4868C22.9637 12.4211 22.4932 12.7591 22.4177 13.2668C22.3422 13.7756 22.6925 14.2473 23.1989 14.3241C24.5583 14.5271 25.248 14.906 25.248 15.4483C25.248 15.6179 25.248 15.9708 24.4085 16.2902C23.9294 16.4722 23.688 17.0095 23.8712 17.4887Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M13.6276 20.8845C11.5736 20.8845 5.32121 20.8845 5.32121 16.9288C5.32121 12.9892 11.5736 12.9892 13.6276 12.9892C15.6816 12.9892 21.9327 12.9892 21.9327 16.9498C21.9327 20.8845 15.8995 20.8845 13.6276 20.8845ZM13.6276 14.8463C10.6859 14.8463 7.17835 15.2078 7.17835 16.9288C7.17835 18.6633 10.6859 19.0273 13.6276 19.0273C16.5693 19.0273 20.0756 18.6671 20.0756 16.9498C20.0756 15.2115 16.5693 14.8463 13.6276 14.8463Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        d=\"M6.19776 10.1818C6.15567 10.1818 6.11233 10.1793 6.069 10.1731C3.87138 9.86483 2.21233 7.95817 2.20738 5.73826C2.20738 3.52826 3.78843 1.66617 5.965 1.30836C6.48624 1.22664 6.94805 1.56959 7.03224 2.0735C7.11519 2.57988 6.77224 3.05778 6.2671 3.14074C4.99062 3.34997 4.06452 4.44197 4.06452 5.73578C4.067 7.03578 5.0389 8.15378 6.32529 8.33331C6.83414 8.40388 7.187 8.87312 7.11643 9.38198C7.05081 9.84626 6.65338 10.1818 6.19776 10.1818Z\"\n        // fill=\"#333333\"\n      />\n      <path\n        d=\"M2.18559 18.026C2.29454 18.0669 2.40597 18.0867 2.51616 18.0867C2.89007 18.0867 3.24293 17.8589 3.38407 17.4887C3.56731 17.0096 3.32588 16.4722 2.84674 16.2902C2.00607 15.9696 2.00607 15.6179 2.00607 15.4483C2.00607 14.906 2.69569 14.5272 4.05512 14.3241C4.5615 14.2474 4.91188 13.7757 4.83635 13.2668C4.75959 12.7592 4.29159 12.4224 3.78026 12.4868C0.779116 12.9362 0.148926 14.3464 0.148926 15.4483C0.148926 16.1206 0.413878 17.35 2.18559 18.026Z\"\n        // fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default NavCandidatesIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,oBAAoB;IACxB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;;0BAC/D,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;gBACvC,GAAE;;;;;;0BAGJ,8OAAC;gBACC,GAAE;;;;;;0BAGJ,8OAAC;gBACC,GAAE;;;;;;0BAGJ,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAGJ,8OAAC;gBACC,GAAE;;;;;;0BAGJ,8OAAC;gBACC,GAAE;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2690, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2696, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavHomeIcon.tsx"], "sourcesContent": ["const NavHomeIcon = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" style={{ width: \"24px\", height: \"24px\" }} viewBox=\"0 0 30 30\" fill=\"none\">\n      <g>\n        <path d=\"M24.0664 13.9443C24.0664 13.212 23.7285 12.5205 23.1504 12.0713H23.1494L16.5469 6.93555C16.1304 6.61158 15.6175 6.43555 15.0898 6.43555C14.5624 6.43565 14.0501 6.61168 13.6338 6.93555L7.0293 12.0713C6.74431 12.293 6.51407 12.577 6.35547 12.9014C6.19686 13.2258 6.11414 13.5822 6.11426 13.9434V22.8584C6.11426 23.2689 6.27715 23.6629 6.56738 23.9531C6.8576 24.2433 7.25173 24.4062 7.66211 24.4062H22.5186C22.9289 24.4062 23.3231 24.2433 23.6133 23.9531C23.9035 23.6629 24.0664 23.2689 24.0664 22.8584V13.9443ZM25.9238 22.8584C25.9238 23.7612 25.5651 24.6271 24.9268 25.2656C24.2882 25.9041 23.4216 26.2637 22.5186 26.2637H7.66211C6.75911 26.2637 5.89242 25.9041 5.25391 25.2656C4.6156 24.6271 4.25684 23.7612 4.25684 22.8584V13.9443C4.25657 13.3002 4.40368 12.6646 4.68652 12.0859C4.96946 11.5071 5.38108 11 5.88965 10.6045L12.4932 5.46973C13.2355 4.89223 14.1493 4.57823 15.0898 4.57812C16.0305 4.57813 16.945 4.89213 17.6875 5.46973L24.2891 10.6045C25.3204 11.4058 25.9238 12.6389 25.9238 13.9443V22.8584Z\" />\n        <path d=\"M9.30687 18.6483C9.57186 18.2093 10.1423 18.068 10.5813 18.3328C11.7705 19.0508 13.3869 19.4373 15.053 19.4373C16.7189 19.4373 18.3359 19.0509 19.5266 18.3328C19.9657 18.068 20.5371 18.2091 20.802 18.6483C21.0666 19.0872 20.9253 19.6577 20.4866 19.9227C18.9411 20.8549 16.9718 21.2938 15.053 21.2938C13.1344 21.2937 11.1666 20.8549 9.6223 19.9227C9.18337 19.6577 9.04207 19.0872 9.30687 18.6483Z\" />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_15321_8934\">\n          <rect width=\"29.7143\" height=\"29.7143\" fill=\"white\" transform=\"translate(0.196289 0.142578)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n};\n\nexport default NavHomeIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAO;QAAG,SAAQ;QAAY,MAAK;;0BACzG,8OAAC;;kCACC,8OAAC;wBAAK,GAAE;;;;;;kCACR,8OAAC;wBAAK,GAAE;;;;;;;;;;;;0BAEV,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAU,QAAO;wBAAU,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKxE;uCAEe", "debugId": null}}, {"offset": {"line": 2764, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2770, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavJobsIcon.tsx"], "sourcesContent": ["const NavJobsIcon = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" style={{ width: \"25px\", height: \"24px\" }} viewBox=\"0 0 31 30\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M9.9135 8.37664H5.06568C3.70607 8.37664 2.5918 9.50363 2.5918 10.9084V15.6188C2.5918 15.9587 2.82564 16.254 3.15658 16.3318L3.58889 16.4335V23.6685C3.58889 24.2863 3.83434 24.8788 4.27115 25.3156C4.70803 25.7525 5.30047 25.9979 5.91828 25.9979H24.7605C25.3783 25.9979 25.9708 25.7525 26.4076 25.3156C26.8445 24.8788 27.0899 24.2863 27.0899 23.6685V16.4335L27.5222 16.3318C27.8531 16.254 28.087 15.9587 28.087 15.6188V10.9084C28.087 9.50363 26.9727 8.37664 25.6131 8.37664H20.7653V6.90869C20.7653 5.30445 19.4648 4.00391 17.8605 4.00391H12.8183C11.214 4.00391 9.9135 5.30445 9.9135 6.90869V8.37664ZM18.6651 18.4142L25.6251 16.7779V23.6685C25.6251 23.8978 25.5339 24.1177 25.3718 24.2798C25.2097 24.4419 24.9898 24.533 24.7605 24.533H5.91828C5.689 24.533 5.4691 24.4419 5.30697 24.2798C5.14484 24.1177 5.05373 23.8978 5.05373 23.6685V16.7779L12.0137 18.4142V19.3379C12.0137 20.2357 12.7426 20.9646 13.6404 20.9646H17.0384C17.9362 20.9646 18.6651 20.2357 18.6651 19.3379V18.4142ZM17.2002 19.3379V17.5495C17.2002 17.4602 17.1277 17.3877 17.0384 17.3877H13.6404C13.5511 17.3877 13.4786 17.4602 13.4786 17.5495V19.3379C13.4786 19.4272 13.5511 19.4997 13.6404 19.4997H17.0384C17.1277 19.4997 17.2002 19.4272 17.2002 19.3379ZM18.5454 16.9375C18.3038 16.3426 17.7197 15.9229 17.0384 15.9229H13.6404C12.9591 15.9229 12.375 16.3426 12.1334 16.9375L4.05664 15.0386V10.9084C4.05664 10.3256 4.5016 9.84148 5.06568 9.84148H25.6131C26.1772 9.84148 26.6221 10.3256 26.6221 10.9084V15.0386L18.5454 16.9375ZM19.3004 8.37664H11.3783V6.90869C11.3783 6.11346 12.023 5.46875 12.8183 5.46875H17.8605C18.6558 5.46875 19.3004 6.11346 19.3004 6.90869V8.37664Z\"\n        // fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default NavJobsIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAO;QAAG,SAAQ;QAAY,MAAK;kBACzG,cAAA,8OAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2800, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2806, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NavSettingsIcon.tsx"], "sourcesContent": ["const NavSettingsIcon = () => {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 31 30\" fill=\"none\">\n      <path\n        d=\"M26.7675 16.9907L25.7251 16.1326C25.011 15.5448 25.0123 14.4523 25.7251 13.8657L26.7675 13.0076C27.5092 12.3969 27.699 11.3497 27.2186 10.5174L25.1603 6.95242C24.6798 6.12025 23.6778 5.76104 22.7782 6.09803L21.5139 6.57172C20.6478 6.89609 19.7023 6.349 19.5506 5.43824L19.3286 4.1064C19.1707 3.15864 18.3586 2.4707 17.3977 2.4707H13.2812C12.3203 2.4707 11.5082 3.15864 11.3503 4.10645L11.1283 5.43824C10.9762 6.35061 10.0294 6.89555 9.16502 6.57177L7.90068 6.09803C7.00102 5.76104 5.99914 6.1203 5.5186 6.95242L3.46036 10.5174C2.97993 11.3495 3.16962 12.3968 3.91149 13.0075L4.95384 13.8656C5.66797 14.4535 5.66655 15.5459 4.95384 16.1326L3.91144 16.9907C3.16962 17.6015 2.97988 18.6487 3.46032 19.4808L5.5186 23.0459C5.99909 23.878 7.00078 24.2372 7.90068 23.9002L9.16497 23.4266C10.0312 23.102 10.9765 23.6495 11.1282 24.56L11.3502 25.8918C11.5082 26.8397 12.3203 27.5276 13.2812 27.5276H17.3977C18.3586 27.5276 19.1707 26.8397 19.3286 25.8919L19.5505 24.5601C19.7025 23.648 20.6493 23.1027 21.5138 23.4267L22.7782 23.9003C23.6781 24.2373 24.6798 23.8781 25.1603 23.0459L27.2186 19.4808C27.699 18.6487 27.5092 17.6015 26.7675 16.9907ZM23.465 22.0671L22.2006 21.5934C20.1795 20.8363 17.9738 22.1137 17.6197 24.2383L17.3977 25.5701H13.2812L13.0592 24.2383C12.7045 22.1096 10.4955 20.8378 8.47826 21.5934L7.21391 22.0671L5.15567 18.5021L6.19803 17.644C7.86436 16.2722 7.86089 13.7233 6.19803 12.3543L5.15567 11.4962L7.21396 7.9312L8.47826 8.40489C10.4995 9.16198 12.7051 7.88466 13.0592 5.76006L13.2812 4.42827H17.3977L17.6196 5.76006C17.9744 7.88902 20.1835 9.16041 22.2006 8.40489L23.4649 7.9312L25.5237 11.4957C25.5237 11.4957 25.5235 11.4959 25.5231 11.4962L24.4808 12.3543C22.8145 13.726 22.8179 16.275 24.4808 17.6439L25.5232 18.502L23.465 22.0671ZM15.3394 10.1705C12.6769 10.1705 10.5108 12.3366 10.5108 14.9992C10.5108 17.6617 12.6769 19.8279 15.3394 19.8279C18.002 19.8279 20.1681 17.6617 20.1681 14.9992C20.1681 12.3366 18.002 10.1705 15.3394 10.1705ZM15.3394 17.8703C13.7563 17.8703 12.4683 16.5823 12.4683 14.9992C12.4683 13.416 13.7563 12.128 15.3394 12.128C16.9226 12.128 18.2106 13.416 18.2106 14.9992C18.2106 16.5823 16.9226 17.8703 15.3394 17.8703Z\"\n        // fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default NavSettingsIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,MAAK;kBAC/D,cAAA,8OAAC;YACC,GAAE;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2830, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2836, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\n      <path\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default ModalCloseIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 2877, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/MobileSidebar.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport NavSettingsIcon from \"../svgComponents/NavSettingsIcon\";\nimport NavJobsIcon from \"../svgComponents/NavJobsIcon\";\nimport NavCalendarIcon from \"../svgComponents/NavCalendarIcon\";\nimport NavCandidatesIcon from \"../svgComponents/NavCandidatesIcon\";\nimport NavHomeIcon from \"../svgComponents/NavHomeIcon\";\nimport { useRouter } from \"next/navigation\";\nimport ROUTES from \"@/constants/routes\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport NotificationIcon from \"../svgComponents/Notification\";\nimport LogoutIcon from \"../svgComponents/LogoutIcon\";\nimport { useSelector } from \"react-redux\";\nimport { selectProfileData } from \"@/redux/slices/authSlice\";\nimport { IUserData } from \"@/interfaces/authInterfaces\";\nimport Image from \"next/image\";\nimport User from \"../../../public/assets/images/user.png\";\nimport downArrow from \"../../../public/assets/images/down-arrow.svg\";\nimport ProfileIcon from \"../svgComponents/ProfileIcon\";\nimport { useTranslations } from \"next-intl\";\n\nconst MobileSidebar = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {\n  const navigate = useRouter();\n  const [subLink, setSubLink] = useState(false);\n  const userProfile: IUserData | null = useSelector(selectProfileData);\n  const t = useTranslations(\"header\");\n\n  return (\n    <>\n      <div className={`sidebar ${isOpen ? \"open\" : \"\"}`}>\n        <div className=\"sidebar-header\">\n          <Image src={userProfile?.image || User} alt=\"Profile\" className=\"sidebar-profile\" width={100} height={100} />\n          <h5 id=\"dropdown-user-name\">{`${userProfile?.first_name}`}</h5>\n\n          <Button onClick={onClose} className=\"clear-btn p-0\">\n            <ModalCloseIcon />\n          </Button>\n        </div>\n        <div className=\"sidebar-menu\">\n          <li\n            onClick={() => {\n              navigate.push(ROUTES.DASHBOARD);\n              onClose();\n            }}\n          >\n            <NavHomeIcon /> Home\n          </li>\n          <li\n            onClick={() => {\n              navigate.push(ROUTES.SCREEN_RESUME.CANDIDATES);\n              onClose();\n            }}\n          >\n            <NavCandidatesIcon /> Candidates\n          </li>\n          <li\n            onClick={() => {\n              navigate.push(ROUTES.INTERVIEW.CALENDAR);\n              onClose();\n            }}\n          >\n            <NavCalendarIcon /> Calendar\n          </li>\n          <li\n            onClick={() => {\n              navigate.push(ROUTES.JOBS.ACTIVE_JOBS);\n              onClose();\n            }}\n          >\n            <NavJobsIcon /> Jobs\n          </li>\n          <li className=\"sub-menu-bar\" onClick={() => setSubLink(!subLink)}>\n            <div className=\"sub-menu-list\">\n              <NavSettingsIcon />{\" \"}\n              <span>\n                Settings{\" \"}\n                <Image src={downArrow} alt=\"downArrow\" style={{ rotate: `${subLink ? \"180deg\" : \"0deg\"}`, width: \"13px\", marginLeft: \"5px\" }} />\n              </span>\n            </div>\n            {subLink && (\n              <ul className=\"sidebar-menu sidebar-sub-menu\">\n                <li\n                  onClick={() => {\n                    navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);\n                  }}\n                >\n                  Roles and Permissions\n                </li>\n                <li\n                  onClick={() => {\n                    navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);\n                  }}\n                >\n                  Employee Management\n                </li>\n              </ul>\n            )}\n          </li>\n          <li>\n            <NotificationIcon />\n            Notifications\n          </li>\n          <li\n            onClick={() => {\n              navigate.push(ROUTES.DASHBOARD);\n              onClose();\n            }}\n          >\n            <ProfileIcon /> {t(\"my_profile\")}\n          </li>\n          <li>\n            <LogoutIcon className=\"strokeSvg\" />\n            Logout\n          </li>\n        </div>\n      </div>\n\n      {isOpen && <div className=\"overlay\" onClick={onClose}></div>}\n    </>\n  );\n};\n\nexport default MobileSidebar;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAA4C;IAClF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,cAAgC,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,mIAAA,CAAA,oBAAiB;IACnE,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,SAAS,IAAI;;kCAC/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAK,aAAa,SAAS,oSAAA,CAAA,UAAI;gCAAE,KAAI;gCAAU,WAAU;gCAAkB,OAAO;gCAAK,QAAQ;;;;;;0CACtG,8OAAC;gCAAG,IAAG;0CAAsB,GAAG,aAAa,YAAY;;;;;;0CAEzD,8OAAC,4IAAA,CAAA,UAAM;gCAAC,SAAS;gCAAS,WAAU;0CAClC,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;oCACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;oCAC9B;gCACF;;kDAEA,8OAAC,kJAAA,CAAA,UAAW;;;;;oCAAG;;;;;;;0CAEjB,8OAAC;gCACC,SAAS;oCACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,UAAU;oCAC7C;gCACF;;kDAEA,8OAAC,wJAAA,CAAA,UAAiB;;;;;oCAAG;;;;;;;0CAEvB,8OAAC;gCACC,SAAS;oCACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,QAAQ;oCACvC;gCACF;;kDAEA,8OAAC,sJAAA,CAAA,UAAe;;;;;oCAAG;;;;;;;0CAErB,8OAAC;gCACC,SAAS;oCACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;oCACrC;gCACF;;kDAEA,8OAAC,kJAAA,CAAA,UAAW;;;;;oCAAG;;;;;;;0CAEjB,8OAAC;gCAAG,WAAU;gCAAe,SAAS,IAAM,WAAW,CAAC;;kDACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sJAAA,CAAA,UAAe;;;;;4CAAI;0DACpB,8OAAC;;oDAAK;oDACK;kEACT,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,sTAAA,CAAA,UAAS;wDAAE,KAAI;wDAAY,OAAO;4DAAE,QAAQ,GAAG,UAAU,WAAW,QAAQ;4DAAE,OAAO;4DAAQ,YAAY;wDAAM;;;;;;;;;;;;;;;;;;oCAG9H,yBACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDACC,SAAS;oDACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,iBAAiB;gDACvD;0DACD;;;;;;0DAGD,8OAAC;gDACC,SAAS;oDACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,mBAAmB;gDACzD;0DACD;;;;;;;;;;;;;;;;;;0CAMP,8OAAC;;kDACC,8OAAC,mJAAA,CAAA,UAAgB;;;;;oCAAG;;;;;;;0CAGtB,8OAAC;gCACC,SAAS;oCACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;oCAC9B;gCACF;;kDAEA,8OAAC,kJAAA,CAAA,UAAW;;;;;oCAAG;oCAAE,EAAE;;;;;;;0CAErB,8OAAC;;kDACC,8OAAC,iJAAA,CAAA,UAAU;wCAAC,WAAU;;;;;;oCAAc;;;;;;;;;;;;;;;;;;;YAMzC,wBAAU,8OAAC;gBAAI,WAAU;gBAAU,SAAS;;;;;;;;AAGnD;uCAEe", "debugId": null}}, {"offset": {"line": 3202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3208, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/HamburgerIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction HamburgerIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 640\" fill=\"#333\" width=\"25\" height=\"25\">\n      <path d=\"M96 160C96 142.3 110.3 128 128 128L512 128C529.7 128 544 142.3 544 160C544 177.7 529.7 192 512 192L128 192C110.3 192 96 177.7 96 160zM96 320C96 302.3 110.3 288 128 288L512 288C529.7 288 544 302.3 544 320C544 337.7 529.7 352 512 352L128 352C110.3 352 96 337.7 96 320zM544 480C544 497.7 529.7 512 512 512L128 512C110.3 512 96 497.7 96 480C96 462.3 110.3 448 128 448L512 448C529.7 448 544 462.3 544 480z\" />\n    </svg>\n  );\n}\n\nexport default HamburgerIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,SAAQ;QAAc,MAAK;QAAO,OAAM;QAAK,QAAO;kBAC1F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 3234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3240, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/Header.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useCallback, useEffect, useRef, useState } from \"react\";\n\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { useSelector } from \"react-redux\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { useTranslations } from \"next-intl\";\n\nimport { syncReduxStateToCookies } from \"@/utils/syncReduxToCookies\";\nimport Logo from \"../../../public/assets/images/logo.svg\";\nimport downArrow from \"../../../public/assets/images/down-arrow.svg\";\nimport User from \"../../../public/assets/images/user.png\";\nimport styles from \"@/styles/header.module.scss\";\nimport NotificationIcon from \"../svgComponents/Notification\";\nimport { logout } from \"@/utils/helper\";\nimport { usePathname, useRouter } from \"next/navigation\";\nimport { useDispatch } from \"react-redux\";\nimport { selectProfileData, setPermissions } from \"@/redux/slices/authSlice\";\nimport { getUserPermissions } from \"@/services/authServices\";\n\n// Interface definitions moved to authServices.ts\nimport DataSecurityIcon from \"../svgComponents/dataSecurityIcon\";\nimport ROUTES from \"@/constants/routes\";\nimport { IUserData } from \"@/interfaces/authInterfaces\";\nimport ProfileIcon from \"../svgComponents/ProfileIcon\";\nimport LogoutIcon from \"../svgComponents/LogoutIcon\";\nimport Notifications from \"../views/notification/Notifications\";\nimport { RootState } from \"@/redux/store\";\nimport { getUnreadNotificationsCount } from \"@/services/notificationServices/notificationService\";\nimport { setHasUnreadNotification } from \"@/redux/slices/notificationSlice\";\nimport NavCalendarIcon from \"../svgComponents/NavCalendarIcon\";\nimport NavCandidatesIcon from \"../svgComponents/NavCandidatesIcon\";\nimport NavHomeIcon from \"../svgComponents/NavHomeIcon\";\nimport NavJobsIcon from \"../svgComponents/NavJobsIcon\";\nimport NavSettingsIcon from \"../svgComponents/NavSettingsIcon\";\nimport MobileSidebar from \"./MobileSidebar\";\nimport Button from \"../formElements/Button\";\nimport HamburgerIcon from \"../svgComponents/HamburgerIcon\";\n\nconst Header = () => {\n  const [dropdown, SetDropdown] = useState(false);\n  const userProfile: IUserData | null = useSelector(selectProfileData);\n\n  const [isSidebarOpen, setSidebarOpen] = useState(false);\n  const [subLink, setSubLink] = useState(false);\n\n  const path = usePathname();\n  const dispatch = useDispatch();\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const t = useTranslations(\"header\");\n  const tCommon = useTranslations(\"common\");\n  const pathname = usePathname();\n  const [isNotificationOpen, setIsNotificationOpen] = useState(false);\n  const dropdownRef = useRef<HTMLUListElement>(null);\n  const hasUnreadNotification = useSelector((state: RootState) => state.notification.hasUnreadNotifications);\n\n  const navigate = useRouter();\n\n  // Handle clicks outside of dropdown to close it\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        dropdownRef.current &&\n        !dropdownRef.current.contains(event.target as Node) &&\n        !(event.target instanceof HTMLElement && event.target.id.includes(\"dropdown\"))\n      ) {\n        SetDropdown(false);\n      }\n    };\n\n    // Add event listener when dropdown is open\n    if (dropdown) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n    }\n\n    // Clean up event listener\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [dropdown]);\n\n  // Toggle dropdown visibility\n  const MenuDropdown = () => {\n    SetDropdown(!dropdown);\n  };\n\n  // Function to fetch permissions using the authServices\n  const fetchPermissions = useCallback(async () => {\n    try {\n      const response = await getUserPermissions();\n\n      // Only update Redux store when success is true\n      if (response.data?.success) {\n        dispatch(setPermissions(response.data.data.rolePermissions));\n        // Sync Redux state to cookies after updating permissions\n        syncReduxStateToCookies(response.data.data.rolePermissions, true);\n      } else {\n        console.log(\"Permission fetch unsuccessful:\", response.data?.message);\n      }\n    } catch (error) {\n      console.error(\"Error fetching permissions:\", error);\n    }\n  }, [path, dispatch]);\n\n  const getUserNotificationsUnreadStatus = useCallback(async () => {\n    try {\n      const response = await getUnreadNotificationsCount();\n      if (response.data?.success) {\n        const hasUnreadNotifications = response.data.data.count > 0;\n        dispatch(setHasUnreadNotification(hasUnreadNotifications));\n      } else {\n        console.error(\"Failed to fetch unread notifications status:\", response.data?.message);\n      }\n    } catch (error) {\n      console.error(\"Error fetching unread notifications status:\", error);\n    }\n  }, []);\n\n  // Sync Redux state to cookies after mounting component\n  useEffect(() => {\n    syncReduxStateToCookies();\n  }, []);\n\n  useEffect(() => {\n    // Check if this is first mount or a genuine route change\n    fetchPermissions();\n    getUserNotificationsUnreadStatus();\n  }, [path, dispatch, fetchPermissions]);\n\n  /**\n   * Logs out the user if the access token is invalid.\n   * If the access token is invalid, it logs out the user and shows a toast message.\n   */\n\n  // const logoutUser = async () => {\n  //   const token = getAccessToken();\n  //   if (!token) {\n  //     onHandleLogout();\n  //     toast.dismiss();\n  //     toastMessageError(t(\"session_expired\"));\n  //   }\n  // };\n\n  const onHandleLogout = async () => {\n    await logout(authData?.id);\n\n    if (typeof window !== \"undefined\") {\n      window.location.reload();\n    }\n  };\n\n  const SidebarDropdown = () => {\n    setSidebarOpen(!isSidebarOpen);\n  };\n  return (\n    <>\n      <header\n        className={styles.header}\n        // className={`${styles.header} ${isVisible ? \"\" : `${styles.hidden}`}`}\n      >\n        <nav className=\"navbar navbar-expand-sm\">\n          <div className=\"container\">\n            <div className=\"d-flex align-items-center justify-content-between w-100\">\n              <Link className=\"navbar-brand\" href={ROUTES.HOME}>\n                <Image src={Logo} alt=\"logo\" width={640} height={320} className={styles.logo} />\n              </Link>\n              <Button onClick={SidebarDropdown} className=\"clear-btn p-0 primary hamburger\">\n                <HamburgerIcon />\n              </Button>\n\n              <ul className=\"header_links\">\n                <li className={pathname === ROUTES.DASHBOARD ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.DASHBOARD)}>\n                  <NavHomeIcon /> Home\n                </li>\n                <li\n                  className={pathname === ROUTES.SCREEN_RESUME.CANDIDATES ? \"active\" : \"\"}\n                  onClick={() => navigate.push(ROUTES.SCREEN_RESUME.CANDIDATES)}\n                >\n                  <NavCandidatesIcon /> Candidates\n                </li>\n                <li className={pathname === ROUTES.INTERVIEW.CALENDAR ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.INTERVIEW.CALENDAR)}>\n                  <NavCalendarIcon /> Calendar\n                </li>\n                <li className={pathname === ROUTES.JOBS.ACTIVE_JOBS ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.JOBS.ACTIVE_JOBS)}>\n                  <NavJobsIcon /> Jobs\n                </li>\n                {/* <li className={pathname === ROUTES.PROFILE.MY_PROFILE ? \"active\" : \"\"} onClick={() => navigate.push(ROUTES.PROFILE.MY_PROFILE)}>\n                  <NavSettingsIcon /> Settings\n                </li> */}\n                <span></span>\n              </ul>\n              <div className={styles.header_right}>\n                <div className={styles.user_drop}>\n                  <NotificationIcon\n                    hasNotification={hasUnreadNotification}\n                    id=\"notification-icon-id\"\n                    className={styles.user_drop}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      e.preventDefault();\n                      setIsNotificationOpen((prev) => !prev);\n                    }}\n                  />\n                </div>\n                <div className={`dropdown ${styles.user_drop}`}>\n                  <button\n                    type=\"button\"\n                    className={`dropdown-toggle ${styles.user_drop_btn}`}\n                    data-bs-toggle=\"dropdown\"\n                    onClick={MenuDropdown}\n                    id=\"dropdown-MenuButton\"\n                  >\n                    <div className={`${styles.circle_img}`}>\n                      <Image src={userProfile?.image || User} alt=\"Profile\" width={100} height={100} id=\"dropdown-user-image\" />\n                    </div>\n                    <div className={styles.admin_info}>\n                      <h5 id=\"dropdown-user-name\">{`${userProfile?.first_name}`}</h5>\n                    </div>\n                    <Image src={downArrow} alt=\"downArrow\" style={{ rotate: `${dropdown ? \"180deg\" : \"0deg\"}` }} id=\"dropdown-downArrow\" />\n                  </button>\n\n                  {dropdown && (\n                    <ul className={styles.dropdown_menu} ref={dropdownRef} id=\"dropdown-menu\">\n                      <li>\n                        <ProfileIcon />\n                        <span\n                          onClick={() => {\n                            navigate.push(ROUTES.PROFILE.MY_PROFILE);\n                            SetDropdown(false);\n                          }}\n                        >\n                          {t(\"my_profile\")}\n                        </span>\n                      </li>\n                      <li className={styles.sub_menubar} onClick={() => setSubLink(!subLink)}>\n                        <div className={styles.sub_menu_list}>\n                          <NavSettingsIcon />\n                          <span>\n                            Settings{\" \"}\n                            <Image\n                              src={downArrow}\n                              alt=\"downArrow\"\n                              style={{ rotate: `${subLink ? \"180deg\" : \"0deg\"}`, width: \"13px\", marginLeft: \"5px\" }}\n                            />\n                          </span>\n                        </div>\n                        {subLink && (\n                          <ul className={`${styles.sidebar_sub_menu}`}>\n                            <li\n                              onClick={() => {\n                                navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);\n                                SetDropdown(false);\n                              }}\n                            >\n                              🔒 Roles and Permissions\n                            </li>\n                            <li\n                              onClick={() => {\n                                navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);\n                                SetDropdown(false);\n                              }}\n                            >\n                              🗂️ Employee Management\n                            </li>\n                          </ul>\n                        )}\n                      </li>\n                      <li>\n                        <LogoutIcon className=\"strokeSvg\" />\n                        <span onClick={() => onHandleLogout()}>Logout</span>\n                      </li>\n                    </ul>\n                  )}\n                </div>\n              </div>\n              {/* <div className={`collapse navbar-collapse navbar-desktop justify-content-end ${styles.navbar_content}`} id=\"collapsibleNavbar\"></div> */}\n            </div>\n          </div>\n        </nav>\n      </header>\n      {isNotificationOpen ? <Notifications setIsNotificationOpen={setIsNotificationOpen} /> : null}\n\n      <MobileSidebar isOpen={isSidebarOpen} onClose={() => setSidebarOpen(false)} />\n\n      {/* common pages information box for  Job Requirement Generation page */}\n      {pathname === ROUTES.JOBS.GENERATE_JOB && (\n        <div className=\"information-box\">\n          <DataSecurityIcon />\n          <p>{tCommon(\"data_security_msg\")}</p>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,iDAAiD;AACjD;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,MAAM,SAAS;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAgC,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,mIAAA,CAAA,oBAAiB;IAEnE,MAAM,CAAC,eAAe,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC7C,MAAM,wBAAwB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,YAAY,CAAC,sBAAsB;IAEzG,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEzB,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,CAAC,CAAC,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,GAC7E;gBACA,YAAY;YACd;QACF;QAEA,2CAA2C;QAC3C,IAAI,UAAU;YACZ,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,0BAA0B;QAC1B,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAS;IAEb,6BAA6B;IAC7B,MAAM,eAAe;QACnB,YAAY,CAAC;IACf;IAEA,uDAAuD;IACvD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD;YAExC,+CAA+C;YAC/C,IAAI,SAAS,IAAI,EAAE,SAAS;gBAC1B,SAAS,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe;gBAC1D,yDAAyD;gBACzD,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9D,OAAO;gBACL,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI,EAAE;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,mCAAmC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8JAAA,CAAA,8BAA2B,AAAD;YACjD,IAAI,SAAS,IAAI,EAAE,SAAS;gBAC1B,MAAM,yBAAyB,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;gBAC1D,SAAS,CAAA,GAAA,2IAAA,CAAA,2BAAwB,AAAD,EAAE;YACpC,OAAO;gBACL,QAAQ,KAAK,CAAC,gDAAgD,SAAS,IAAI,EAAE;YAC/E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;QAC/D;IACF,GAAG,EAAE;IAEL,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD;IACxB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD;QACA;IACF,GAAG;QAAC;QAAM;QAAU;KAAiB;IAErC;;;GAGC,GAED,mCAAmC;IACnC,oCAAoC;IACpC,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,+CAA+C;IAC/C,MAAM;IACN,KAAK;IAEL,MAAM,iBAAiB;QACrB,MAAM,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QAEvB,uCAAmC;;QAEnC;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAC;IAClB;IACA,qBACE;;0BACE,8OAAC;gBACC,WAAW,qJAAA,CAAA,UAAM,CAAC,MAAM;0BAGxB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAe,MAAM,0HAAA,CAAA,UAAM,CAAC,IAAI;8CAC9C,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,oSAAA,CAAA,UAAI;wCAAE,KAAI;wCAAO,OAAO;wCAAK,QAAQ;wCAAK,WAAW,qJAAA,CAAA,UAAM,CAAC,IAAI;;;;;;;;;;;8CAE9E,8OAAC,4IAAA,CAAA,UAAM;oCAAC,SAAS;oCAAiB,WAAU;8CAC1C,cAAA,8OAAC,oJAAA,CAAA,UAAa;;;;;;;;;;8CAGhB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAW,aAAa,0HAAA,CAAA,UAAM,CAAC,SAAS,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;;8DACzG,8OAAC,kJAAA,CAAA,UAAW;;;;;gDAAG;;;;;;;sDAEjB,8OAAC;4CACC,WAAW,aAAa,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,UAAU,GAAG,WAAW;4CACrE,SAAS,IAAM,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,UAAU;;8DAE5D,8OAAC,wJAAA,CAAA,UAAiB;;;;;gDAAG;;;;;;;sDAEvB,8OAAC;4CAAG,WAAW,aAAa,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,QAAQ;;8DAC3H,8OAAC,sJAAA,CAAA,UAAe;;;;;gDAAG;;;;;;;sDAErB,8OAAC;4CAAG,WAAW,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW;4CAAI,SAAS,IAAM,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;;8DACvH,8OAAC,kJAAA,CAAA,UAAW;;;;;gDAAG;;;;;;;sDAKjB,8OAAC;;;;;;;;;;;8CAEH,8OAAC;oCAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,YAAY;;sDACjC,8OAAC;4CAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,SAAS;sDAC9B,cAAA,8OAAC,mJAAA,CAAA,UAAgB;gDACf,iBAAiB;gDACjB,IAAG;gDACH,WAAW,qJAAA,CAAA,UAAM,CAAC,SAAS;gDAC3B,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,EAAE,cAAc;oDAChB,sBAAsB,CAAC,OAAS,CAAC;gDACnC;;;;;;;;;;;sDAGJ,8OAAC;4CAAI,WAAW,CAAC,SAAS,EAAE,qJAAA,CAAA,UAAM,CAAC,SAAS,EAAE;;8DAC5C,8OAAC;oDACC,MAAK;oDACL,WAAW,CAAC,gBAAgB,EAAE,qJAAA,CAAA,UAAM,CAAC,aAAa,EAAE;oDACpD,kBAAe;oDACf,SAAS;oDACT,IAAG;;sEAEH,8OAAC;4DAAI,WAAW,GAAG,qJAAA,CAAA,UAAM,CAAC,UAAU,EAAE;sEACpC,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK,aAAa,SAAS,oSAAA,CAAA,UAAI;gEAAE,KAAI;gEAAU,OAAO;gEAAK,QAAQ;gEAAK,IAAG;;;;;;;;;;;sEAEpF,8OAAC;4DAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,UAAU;sEAC/B,cAAA,8OAAC;gEAAG,IAAG;0EAAsB,GAAG,aAAa,YAAY;;;;;;;;;;;sEAE3D,8OAAC,6HAAA,CAAA,UAAK;4DAAC,KAAK,sTAAA,CAAA,UAAS;4DAAE,KAAI;4DAAY,OAAO;gEAAE,QAAQ,GAAG,WAAW,WAAW,QAAQ;4DAAC;4DAAG,IAAG;;;;;;;;;;;;gDAGjG,0BACC,8OAAC;oDAAG,WAAW,qJAAA,CAAA,UAAM,CAAC,aAAa;oDAAE,KAAK;oDAAa,IAAG;;sEACxD,8OAAC;;8EACC,8OAAC,kJAAA,CAAA,UAAW;;;;;8EACZ,8OAAC;oEACC,SAAS;wEACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;wEACvC,YAAY;oEACd;8EAEC,EAAE;;;;;;;;;;;;sEAGP,8OAAC;4DAAG,WAAW,qJAAA,CAAA,UAAM,CAAC,WAAW;4DAAE,SAAS,IAAM,WAAW,CAAC;;8EAC5D,8OAAC;oEAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,aAAa;;sFAClC,8OAAC,sJAAA,CAAA,UAAe;;;;;sFAChB,8OAAC;;gFAAK;gFACK;8FACT,8OAAC,6HAAA,CAAA,UAAK;oFACJ,KAAK,sTAAA,CAAA,UAAS;oFACd,KAAI;oFACJ,OAAO;wFAAE,QAAQ,GAAG,UAAU,WAAW,QAAQ;wFAAE,OAAO;wFAAQ,YAAY;oFAAM;;;;;;;;;;;;;;;;;;gEAIzF,yBACC,8OAAC;oEAAG,WAAW,GAAG,qJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;;sFACzC,8OAAC;4EACC,SAAS;gFACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,iBAAiB;gFACrD,YAAY;4EACd;sFACD;;;;;;sFAGD,8OAAC;4EACC,SAAS;gFACP,SAAS,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,mBAAmB;gFACvD,YAAY;4EACd;sFACD;;;;;;;;;;;;;;;;;;sEAMP,8OAAC;;8EACC,8OAAC,iJAAA,CAAA,UAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEAAK,SAAS,IAAM;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWxD,mCAAqB,8OAAC,4JAAA,CAAA,UAAa;gBAAC,uBAAuB;;;;;uBAA4B;0BAExF,8OAAC,6IAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAe,SAAS,IAAM,eAAe;;;;;;YAGnE,aAAa,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,kBACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uJAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC;kCAAG,QAAQ;;;;;;;;;;;;;;AAKtB;uCAEe", "debugId": null}}, {"offset": {"line": 3825, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3831, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/header/HeaderWrapper.tsx"], "sourcesContent": ["\"use client\";\nimport { usePathname } from \"next/navigation\";\nimport Header from \"@/components/header/Header\";\nimport { BEFORE_LOGIN_ROUTES } from \"@/constants/routes\";\n\nexport default function HeaderWrapper() {\n  const pathname = usePathname();\n\n  console.log(\"pathname\", pathname);\n  if (BEFORE_LOGIN_ROUTES.includes(pathname!)) {\n    return null;\n  }\n\n  return <Header />;\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,QAAQ,GAAG,CAAC,YAAY;IACxB,IAAI,0HAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,WAAY;QAC3C,OAAO;IACT;IAEA,qBAAO,8OAAC,sIAAA,CAAA,UAAM;;;;;AAChB", "debugId": null}}, {"offset": {"line": 3855, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}