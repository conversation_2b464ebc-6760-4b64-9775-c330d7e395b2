{"/candidates-list/[jobId]/page": "app/candidates-list/[jobId]/page.js", "/dashboard/page": "app/dashboard/page.js", "/candidates/page": "app/candidates/page.js", "/hiring-type/page": "app/hiring-type/page.js", "/generate-job/page": "app/generate-job/page.js", "/career-based-skills/page": "app/career-based-skills/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/role-based-skills/page": "app/role-based-skills/page.js", "/culture-based-skills/page": "app/culture-based-skills/page.js", "/page": "app/page.js", "/candidate-profile/[jobApplicationId]/page": "app/candidate-profile/[jobApplicationId]/page.js", "/active-jobs/page": "app/active-jobs/page.js"}