module.exports = {

"[project]/src/constants/jobRequirementConstant.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "APPLICATION_STATUS": (()=>APPLICATION_STATUS),
    "CATEGORY_OPTION": (()=>CATEGORY_OPTION),
    "COMPLIANCE_LINK": (()=>COMPLIANCE_LINK),
    "COMPLIANCE_OPTIONS": (()=>COMPLIANCE_OPTIONS),
    "CURRENCY_SYMBOL": (()=>CURRENCY_SYMBOL),
    "CURSOR_POINT": (()=>CURSOR_POINT),
    "DEPARTMENT_OPTION": (()=>DEPARTMENT_OPTION),
    "EXPERIENCE_LEVEL_OPTIONS": (()=>EXPERIENCE_LEVEL_OPTIONS),
    "FILE_NAME": (()=>FILE_NAME),
    "FILE_SIZE_LIMIT": (()=>FILE_SIZE_LIMIT),
    "FILE_TYPE": (()=>FILE_TYPE),
    "HIRING_TYPE": (()=>HIRING_TYPE),
    "HIRING_TYPE_KEY": (()=>HIRING_TYPE_KEY),
    "JOB_GENERATION_UPLOAD_MESSAGES": (()=>JOB_GENERATION_UPLOAD_MESSAGES),
    "LOCATION_TYPE_OPTIONS": (()=>LOCATION_TYPE_OPTIONS),
    "MAX_FILE_SIZE": (()=>MAX_FILE_SIZE),
    "SALARY_CYCLE_OPTIONS": (()=>SALARY_CYCLE_OPTIONS),
    "SALARY_REMOVE_SYMBOL_REGEX": (()=>SALARY_REMOVE_SYMBOL_REGEX),
    "SKILL_CATEGORY": (()=>SKILL_CATEGORY),
    "SKILL_TYPE": (()=>SKILL_TYPE),
    "SUN_EDITOR_BUTTON_LIST": (()=>SUN_EDITOR_BUTTON_LIST),
    "TONE_STYLE_OPTIONS": (()=>TONE_STYLE_OPTIONS)
});
const CATEGORY_OPTION = [
    {
        label: "Full time",
        value: "full_time"
    },
    {
        label: "Part time",
        value: "part_time"
    },
    {
        label: "Contract",
        value: "contract"
    },
    {
        label: "Internship",
        value: "internship"
    },
    {
        label: "Freelance",
        value: "freelance"
    }
];
const SALARY_CYCLE_OPTIONS = [
    {
        label: "Per Hour",
        value: "per hour"
    },
    {
        label: "Per Month",
        value: "per month"
    },
    {
        label: "Per Annum",
        value: "per annum"
    }
];
const LOCATION_TYPE_OPTIONS = [
    {
        label: "Remote",
        value: "remote"
    },
    {
        label: "Hybrid",
        value: "hybrid"
    },
    {
        label: "On-site",
        value: "onsite"
    }
];
const TONE_STYLE_OPTIONS = [
    {
        label: "Professional & Formal",
        value: "Professional_Formal"
    },
    {
        label: "Conversational & Approachable",
        value: "Conversational_Approachable"
    },
    {
        label: "Bold & Energetic",
        value: "Bold_Energetic"
    },
    {
        label: "Inspirational & Mission-Driven",
        value: "Inspirational_Mission-Driven"
    },
    {
        label: "Technical & Precise",
        value: "Technical_Precise"
    },
    {
        label: "Creative & Fun",
        value: "Creative_Fun"
    },
    {
        label: "Inclusive & Human-Centered",
        value: "Inclusive_Human-Centered"
    },
    {
        label: "Minimalist & Straightforward",
        value: "Minimalist_Straightforward"
    }
];
const COMPLIANCE_OPTIONS = [
    {
        label: "Equal Employment Opportunity (EEO) Statement",
        value: "Equal Employment Opportunity (EEO) Statement"
    },
    {
        label: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)",
        value: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"
    },
    {
        label: "Disability Accommodation Statement",
        value: "Disability Accommodation Statement"
    },
    {
        label: "Veterans Preference Statement (For Government Agencies and Federal Contractors)",
        value: "Veterans Preference Statement (For Government Agencies and Federal Contractors)"
    },
    {
        label: "Diversity & Inclusion Commitment",
        value: "Diversity & Inclusion Commitment"
    },
    {
        label: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)",
        value: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)"
    },
    {
        label: "Background Check and Drug-Free Workplace Policy (If Applicable)",
        value: "Background Check and Drug-Free Workplace Policy (If Applicable)"
    },
    {
        label: "Work Authorization & Immigration Statement",
        value: "Work Authorization & Immigration Statement"
    }
];
const EXPERIENCE_LEVEL_OPTIONS = [
    {
        label: "General",
        value: "General"
    },
    {
        label: "No experience necessary",
        value: "No experience necessary"
    },
    {
        label: "Entry-Level Position",
        value: "Entry-Level Position"
    },
    {
        label: "Mid-Level Professional",
        value: "Mid-Level Professional"
    },
    {
        label: "Senior/Experienced Professional",
        value: "Senior/Experienced Professional"
    },
    {
        label: "Managerial/Executive Level",
        value: "Managerial/Executive Level"
    },
    {
        label: "Specialized Expert",
        value: "Specialized Expert"
    }
];
const DEPARTMENT_OPTION = [
    {
        label: "IT",
        value: "IT"
    },
    {
        label: "HR",
        value: "HR"
    },
    {
        label: "Marketing",
        value: "Marketing"
    },
    {
        label: "Finance",
        value: "Finance"
    },
    {
        label: "Sales",
        value: "Sales"
    }
];
const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const FILE_TYPE = "application/pdf";
const FILE_NAME = ".pdf";
const SALARY_REMOVE_SYMBOL_REGEX = /[\$\s]/g;
const CURRENCY_SYMBOL = "$";
const SUN_EDITOR_BUTTON_LIST = [
    [
        "font",
        "fontSize",
        "formatBlock"
    ],
    [
        "bold",
        "underline",
        "italic"
    ],
    [
        "fontColor",
        "hiliteColor"
    ],
    [
        "align",
        "list",
        "lineHeight"
    ]
];
const HIRING_TYPE = {
    INTERNAL: "internal",
    EXTERNAL: "external"
};
const SKILL_CATEGORY = {
    Personal_Health: "Personal Health",
    Social_Interaction: "Social Interaction",
    Mastery_Of_Emotions: "Mastery of Emotions",
    Mentality: "Mentality",
    Cognitive_Abilities: "Cognitive Abilities"
};
const APPLICATION_STATUS = {
    PENDING: "Pending",
    APPROVED: "Approved",
    REJECTED: "Rejected",
    HIRED: "Hired",
    ON_HOLD: "On-Hold",
    FINAL_REJECT: "Final-Reject"
};
const SKILL_TYPE = {
    ROLE: "role",
    CULTURE: "culture"
};
const HIRING_TYPE_KEY = "hiringType";
const CURSOR_POINT = {
    cursor: "pointer"
};
const COMPLIANCE_LINK = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";
const JOB_GENERATION_UPLOAD_MESSAGES = [
    "Analyzing your job description...",
    "Extracting key requirements...",
    "Processing document content...",
    "Identifying skills and qualifications...",
    "Parsing job details...",
    "Almost ready..."
];
}}),
"[project]/src/components/svgComponents/BackArrowIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function BackArrowIcon({ onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "cursor-pointer me-3",
        width: "26",
        height: "26",
        viewBox: "0 0 32 32",
        fill: "none",
        onClick: onClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = BackArrowIcon;
}}),
"[project]/src/services/screenResumeServices.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "changeApplicationStatus": (()=>changeApplicationStatus),
    "getAllPendingJobApplications": (()=>getAllPendingJobApplications),
    "getPresignedUrl": (()=>getPresignedUrl),
    "processFileUpload": (()=>processFileUpload),
    "uploadManualCandidate": (()=>uploadManualCandidate),
    "uploadToS3": (()=>uploadToS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
;
;
const getPresignedUrl = async (file)=>{
    const formData = new FormData();
    formData.append("file", file);
    formData.append("fileType", file.type);
    formData.append("fileName", file.name);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].resumeScreen.GET_PRESIGNED_URL, formData, {
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
};
const uploadToS3 = async (presignedUrl, file)=>{
    return fetch(presignedUrl, {
        method: "PUT",
        body: file,
        headers: {
            "Content-Type": file.type
        }
    });
};
const processFileUpload = async (file)=>{
    try {
        // Get presigned URL
        const presignedUrlResponse = await getPresignedUrl(file);
        if (!presignedUrlResponse.data) {
            throw new Error("Failed to get presigned URL");
        }
        const responseData = presignedUrlResponse.data;
        // The response might have data nested inside another data property
        const urlData = responseData.data;
        if (!urlData.presignedUrl || !urlData.fileUrl) {
            console.error("Missing URL information in response:", urlData);
            throw new Error("Missing URL information in response");
        }
        const { presignedUrl, fileUrl, fileText } = urlData;
        // Upload file to S3
        const uploadResponse = await uploadToS3(presignedUrl, file);
        if (!uploadResponse.ok) {
            throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);
        }
        // Return the file URL and flag for backend extraction
        return {
            fileUrl,
            fileText: fileText,
            presignedUrl
        };
    } catch (error) {
        console.error("Error processing file upload:", error);
        // Include error details in the console for debugging
        if (error instanceof Error) {
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
        }
        throw error;
    }
};
const getAllPendingJobApplications = async (params)=>{
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        if (params.limit) queryParams.append("limit", params.limit.toString());
        // Always include offset parameter, even when it's 0
        queryParams.append("offset", params.offset !== undefined ? params.offset.toString() : "0");
        if (params.job_id) queryParams.append("job_id", params.job_id.toString());
        if (params.status) queryParams.append("status", params.status);
        // Make API request
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].get(url);
    } catch (error) {
        console.error("Error fetching job applications:", error);
        throw error;
    }
};
const uploadManualCandidate = async (uploadManualCandidateData)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].resumeScreen.MANUAL_CANDIDATE_UPLOAD, uploadManualCandidateData);
};
const changeApplicationStatus = async (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].resumeScreen.CHANGE_APPLICATION_STATUS, data);
};
}}),
"[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "commonPage-module-scss-module__em0r7a__active",
  "add_another_candidate_link": "commonPage-module-scss-module__em0r7a__add_another_candidate_link",
  "approved_status_indicator": "commonPage-module-scss-module__em0r7a__approved_status_indicator",
  "border_none": "commonPage-module-scss-module__em0r7a__border_none",
  "candidate_card": "commonPage-module-scss-module__em0r7a__candidate_card",
  "candidate_card_header": "commonPage-module-scss-module__em0r7a__candidate_card_header",
  "candidate_qualification_page": "commonPage-module-scss-module__em0r7a__candidate_qualification_page",
  "candidates_list_page": "commonPage-module-scss-module__em0r7a__candidates_list_page",
  "candidates_list_section": "commonPage-module-scss-module__em0r7a__candidates_list_section",
  "career-skill-card": "commonPage-module-scss-module__em0r7a__career-skill-card",
  "dashboard__stat": "commonPage-module-scss-module__em0r7a__dashboard__stat",
  "dashboard__stat_design": "commonPage-module-scss-module__em0r7a__dashboard__stat_design",
  "dashboard__stat_image": "commonPage-module-scss-module__em0r7a__dashboard__stat_image",
  "dashboard__stat_label": "commonPage-module-scss-module__em0r7a__dashboard__stat_label",
  "dashboard__stat_value": "commonPage-module-scss-module__em0r7a__dashboard__stat_value",
  "dashboard__stats": "commonPage-module-scss-module__em0r7a__dashboard__stats",
  "dashboard__stats_header": "commonPage-module-scss-module__em0r7a__dashboard__stats_header",
  "dashboard_inner_head": "commonPage-module-scss-module__em0r7a__dashboard_inner_head",
  "dashboard_page": "commonPage-module-scss-module__em0r7a__dashboard_page",
  "header_tab": "commonPage-module-scss-module__em0r7a__header_tab",
  "inner_heading": "commonPage-module-scss-module__em0r7a__inner_heading",
  "inner_page": "commonPage-module-scss-module__em0r7a__inner_page",
  "input_type_file": "commonPage-module-scss-module__em0r7a__input_type_file",
  "interview_form_icon": "commonPage-module-scss-module__em0r7a__interview_form_icon",
  "job_info": "commonPage-module-scss-module__em0r7a__job_info",
  "job_page": "commonPage-module-scss-module__em0r7a__job_page",
  "manual_upload_resume": "commonPage-module-scss-module__em0r7a__manual_upload_resume",
  "operation_admins_img": "commonPage-module-scss-module__em0r7a__operation_admins_img",
  "resume_page": "commonPage-module-scss-module__em0r7a__resume_page",
  "search_box": "commonPage-module-scss-module__em0r7a__search_box",
  "section_heading": "commonPage-module-scss-module__em0r7a__section_heading",
  "section_name": "commonPage-module-scss-module__em0r7a__section_name",
  "selected": "commonPage-module-scss-module__em0r7a__selected",
  "selecting": "commonPage-module-scss-module__em0r7a__selecting",
  "selection": "commonPage-module-scss-module__em0r7a__selection",
  "skills_info_box": "commonPage-module-scss-module__em0r7a__skills_info_box",
  "skills_tab": "commonPage-module-scss-module__em0r7a__skills_tab",
  "text_xs": "commonPage-module-scss-module__em0r7a__text_xs",
  "upload_resume_page": "commonPage-module-scss-module__em0r7a__upload_resume_page",
});
}}),
"[project]/src/components/formElements/InputWrapper.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
;
;
/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */ const InputWrapper = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `form-group ${className ?? ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 10,
        columnNumber: 3
    }, this);
/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */ InputWrapper.Label = function({ children, htmlFor, required, className, onClick, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
        htmlFor: htmlFor,
        className: className,
        onClick: onClick,
        style: style,
        children: [
            children,
            required ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("sup", {
                children: "*"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/InputWrapper.tsx",
                lineNumber: 37,
                columnNumber: 19
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */ InputWrapper.Error = function({ message, style }) {
    return message ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        className: "auth-msg error",
        style: style,
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this) : null;
};
/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */ InputWrapper.Icon = function({ children, // src,
onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        className: "show-icon",
        type: "button",
        onClick: onClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InputWrapper;
}}),
"[project]/src/components/formElements/Textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
;
;
function Textarea({ control, name, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Controller"], {
        control: control,
        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                ...props,
                value: field.value,
                onChange: field.onChange,
                "aria-label": ""
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textarea.tsx",
                lineNumber: 13,
                columnNumber: 30
            }, void 0),
        name: name,
        defaultValue: ""
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/Textarea.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/svgComponents/GreenCheckIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function GreenCheckIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        className: className,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9972 7.34869 18.9427 4.80678 17.068 2.93202C15.1932 1.05727 12.6513 0.00279983 10 0ZM14.3904 8.23654L9.00577 13.6212C8.93433 13.6927 8.84949 13.7494 8.75611 13.7881C8.66273 13.8268 8.56263 13.8468 8.46154 13.8468C8.36045 13.8468 8.26035 13.8268 8.16697 13.7881C8.07359 13.7494 7.98875 13.6927 7.91731 13.6212L5.60962 11.3135C5.46528 11.1691 5.38419 10.9734 5.38419 10.7692C5.38419 10.5651 5.46528 10.3693 5.60962 10.225C5.75396 10.0807 5.94972 9.99957 6.15385 9.99957C6.35798 9.99957 6.55374 10.0807 6.69808 10.225L8.46154 11.9894L13.3019 7.14808C13.3734 7.07661 13.4582 7.01991 13.5516 6.98123C13.645 6.94256 13.7451 6.92265 13.8462 6.92265C13.9472 6.92265 14.0473 6.94256 14.1407 6.98123C14.2341 7.01991 14.3189 7.07661 14.3904 7.14808C14.4619 7.21954 14.5185 7.30439 14.5572 7.39777C14.5959 7.49115 14.6158 7.59123 14.6158 7.69231C14.6158 7.79338 14.5959 7.89346 14.5572 7.98684C14.5185 8.08022 14.4619 8.16507 14.3904 8.23654Z",
            fill: "#007733"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/GreenCheckIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/GreenCheckIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = GreenCheckIcon;
}}),
"[project]/src/components/svgComponents/RoundCrossIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function RoundCrossIcon({ DangerColor }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "25",
        height: "24",
        viewBox: "0 0 25 24",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "12.3331",
                cy: "11.9996",
                r: "10.9254",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RoundCrossIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12.3325 1C10.1569 1 8.03019 1.64514 6.22125 2.85383C4.41231 4.06253 3.00241 5.7805 2.16985 7.79048C1.33729 9.80047 1.11945 12.0122 1.54389 14.146C1.96832 16.2798 3.01597 18.2398 4.55435 19.7782C6.09273 21.3166 8.05274 22.3642 10.1865 22.7886C12.3203 23.2131 14.5321 22.9952 16.542 22.1627C18.552 21.3301 20.27 19.9202 21.4787 18.1113C22.6874 16.3023 23.3325 14.1756 23.3325 12C23.3291 9.08368 22.1691 6.28778 20.1069 4.22563C18.0447 2.16347 15.2489 1.00344 12.3325 1ZM16.5745 14.829C16.67 14.9212 16.7462 15.0316 16.7986 15.1536C16.851 15.2756 16.8786 15.4068 16.8798 15.5396C16.8809 15.6724 16.8556 15.8041 16.8053 15.927C16.7551 16.0499 16.6808 16.1615 16.5869 16.2554C16.493 16.3493 16.3814 16.4235 16.2585 16.4738C16.1356 16.5241 16.0039 16.5494 15.8711 16.5483C15.7383 16.5471 15.6071 16.5195 15.4851 16.4671C15.3631 16.4147 15.2528 16.3385 15.1605 16.243L12.3325 13.414L9.50453 16.243C9.31592 16.4252 9.06332 16.526 8.80112 16.5237C8.53893 16.5214 8.28812 16.4162 8.10271 16.2308C7.9173 16.0454 7.81213 15.7946 7.80985 15.5324C7.80757 15.2702 7.90837 15.0176 8.09053 14.829L10.9185 12L8.09053 9.171C7.99501 9.07875 7.91883 8.96841 7.86642 8.84641C7.81401 8.7244 7.78643 8.59318 7.78527 8.4604C7.78412 8.32762 7.80942 8.19594 7.8597 8.07305C7.90998 7.95015 7.98424 7.8385 8.07813 7.74461C8.17202 7.65071 8.28367 7.57646 8.40657 7.52618C8.52947 7.4759 8.66115 7.4506 8.79393 7.45175C8.92671 7.4529 9.05793 7.48049 9.17993 7.5329C9.30193 7.58531 9.41228 7.66149 9.50453 7.757L12.3325 10.586L15.1605 7.757C15.2528 7.66149 15.3631 7.58531 15.4851 7.5329C15.6071 7.48049 15.7383 7.4529 15.8711 7.45175C16.0039 7.4506 16.1356 7.4759 16.2585 7.52618C16.3814 7.57646 16.493 7.65071 16.5869 7.74461C16.6808 7.8385 16.7551 7.95015 16.8053 8.07305C16.8556 8.19594 16.8809 8.32762 16.8798 8.4604C16.8786 8.59318 16.851 8.7244 16.7986 8.84641C16.7462 8.96841 16.67 9.07875 16.5745 9.171L13.7465 12L16.5745 14.829Z",
                fill: DangerColor ? "#d00000" : "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RoundCrossIcon.tsx",
                lineNumber: 7,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/RoundCrossIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = RoundCrossIcon;
}}),
"[project]/public/assets/images/rejected.json (json)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"v\":\"4.8.0\",\"ip\":0,\"op\":90,\"fr\":30,\"w\":1080,\"h\":1080,\"nm\":\"R\",\"assets\":[],\"layers\":[{\"ind\":1,\"nm\":\"L\",\"sr\":1,\"ks\":{\"o\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.833],\"y\":[0.833]},\"o\":{\"x\":[0.167],\"y\":[0.167]},\"t\":0,\"s\":[0]},{\"t\":10,\"s\":[100]}]},\"r\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.667],\"y\":[1]},\"o\":{\"x\":[0.333],\"y\":[0]},\"t\":2,\"s\":[-15]},{\"i\":{\"x\":[0.667],\"y\":[1]},\"o\":{\"x\":[0.333],\"y\":[0]},\"t\":12,\"s\":[4]},{\"i\":{\"x\":[0.667],\"y\":[1]},\"o\":{\"x\":[0.333],\"y\":[0]},\"t\":17,\"s\":[-3]},{\"i\":{\"x\":[0.667],\"y\":[1]},\"o\":{\"x\":[0.333],\"y\":[0]},\"t\":21,\"s\":[2]},{\"t\":25,\"s\":[0]}]},\"p\":{\"a\":0,\"k\":[539.619,539.702,0]},\"a\":{\"a\":0,\"k\":[166.119,148.202,0]},\"s\":{\"k\":[{\"i\":{\"x\":[0.667,0.667,0.667],\"y\":[1,1,1]},\"o\":{\"x\":[0.333,0.333,0.333],\"y\":[0,0,0]},\"t\":0,\"s\":[600,600,100]},{\"i\":{\"x\":[0.833,0.833,0.833],\"y\":[1,1,1]},\"o\":{\"x\":[0.167,0.167,0.167],\"y\":[0,0,0]},\"t\":10,\"s\":[240,240,100]},{\"i\":{\"x\":[0.833,0.833,0.833],\"y\":[1,1,1]},\"o\":{\"x\":[0.167,0.167,0.167],\"y\":[0,0,0]},\"t\":15,\"s\":[232,232,100]},{\"i\":{\"x\":[0.833,0.833,0.833],\"y\":[1,1,1]},\"o\":{\"x\":[0.167,0.167,0.167],\"y\":[0,0,0]},\"t\":19,\"s\":[237,237,100]},{\"t\":23,\"s\":[235,235,100]}],\"a\":1}},\"shapes\":[{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[0.585,1.608],[0,0],[1.727,-0.628],[0,0],[0,0]],\"o\":[[1.728,-0.629],[0,0],[-0.585,-1.608],[0,0],[0,0],[0,0]],\"v\":[[8.147,14.739],[9.937,11.389],[0.075,-15.707],[-3.471,-17.183],[-9.585,-14.957],[2.034,16.965]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":1,\"ks\":{\"k\":{\"i\":[[0,0],[-0.73,0.266],[0,0],[-2.038,-5.599],[0,0],[6.246,-2.273],[0,0],[0.238,0.655]],\"o\":[[-0.217,-0.595],[0,0],[6.246,-2.274],[0,0],[2.038,5.598],[0,0],[-0.731,0.266],[0,0]],\"v\":[[-21.137,-18.577],[-20.42,-19.916],[-4.472,-25.72],[8.239,-20.567],[19.315,9.866],[12.868,21.923],[-3.079,27.728],[-4.488,27.163]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[278.867,107.29]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[0.797,-0.29],[0,0],[0,0],[0,0],[-0.238,-0.655],[0,0],[0.665,-0.242],[0,0],[0,0],[0,0],[-0.238,-0.655],[0,0],[0.797,-0.29],[0,0],[0.217,0.596],[0,0],[-0.598,0.217],[0,0],[-0.216,-0.595]],\"o\":[[0.217,0.596],[0,0],[0,0],[0,0],[0.665,-0.242],[0,0],[0.238,0.655],[0,0],[0,0],[0,0],[0.798,-0.29],[0,0],[0.217,0.596],[0,0],[-0.598,0.218],[0,0],[-0.217,-0.595],[0,0],[0.798,-0.29],[0,0]],\"v\":[[7.1,-21.715],[6.406,-20.315],[-10.207,-14.268],[-5.871,-2.357],[6.82,-6.977],[8.252,-6.352],[10.398,-0.456],[9.703,0.943],[-2.988,5.562],[1.39,17.594],[18.002,11.547],[19.412,12.112],[21.58,18.068],[20.864,19.409],[-3.589,28.308],[-4.933,27.719],[-21.58,-18.021],[-20.931,-19.336],[3.522,-28.237],[4.932,-27.671]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[246.818,118.56]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[-0.217,-0.595],[0,0],[0.731,-0.266],[0,0],[0,0],[0.731,-0.266],[0,0],[0.239,0.654],[0,0],[0,0],[0.216,0.595],[0,0],[-0.664,0.242]],\"o\":[[0.731,-0.266],[0,0],[0.216,0.595],[0,0],[0,0],[0.239,0.655],[0,0],[-0.664,0.242],[0,0],[0,0],[-0.664,0.242],[0,0],[-0.217,-0.596],[0,0]],\"v\":[[7.931,-26.664],[9.341,-26.098],[11.508,-20.142],[10.792,-18.802],[3.084,-15.997],[17.216,22.833],[16.501,24.173],[9.59,26.688],[8.18,26.123],[-5.953,-12.708],[-13.661,-9.902],[-15.07,-10.468],[-17.238,-16.424],[-16.522,-17.764]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[211.246,128.331]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[0.664,-0.242],[0,0],[0.217,0.596],[0,0],[1.727,-0.629],[0,0],[-0.563,-1.548],[0,0],[-1.728,0.628],[0,0],[0.585,1.608],[0,0],[-0.731,0.266],[0,0],[-0.217,-0.595],[0,0],[6.18,-2.249],[0,0],[2.037,5.598],[0,0],[-6.247,2.274],[0,0],[-2.038,-5.598]],\"o\":[[0.217,0.595],[0,0],[-0.731,0.267],[0,0],[-0.564,-1.549],[0,0],[-1.794,0.653],[0,0],[0.585,1.608],[0,0],[1.728,-0.629],[0,0],[-0.217,-0.596],[0,0],[0.665,-0.242],[0,0],[2.038,5.599],[0,0],[-6.247,2.273],[0,0],[-2.038,-5.598],[0,0],[6.179,-2.249],[0,0]],\"v\":[[10.115,-13.663],[9.399,-12.323],[2.754,-9.905],[1.343,-10.471],[-0.39,-15.235],[-3.915,-16.65],[-7.371,-15.392],[-9.162,-12.043],[0.723,15.115],[4.248,16.53],[7.703,15.272],[9.494,11.922],[7.76,7.158],[8.476,5.818],[15.121,3.399],[16.531,3.965],[18.872,10.397],[12.425,22.456],[4.983,25.165],[-7.773,20.096],[-18.872,-10.397],[-12.358,-22.48],[-4.916,-25.189],[7.774,-20.095]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[185.686,140.834]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[0.797,-0.29],[0,0],[0,0],[0,0],[-0.238,-0.655],[0,0],[0.664,-0.242],[0,0],[0,0],[0,0],[-0.239,-0.655],[0,0],[0.797,-0.29],[0,0],[0.216,0.596],[0,0],[-0.598,0.217],[0,0],[-0.217,-0.595]],\"o\":[[0.217,0.595],[0,0],[0,0],[0,0],[0.664,-0.241],[0,0],[0.239,0.655],[0,0],[0,0],[0,0],[0.797,-0.29],[0,0],[0.217,0.595],[0,0],[-0.599,0.218],[0,0],[-0.217,-0.595],[0,0],[0.797,-0.29],[0,0]],\"v\":[[7.101,-21.715],[6.406,-20.315],[-10.206,-14.268],[-5.871,-2.357],[6.821,-6.977],[8.251,-6.352],[10.398,-0.456],[9.703,0.943],[-2.989,5.563],[1.391,17.594],[18.003,11.547],[19.412,12.112],[21.581,18.068],[20.864,19.409],[-3.589,28.308],[-4.932,27.719],[-21.58,-18.021],[-20.93,-19.336],[3.523,-28.237],[4.932,-27.671]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[153.126,152.661]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[-0.732,0.266],[0,0],[-0.217,-0.596],[0,0],[6.179,-2.249],[0,0],[2.037,5.598],[0,0],[-0.731,0.266],[0,0],[-0.217,-0.595],[0,0],[-1.728,0.629],[0,0],[0.563,1.549]],\"o\":[[-0.217,-0.595],[0,0],[0.665,-0.242],[0,0],[2.038,5.598],[0,0],[-6.246,2.273],[0,0],[-0.217,-0.596],[0,0],[0.665,-0.242],[0,0],[0.563,1.548],[0,0],[1.728,-0.629],[0,0]],\"v\":[[-9.403,-23.885],[-8.687,-25.225],[-1.776,-27.74],[-0.366,-27.174],[13.506,10.942],[7.059,23],[-0.382,25.709],[-13.073,20.616],[-15.328,14.422],[-14.61,13.082],[-7.967,10.663],[-6.556,11.229],[-4.908,15.756],[-1.384,17.17],[2.072,15.913],[3.863,12.563]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[125.267,164.233]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[0.797,-0.29],[0,0],[0,0],[0,0],[-0.238,-0.654],[0,0],[0.665,-0.242],[0,0],[0,0],[0,0],[-0.238,-0.655],[0,0],[0.797,-0.291],[0,0],[0.217,0.595],[0,0],[-0.598,0.218],[0,0],[-0.217,-0.595]],\"o\":[[0.217,0.595],[0,0],[0,0],[0,0],[0.665,-0.242],[0,0],[0.238,0.655],[0,0],[0,0],[0,0],[0.797,-0.29],[0,0],[0.217,0.595],[0,0],[-0.598,0.217],[0,0],[-0.217,-0.596],[0,0],[0.797,-0.29],[0,0]],\"v\":[[7.1,-21.714],[6.406,-20.315],[-10.206,-14.269],[-5.871,-2.357],[6.82,-6.977],[8.252,-6.352],[10.398,-0.455],[9.703,0.944],[-2.988,5.563],[1.39,17.593],[18.003,11.547],[19.412,12.113],[21.58,18.069],[20.864,19.409],[-3.589,28.309],[-4.933,27.719],[-21.58,-18.02],[-20.931,-19.336],[3.523,-28.236],[4.933,-27.67]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[86.745,176.822]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[0.563,1.549],[0,0],[1.727,-0.629],[0,0],[0,0]],\"o\":[[1.728,-0.629],[0,0],[-0.564,-1.548],[0,0],[0,0],[0,0]],\"v\":[[1.994,-1.245],[3.784,-4.595],[-0.291,-15.792],[-3.816,-17.206],[-10.395,-14.812],[-4.584,1.149]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":1,\"ks\":{\"k\":{\"i\":[[0,0],[2.735,-2.411],[0,0],[0.73,-0.266],[0,0],[0.328,0.488],[0,0],[0,0],[0,0],[0.665,-0.242],[0,0],[0.217,0.595],[0,0],[-0.664,0.242],[0,0],[-2.038,-5.598]],\"o\":[[1.452,3.99],[0,0],[0.46,0.642],[0,0],[-0.665,0.242],[0,0],[0,0],[0,0],[0.217,0.595],[0,0],[-0.665,0.241],[0,0],[-0.217,-0.595],[0,0],[6.246,-2.274],[0,0]],\"v\":[[13.428,-6.216],[11.438,3.41],[21.703,17.345],[21.163,18.756],[14.252,21.271],[12.819,20.848],[2.861,7.341],[-1.724,9.01],[3.739,24.019],[3.022,25.358],[-3.888,27.874],[-5.299,27.308],[-21.946,-18.431],[-21.229,-19.771],[-4.551,-25.841],[8.14,-20.748]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[53.156,189.591]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[3.963,-1.443],[0,0],[1.443,3.963],[0,0],[-3.963,1.442],[0,0],[-1.442,-3.963]],\"o\":[[1.442,3.963],[0,0],[-3.963,1.443],[0,0],[-1.442,-3.963],[0,0],[3.963,-1.443],[0,0]],\"v\":[[152.188,-33.081],[147.617,-23.277],[-128.043,77.054],[-137.847,72.483],[-152.188,33.08],[-147.617,23.277],[128.043,-77.055],[137.846,-72.483]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":1,\"ks\":{\"k\":{\"i\":[[5.153,-1.876],[0,0],[-1.875,-5.152],[0,0],[-5.151,1.875],[0,0],[1.875,5.152],[0,0]],\"o\":[[0,0],[-5.152,1.875],[0,0],[1.875,5.152],[0,0],[5.151,-1.876],[0,0],[-1.876,-5.152]],\"v\":[[127.258,-79.21],[-148.401,21.121],[-154.344,33.866],[-140.003,73.268],[-127.259,79.212],[148.402,-21.121],[154.344,-33.866],[140.003,-73.269]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[166.119,148.066]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]},{\"ty\":\"gr\",\"nm\":\"G\",\"bm\":0,\"it\":[{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":0,\"ks\":{\"k\":{\"i\":[[0,0],[4.954,-1.803],[0,0],[1.803,4.954],[0,0],[-4.954,1.803],[0,0],[-1.803,-4.954]],\"o\":[[1.803,4.954],[0,0],[-4.954,1.803],[0,0],[-1.803,-4.954],[0,0],[4.954,-1.803],[0,0]],\"v\":[[159.751,-31.168],[154.037,-18.914],[-130.069,84.492],[-142.323,78.778],[-159.751,30.895],[-154.037,18.641],[130.069,-84.765],[142.323,-79.051]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":1,\"ks\":{\"k\":{\"i\":[[0,0],[-20.11,7.319],[-5.689,19.043],[0,0],[42.999,-15.636],[28.48,32.481]],\"o\":[[16.598,10.931],[20.111,-7.32],[0,0],[-0.938,43.211],[-42.977,15.629],[0,0]],\"v\":[[-35.801,54.251],[22.281,61.082],[62.385,18.514],[107.161,2.217],[36.185,101.368],[-81.861,71.016]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":2,\"ks\":{\"k\":{\"i\":[[50.779,-18.466],[31.889,40.035],[0,0],[-1.325,-1.425],[-13.428,-6.267],[-14.735,-0.759],[-14.475,5.264],[-11.19,10.402],[-6.266,13.429],[-0.758,14.735],[0,1.959],[0,0]],\"o\":[[-50.757,18.458],[0,0],[1.245,1.484],[10.046,10.807],[13.429,6.266],[15.256,0.783],[14.476,-5.264],[10.806,-10.045],[6.267,-13.428],[0.101,-1.965],[0,0],[1.306,51.194]],\"v\":[[41.182,115.106],[-96.493,76.341],[-88.161,73.309],[-84.307,77.675],[-48.931,103.405],[-6.487,113.991],[38.32,107.239],[76.999,83.63],[102.727,48.254],[113.313,5.811],[113.461,-0.076],[121.794,-3.109]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":3,\"ks\":{\"k\":{\"i\":[[0,0],[20.046,-7.296],[5.727,-18.962],[0,0],[-43.072,15.663],[-28.467,-32.602]],\"o\":[[-16.576,-10.846],[-20.045,7.296],[0,0],[0.85,-43.295],[43.051,-15.655],[0,0]],\"v\":[[35.692,-54.484],[-22.229,-61.209],[-62.277,-18.827],[-108.522,-1.995],[-37.539,-101.368],[80.655,-70.85]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":4,\"ks\":{\"k\":{\"i\":[[-50.851,18.492],[-31.869,-40.157],[0,0],[1.375,1.48],[13.428,6.266],[14.734,0.758],[14.476,-5.265],[11.189,-10.401],[6.267,-13.428],[0.758,-14.735],[-0.004,-2.032],[0,0]],\"o\":[[50.828,-18.484],[0,0],[-1.289,-1.542],[-10.045,-10.806],[-13.429,-6.267],[-15.257,-0.784],[-14.476,5.264],[-10.807,10.045],[-6.266,13.429],[-0.105,2.039],[0,0],[-1.399,-51.273]],\"v\":[[-42.535,-115.105],[95.277,-76.172],[86.947,-73.14],[82.953,-77.675],[47.577,-103.404],[5.134,-113.99],[-39.674,-107.238],[-78.352,-83.63],[-104.082,-48.254],[-114.667,-5.81],[-114.815,0.296],[-123.145,3.327]],\"c\":true},\"a\":0}},{\"ty\":\"sh\",\"nm\":\"P\",\"ind\":5,\"ks\":{\"k\":{\"i\":[[6.935,-2.524],[0,0],[0.657,0.121],[0.982,10.39],[10.207,-2.181],[4.884,9.224],[8.593,-5.922],[8.042,6.653],[5.674,-8.76],[9.975,3.069],[1.89,-10.265],[10.39,-0.981],[-2.182,-10.206],[9.224,-4.884],[-5.921,-8.594],[6.653,-8.043],[0.377,-0.577],[0,0],[-2.524,-6.935],[0,0],[-6.936,2.524],[0,0],[-0.538,-0.099],[-0.981,-10.389],[-10.207,2.181],[-4.884,-9.224],[-8.594,5.922],[-8.043,-6.653],[-5.673,8.76],[-9.975,-3.069],[-1.89,10.265],[-10.389,0.981],[2.181,10.207],[-9.223,4.885],[5.922,8.593],[-6.653,8.043],[-0.324,0.476],[0,0],[2.525,6.935],[0,0]],\"o\":[[0,0],[-0.62,-0.183],[-10.265,-1.891],[-0.982,-10.39],[-10.206,2.182],[-4.884,-9.224],[-8.593,5.921],[-8.044,-6.653],[-5.674,8.76],[-9.976,-3.069],[-1.891,10.265],[-10.39,0.982],[2.181,10.207],[-9.224,4.884],[5.922,8.593],[-0.454,0.549],[0,0],[-6.936,2.525],[0,0],[2.524,6.935],[0,0],[0.514,0.142],[10.265,1.89],[0.982,10.39],[10.207,-2.181],[4.884,9.224],[8.593,-5.922],[8.042,6.653],[5.674,-8.76],[9.976,3.069],[1.89,-10.266],[10.39,-0.981],[-2.182,-10.205],[9.224,-4.884],[-5.922,-8.594],[0.378,-0.457],[0,0],[6.935,-2.524],[0,0],[-2.525,-6.935]],\"v\":[[128.761,-88.359],[115.274,-83.45],[113.36,-83.909],[92.912,-106.238],[72.57,-121.163],[45.132,-133.966],[20.628,-139.969],[-9.619,-141.299],[-34.559,-137.468],[-63.012,-127.121],[-84.586,-114.037],[-106.915,-93.589],[-121.839,-73.247],[-134.643,-45.809],[-140.647,-21.304],[-141.976,8.944],[-143.222,10.635],[-155.345,15.047],[-163.345,32.203],[-145.917,80.086],[-128.761,88.086],[-116.291,83.547],[-114.714,83.91],[-94.266,106.238],[-73.924,121.163],[-46.486,133.967],[-21.981,139.97],[8.267,141.299],[33.205,137.469],[61.658,127.122],[83.233,114.038],[105.561,93.589],[120.486,73.247],[133.289,45.809],[139.293,21.305],[140.622,-8.943],[141.672,-10.344],[155.345,-15.32],[163.344,-32.476],[145.917,-80.359]],\"c\":true},\"a\":0}},{\"ty\":\"fl\",\"nm\":\"F\",\"bm\":0,\"c\":{\"a\":0,\"k\":[0.827,0.066,0.066,1]},\"o\":{\"a\":0,\"k\":100},\"r\":1},{\"ty\":\"tr\",\"o\":{\"a\":0,\"k\":100},\"r\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[166.119,148.202]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"nm\":\"T\",\"sk\":{\"a\":0,\"k\":0},\"sa\":{\"a\":0,\"k\":0}}]}],\"cl\":\"ai\",\"ip\":0,\"op\":150,\"st\":-167.5,\"ty\":4}],\"markers\":[]}"));}}),
"[project]/public/assets/images/hurray.json (json)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"nm\":\"Comp 1\",\"ddd\":0,\"h\":512,\"w\":512,\"meta\":{\"g\":\"@lottiefiles/toolkit-js 0.33.2\"},\"layers\":[{\"ty\":0,\"nm\":\"Pre-comp 1\",\"sr\":1,\"st\":0,\"op\":60,\"ip\":0,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[256,256,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[254,259.8,0],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"w\":512,\"h\":512,\"refId\":\"comp_0\",\"ind\":1}],\"v\":\"5.7.4\",\"fr\":60,\"op\":60,\"ip\":0,\"assets\":[{\"nm\":\"\",\"id\":\"comp_0\",\"layers\":[{\"ty\":4,\"nm\":\"green-boom 2\",\"sr\":1,\"st\":30,\"op\":60,\"ip\":30,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[23.576,-172.424,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[319.576,269.576,0],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.472,\"y\":-0.049},\"i\":{\"x\":0,\"y\":1},\"s\":[4,4],\"t\":30},{\"o\":{\"x\":0.6,\"y\":0.01},\"i\":{\"x\":0.25,\"y\":0.992},\"s\":[14,14],\"t\":39},{\"s\":[0,0],\"t\":41}],\"ix\":2}},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":1,\"ml\":4,\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.933,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[100],\"t\":30},{\"s\":[0],\"t\":40}],\"ix\":4},\"w\":{\"a\":0,\"k\":6,\"ix\":5},\"d\":[{\"nm\":\"dash\",\"n\":\"d\",\"v\":{\"a\":0,\"k\":0,\"ix\":1}},{\"nm\":\"offset\",\"n\":\"o\",\"v\":{\"a\":0,\"k\":0,\"ix\":7}}],\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[23.576,-172.424],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":1},{\"ty\":4,\"nm\":\"green-boom\",\"sr\":1,\"st\":34,\"op\":64,\"ip\":34,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[23.576,-172.424,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[379.576,219.576,0],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.472,\"y\":-0.049},\"i\":{\"x\":0,\"y\":1},\"s\":[6,6],\"t\":35},{\"o\":{\"x\":0.6,\"y\":0.013},\"i\":{\"x\":0.25,\"y\":0.989},\"s\":[16,16],\"t\":44},{\"s\":[0,0],\"t\":47}],\"ix\":2}},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":1,\"ml\":4,\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.933,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[100],\"t\":36},{\"s\":[0],\"t\":48}],\"ix\":4},\"w\":{\"a\":0,\"k\":8,\"ix\":5},\"d\":[{\"nm\":\"dash\",\"n\":\"d\",\"v\":{\"a\":0,\"k\":0,\"ix\":1}},{\"nm\":\"offset\",\"n\":\"o\",\"v\":{\"a\":0,\"k\":0,\"ix\":7}}],\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[23.576,-172.424],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":2},{\"ty\":4,\"nm\":\"yellow-boom\",\"sr\":1,\"st\":24,\"op\":54,\"ip\":24,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[23.576,-172.424,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[269.576,133.576,0],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.833,\"y\":0},\"i\":{\"x\":0.267,\"y\":1},\"s\":[10,10],\"t\":25},{\"o\":{\"x\":0.933,\"y\":0},\"i\":{\"x\":0.833,\"y\":1},\"s\":[20,20],\"t\":35},{\"s\":[0,0],\"t\":38}],\"ix\":2}},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":1,\"ml\":4,\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.933,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[100],\"t\":28},{\"s\":[0],\"t\":37}],\"ix\":4},\"w\":{\"a\":0,\"k\":6,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[23.576,-172.424],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":3},{\"ty\":4,\"nm\":\"confetti_shape\",\"sr\":1,\"st\":24,\"op\":180,\"ip\":24,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[74,69,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[74,74,100],\"t\":24},{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[105,105,100],\"t\":26},{\"s\":[100,100,100],\"t\":27}],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":0,\"k\":[296.5,226,0],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":false,\"i\":[[0,0],[-19.736,-7.604]],\"o\":[[0,0],[0,0]],\"v\":[[-17.609,3.794],[17.609,3.802]]},\"ix\":2}},{\"ty\":\"tm\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Filter - Trim\",\"nm\":\"Trim Paths 1\",\"ix\":2,\"e\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":24},{\"s\":[100],\"t\":35}],\"ix\":2},\"o\":{\"a\":0,\"k\":0,\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":30},{\"s\":[75],\"t\":42}],\"ix\":1},\"m\":1},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":2,\"ml\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":12,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[114.787,122.331],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0.018,\"y\":1},\"s\":[0],\"t\":24},{\"s\":[100],\"t\":29}],\"ix\":7}}]},{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 2\",\"ix\":2,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":false,\"i\":[[0,0],[-52.412,-14.912]],\"o\":[[0,0],[0,0]],\"v\":[[-41.134,17.475],[41.134,-2.564]]},\"ix\":2}},{\"ty\":\"tm\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Filter - Trim\",\"nm\":\"Trim Paths 1\",\"ix\":2,\"e\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.637,\"y\":0.012},\"i\":{\"x\":0.008,\"y\":1},\"s\":[0],\"t\":24},{\"s\":[100],\"t\":39}],\"ix\":2},\"o\":{\"a\":0,\"k\":0,\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":30},{\"s\":[20],\"t\":44}],\"ix\":1},\"m\":1},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":2,\"ml\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":12,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[96.008,94.995],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.752,\"y\":0},\"i\":{\"x\":0.374,\"y\":1},\"s\":[0],\"t\":24},{\"s\":[100],\"t\":27}],\"ix\":7}}]},{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 3\",\"ix\":3,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":false,\"i\":[[0,0],[7.508,19.472]],\"o\":[[13.538,-13.3],[0,0]],\"v\":[[-7.766,27.525],[0.258,-27.525]]},\"ix\":2}},{\"ty\":\"tm\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Filter - Trim\",\"nm\":\"Trim Paths 1\",\"ix\":2,\"e\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.464,\"y\":0.001},\"i\":{\"x\":0.067,\"y\":0.99},\"s\":[1],\"t\":24.182},{\"s\":[100],\"t\":38}],\"ix\":2},\"o\":{\"a\":0,\"k\":0,\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":27},{\"s\":[30],\"t\":42}],\"ix\":1},\"m\":1},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":2,\"ml\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":12,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[63.876,45.12],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":24},{\"s\":[100],\"t\":29}],\"ix\":7}}]},{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 4\",\"ix\":4,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":false,\"i\":[[0,0],[0,0]],\"o\":[[-0.132,-22.835],[0,0]],\"v\":[[7.391,17.381],[-7.391,-17.381]]},\"ix\":2}},{\"ty\":\"tm\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Filter - Trim\",\"nm\":\"Trim Paths 1\",\"ix\":2,\"e\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":24},{\"s\":[100],\"t\":34}],\"ix\":2},\"o\":{\"a\":0,\"k\":0,\"ix\":3},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":30},{\"s\":[54],\"t\":37}],\"ix\":1},\"m\":2},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":2,\"ml\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":12,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[7.805,18.077],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[27.195,47.458],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":4},{\"ty\":4,\"nm\":\"yellow-oval\",\"sr\":1,\"st\":17,\"op\":180,\"ip\":17,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.408,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[251.01,188.586,0],\"t\":17.307,\"ti\":[26.176,13.914,0],\"to\":[-2.51,-34.086,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[207.896,111.836,0],\"t\":28,\"ti\":[0,-3.333,0],\"to\":[-26.176,-13.914,0]},{\"s\":[197.896,161.836,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[14,14],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":5},{\"ty\":4,\"nm\":\"yellow-oval\",\"sr\":1,\"st\":30,\"op\":180,\"ip\":30,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.898,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[40,40,100],\"t\":33},{\"s\":[100,100,100],\"t\":40}],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[272.548,202.548,0],\"t\":30,\"ti\":[5.333,42.333,0],\"to\":[9.667,-24.833,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[282.548,122.548,0],\"t\":37,\"ti\":[0,-3.333,0],\"to\":[1.667,-13.333,0]},{\"s\":[272.548,172.548,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":30},{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0.833,\"y\":1},\"s\":[100],\"t\":39},{\"s\":[1],\"t\":53}],\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[8,8],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":6},{\"ty\":4,\"nm\":\"green-oval-small\",\"sr\":1,\"st\":17,\"op\":180,\"ip\":17,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.923,\"y\":0},\"i\":{\"x\":0.056,\"y\":1},\"s\":[283.5,203,0],\"t\":17,\"ti\":[0,0,0],\"to\":[43.583,-62.28,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0.056,\"y\":1},\"s\":[410.5,156,0],\"t\":24,\"ti\":[0,0,0],\"to\":[0,0,0]},{\"s\":[400.5,206,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":1,\"y\":0},\"i\":{\"x\":0.667,\"y\":1},\"s\":[100],\"t\":17},{\"s\":[0],\"t\":38}],\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[8,8],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":7},{\"ty\":4,\"nm\":\"green-oval\",\"sr\":1,\"st\":24,\"op\":180,\"ip\":24,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.863,\"y\":0},\"i\":{\"x\":0.407,\"y\":1},\"s\":[276.5,209,0],\"t\":24,\"ti\":[-2.512,57.293,0],\"to\":[0,0,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0.407,\"y\":1},\"s\":[335.5,104,0],\"t\":34,\"ti\":[0,-3.333,0],\"to\":[0,0,0]},{\"s\":[325.5,154,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[14,14],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":8},{\"ty\":4,\"nm\":\"yellow-oval\",\"sr\":1,\"st\":25,\"op\":180,\"ip\":25,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":1,\"y\":-0.005},\"i\":{\"x\":0.667,\"y\":1},\"s\":[20,20,100],\"t\":25},{\"s\":[100,100,100],\"t\":40}],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.454,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[286.548,206.048,0],\"t\":25,\"ti\":[-56.667,11,0],\"to\":[11.667,-10,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[356.548,146.048,0],\"t\":36,\"ti\":[0,-3.333,0],\"to\":[11.667,-10,0]},{\"s\":[346.548,196.048,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0.063,\"y\":1},\"s\":[0],\"t\":25},{\"s\":[100],\"t\":35}],\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[14,14],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":9},{\"ty\":4,\"nm\":\"green-oval\",\"sr\":1,\"st\":27,\"op\":180,\"ip\":27,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[20,20,100],\"t\":27},{\"s\":[100,100,100],\"t\":31}],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[282.548,215.548,0],\"t\":27,\"ti\":[-17.048,45.452,0],\"to\":[6.667,-8.333,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[322.548,165.548,0],\"t\":30,\"ti\":[0,-3.333,0],\"to\":[17.048,-45.452,0]},{\"s\":[312.548,215.548,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":27},{\"s\":[100],\"t\":32}],\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[14,14],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":10},{\"ty\":4,\"nm\":\"green-oval\",\"sr\":1,\"st\":25,\"op\":180,\"ip\":25,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[392.048,235.048,0],\"t\":25,\"ti\":[-2.167,-7.167,0],\"to\":[1.667,1.667,0]},{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[402.048,245.048,0],\"t\":40,\"ti\":[0,-3.333,0],\"to\":[1.667,1.667,0]},{\"s\":[392.048,295.048,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0.029,\"y\":1.005},\"s\":[100],\"t\":30},{\"s\":[0],\"t\":50}],\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[14,14],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":11},{\"ty\":4,\"nm\":\"yellow-oval\",\"sr\":1,\"st\":25,\"op\":180,\"ip\":25,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[-41.952,-163.452,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[30,30,100],\"t\":25},{\"s\":[100,100,100],\"t\":34}],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.571,\"y\":0.009},\"i\":{\"x\":0,\"y\":1},\"s\":[296.548,229.548,0],\"t\":25,\"ti\":[0,0,0],\"to\":[42.952,-13.048,0]},{\"o\":{\"x\":0.167,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[366.548,259.548,0],\"t\":30,\"ti\":[0,0,0],\"to\":[0,0,0]},{\"s\":[356.548,309.548,0],\"t\":75}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[20],\"t\":25},{\"s\":[100],\"t\":38}],\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Ellipse 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"el\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Ellipse\",\"nm\":\"Ellipse Path 1\",\"d\":1,\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"s\":{\"a\":0,\"k\":[14,14],\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[-41.952,-163.452],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":12},{\"ty\":4,\"nm\":\"confetti_osnova\",\"sr\":1,\"st\":0,\"op\":180,\"ip\":0,\"hd\":false,\"ddd\":0,\"bm\":0,\"hasMask\":false,\"ao\":0,\"ks\":{\"a\":{\"a\":0,\"k\":[96,102,0],\"ix\":1},\"s\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.637,\"y\":0.029},\"i\":{\"x\":0,\"y\":0.91},\"s\":[30,30,100],\"t\":0.02},{\"o\":{\"x\":0.101,\"y\":-0.005},\"i\":{\"x\":0.226,\"y\":1.005},\"s\":[105,105,100],\"t\":19.02},{\"s\":[95,95,100],\"t\":21}],\"ix\":6},\"sk\":{\"a\":0,\"k\":0},\"p\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.167,\"y\":0.167},\"i\":{\"x\":0.833,\"y\":0.833},\"s\":[246.5,281,0],\"t\":75,\"ti\":[-1.667,8.333,0],\"to\":[1.667,-8.333,0]},{\"s\":[256.5,231,0],\"t\":180}],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"sa\":{\"a\":0,\"k\":0},\"o\":{\"a\":0,\"k\":100,\"ix\":11}},\"ef\":[],\"shapes\":[{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 1\",\"ix\":1,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":true,\"i\":[[0,0],[2.255,3.95],[1.375,2.545],[0,0],[0,0],[0,0]],\"o\":[[-2.959,-4.041],[-0.917,-1.541],[0,0],[0,0],[0,0],[0,0]],\"v\":[[25.813,-44.794],[17.971,-56.812],[14.716,-62.667],[-25.813,62.667],[3.379,52.743],[-4.177,49.909]]},\"ix\":2}},{\"ty\":\"gf\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - G-Fill\",\"nm\":\"Gradient Fill 1\",\"e\":{\"a\":0,\"k\":[91.352,-277.215],\"ix\":6},\"g\":{\"p\":3,\"k\":{\"a\":0,\"k\":[0,0.796078431372549,0.6,0.19607843137254904,0.5,0.796078431372549,0.6,0.19607843137254904,1,0.796078431372549,0.6,0.19607843137254904],\"ix\":9}},\"t\":1,\"a\":{\"a\":0,\"k\":0},\"h\":{\"a\":0,\"k\":0},\"s\":{\"a\":0,\"k\":[-10.785,43.922],\"ix\":5},\"r\":1,\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.792,\"y\":0},\"i\":{\"x\":0.437,\"y\":1},\"s\":[0],\"t\":0},{\"s\":[100],\"t\":16}],\"ix\":10}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[0.7961,0.6,0.1961],\"ix\":4},\"r\":1,\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":1,\"y\":-0.006},\"i\":{\"x\":0.667,\"y\":1},\"s\":[0],\"t\":0},{\"s\":[100],\"t\":23}],\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[47.396,113.951],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]},{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 2\",\"ix\":2,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":true,\"i\":[[10.534,-10.533],[31.99,31.991],[-10.534,10.534],[-31.99,-31.99]],\"o\":[[-10.533,10.534],[-31.991,-31.991],[10.534,-10.534],[31.992,31.991]],\"v\":[[57.924,57.924],[-19.072,19.073],[-57.924,-57.924],[19.072,-19.073]]},\"ix\":2}},{\"ty\":\"tm\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Filter - Trim\",\"nm\":\"Trim Paths 1\",\"ix\":2,\"e\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.454,\"y\":0},\"i\":{\"x\":0.011,\"y\":0.993},\"s\":[0],\"t\":0},{\"s\":[100],\"t\":20}],\"ix\":2},\"o\":{\"a\":0,\"k\":170,\"ix\":3},\"s\":{\"a\":0,\"k\":0,\"ix\":1},\"m\":1},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":1,\"ml\":4,\"o\":{\"a\":0,\"k\":100,\"ix\":4},\"w\":{\"a\":0,\"k\":8,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.0784,0.0824,0.0902],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[120.608,76.458],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]},{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 3\",\"ix\":3,\"cix\":2,\"np\":4,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":true,\"i\":[[10.534,-10.533],[31.99,31.991],[-10.534,10.534],[-31.99,-31.99]],\"o\":[[-10.533,10.534],[-31.991,-31.991],[10.534,-10.534],[31.992,31.991]],\"v\":[[59.653,56.195],[-17.343,17.344],[-56.195,-59.653],[20.801,-20.802]]},\"ix\":2}},{\"ty\":\"fl\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Fill\",\"nm\":\"Fill 1\",\"c\":{\"a\":0,\"k\":[1,1,1],\"ix\":4},\"r\":1,\"o\":{\"a\":0,\"k\":100,\"ix\":5}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[118.879,78.187],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.66,\"y\":0},\"i\":{\"x\":0.34,\"y\":1},\"s\":[0],\"t\":0},{\"s\":[100],\"t\":6}],\"ix\":7}}]},{\"ty\":\"gr\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Group\",\"nm\":\"Group 4\",\"ix\":4,\"cix\":2,\"np\":3,\"it\":[{\"ty\":\"sh\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Shape - Group\",\"nm\":\"Path 1\",\"ix\":1,\"d\":1,\"ks\":{\"a\":0,\"k\":{\"c\":false,\"i\":[[0,0],[0,0],[-6.219,2.031],[0,0]],\"o\":[[0,0],[-2.007,6.222],[0,0],[0,0]],\"v\":[[-31.137,-84.827],[-81.74,72.734],[-71.628,82.796],[83.747,30.34]]},\"ix\":2}},{\"ty\":\"tm\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Filter - Trim\",\"nm\":\"Trim Paths 1\",\"ix\":2,\"e\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.547,\"y\":0},\"i\":{\"x\":0.195,\"y\":1},\"s\":[0],\"t\":0},{\"s\":[100],\"t\":20}],\"ix\":2},\"o\":{\"a\":0,\"k\":60,\"ix\":3},\"s\":{\"a\":0,\"k\":0,\"ix\":1},\"m\":1},{\"ty\":\"st\",\"bm\":0,\"hd\":false,\"mn\":\"ADBE Vector Graphic - Stroke\",\"nm\":\"Stroke 1\",\"lc\":2,\"lj\":1,\"ml\":4,\"o\":{\"a\":1,\"k\":[{\"o\":{\"x\":0.333,\"y\":0},\"i\":{\"x\":0,\"y\":1},\"s\":[0],\"t\":0},{\"s\":[100],\"t\":10}],\"ix\":4},\"w\":{\"a\":0,\"k\":8,\"ix\":5},\"c\":{\"a\":0,\"k\":[0.0784,0.0824,0.0902],\"ix\":3}},{\"ty\":\"tr\",\"a\":{\"a\":0,\"k\":[0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100],\"ix\":3},\"sk\":{\"a\":0,\"k\":0,\"ix\":4},\"p\":{\"a\":0,\"k\":[91.747,106.253],\"ix\":2},\"r\":{\"a\":0,\"k\":0,\"ix\":6},\"sa\":{\"a\":0,\"k\":0,\"ix\":5},\"o\":{\"a\":0,\"k\":100,\"ix\":7}}]}],\"ind\":13}]}]}"));}}),
"[project]/src/components/commonModals/CandidateStatusModal.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$GreenCheckIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/GreenCheckIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RoundCrossIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/RoundCrossIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/screenResumeServices.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/jobRequirementConstant.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lottie$2d$react$2f$build$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lottie-react/build/index.es.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lottie$2d$react$2f$build$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lottie-react/build/index.es.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$rejected$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/public/assets/images/rejected.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$hurray$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/public/assets/images/hurray.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const CandidateStatusModal = ({ onClickCancel, candidate, actionType = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED, onSuccess, aiDecision, title })=>{
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((state)=>state.auth.authData);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [success, setSuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { control, handleSubmit, formState: { errors } } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        defaultValues: {
            reason: ""
        },
        mode: "onSubmit",
        criteriaMode: "firstError",
        shouldFocusError: true,
        reValidateMode: "onChange",
        resolver: (values)=>{
            const errors = {};
            // Required validation for reason field
            if (!values.reason || values.reason.trim() === "") {
                errors.reason = {
                    type: "required",
                    message: "Please provide a reason"
                };
            } else if (values.reason.trim().length < 5) {
                errors.reason = {
                    type: "minLength",
                    message: "Reason should be at least 5 characters long"
                };
            } else if (values.reason.trim().length > 300) {
                errors.reason = {
                    type: "maxLength",
                    message: "Reason should not exceed 300 characters"
                };
            }
            return {
                values,
                errors
            };
        }
    });
    const onSubmit = async (formData)=>{
        if (!candidate || !authData) return;
        try {
            setIsSubmitting(true);
            setError("");
            const data = {
                job_id: candidate.job_id,
                candidate_id: candidate.candidate_id,
                hiring_manager_id: authData.id,
                status: actionType,
                hiring_manager_reason: formData.reason
            };
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["changeApplicationStatus"])(data);
            if (response.data && response.data.success) {
                let successMessage = "";
                switch(actionType){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED:
                        successMessage = "Candidate successfully marked as approved.";
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED:
                        successMessage = "Candidate successfully marked as rejected.";
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD:
                        successMessage = "Candidate has been placed on hold successfully!";
                        break;
                    default:
                        successMessage = `Candidate status updated to ${actionType.toLowerCase()}.`;
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(successMessage);
                setSuccess(true);
                // Call the onSuccess callback if provided
                if (onSuccess) {
                    setTimeout(()=>{
                        onClickCancel();
                        onSuccess();
                    }, 1500);
                }
            } else {
                setError(response.data?.message || "Failed to update candidate status");
            }
        } catch (err) {
            console.error("Error updating candidate status:", err);
            setError("An unexpected error occurred. Please try again.");
        } finally{
            setIsSubmitting(false);
        }
    };
    // Helper function to determine modal header content
    const renderModalHeader = ()=>{
        if (actionType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "model-heading-lottie",
                        children: [
                            "Hurray! ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lottie$2d$react$2f$build$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
                                animationData: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$hurray$2e$json__$28$json$29$__["default"],
                                className: "lottie-icon"
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                lineNumber: 137,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 136,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Candidate marked as a good fit, shortlisted for the next step."
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 139,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true);
        } else if (actionType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "model-heading-lottie",
                        children: [
                            "Uh-Oh! ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lottie$2d$react$2f$build$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
                                animationData: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$rejected$2e$json__$28$json$29$__["default"],
                                className: "lottie-icon"
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                lineNumber: 146,
                                columnNumber: 20
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 145,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "The candidate is unfit for the job."
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 148,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true);
        } else if (actionType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "On-Hold Confirmation"
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 154,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "You are about to place this candidate on hold for further review."
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 155,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true);
        }
    };
    // Helper function to determine the status class
    const getStatusClass = ()=>{
        switch(actionType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED:
                return "approved-status";
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED:
                return "approved-status rejected-status";
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD:
                return "on-hold-status";
            default:
                return "";
        }
    };
    // Helper function to determine status label
    const getStatusLabel = ()=>{
        switch(actionType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$GreenCheckIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                            lineNumber: 181,
                            columnNumber: 13
                        }, this),
                        actionType,
                        " By You"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                    lineNumber: 180,
                    columnNumber: 11
                }, this);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RoundCrossIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            DangerColor: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                            lineNumber: 188,
                            columnNumber: 13
                        }, this),
                        actionType,
                        " By You"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                    lineNumber: 187,
                    columnNumber: 11
                }, this);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: "On-Hold For Review"
                }, void 0, false, {
                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                    lineNumber: 193,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: actionType
                }, void 0, false, {
                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                    lineNumber: 195,
                    columnNumber: 16
                }, this);
        }
    };
    // Helper function to determine reason label
    const getReasonLabel = ()=>{
        switch(actionType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED:
                return "Reason for approval";
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED:
                return "Reason for rejection";
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD:
                return "Reason for putting on-hold";
            default:
                return "Reason";
        }
    };
    // Helper function to determine reason placeholder
    const getReasonPlaceholder = ()=>{
        switch(actionType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED:
                return "Please enter the reason for approving this candidate";
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED:
                return "Please enter the reason for rejecting this candidate";
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD:
                return "Enter reason for putting candidate on-hold";
            default:
                return "Enter reason";
        }
    };
    // Helper for the AI reason title
    const getAiReasonTitle = ()=>{
        if (title) {
            return title;
        }
        if (actionType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED || aiDecision === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED) {
            return "Why the candidate is unfit for the role";
        }
        return "Why the candidate is a good fit for the role";
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `modal-dialog modal-dialog-centered ${actionType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD ? "modal-md" : ""}`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header justify-content-center pb-0",
                        children: [
                            renderModalHeader(),
                            !isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                    lineNumber: 248,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                lineNumber: 247,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "qualification-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "qualification-card-top",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "name",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toTitleCase"])(candidate?.candidate_name || "Candidate")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                        lineNumber: 257,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        children: [
                                                            candidate?.ai_decision || "Pending",
                                                            " by S9 InnerView"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                        lineNumber: 258,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 256,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "top-right",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: getStatusClass(),
                                                    children: getStatusLabel()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                    lineNumber: 261,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 260,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                        lineNumber: 255,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "qualification-card-mid",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                                    children: getAiReasonTitle()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 265,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: candidate?.ai_reason || "No reason provided by AI evaluation."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 268,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                        lineNumber: 264,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                lineNumber: 254,
                                columnNumber: 13
                            }, this),
                            !success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                onSubmit: handleSubmit(onSubmit),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Label, {
                                                htmlFor: "reason",
                                                required: true,
                                                children: getReasonLabel()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 275,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                rows: 4,
                                                name: "reason",
                                                control: control,
                                                placeholder: getReasonPlaceholder(),
                                                className: "form-control"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 278,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Error, {
                                                message: errors?.reason?.message || ""
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                                lineNumber: 279,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                        lineNumber: 274,
                                        columnNumber: 17
                                    }, this),
                                    error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "error-message alert alert-danger my-3",
                                        children: error
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                        lineNumber: 282,
                                        columnNumber: 27
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        type: "submit",
                                        className: "primary-btn rounded-md w-100",
                                        disabled: isSubmitting,
                                        children: isSubmitting ? "Submitting..." : "Submit"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                        lineNumber: 284,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                                lineNumber: 273,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                        lineNumber: 252,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
                lineNumber: 243,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
            lineNumber: 242,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/CandidateStatusModal.tsx",
        lineNumber: 241,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = CandidateStatusModal;
}}),
"[project]/src/components/views/resume/CandidateQualification.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/* eslint-disable react-hooks/exhaustive-deps */ // Internal libraries
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
// External libraries
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
// Redux, constants, interfaces
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/jobRequirementConstant.ts [app-ssr] (ecmascript)");
// Components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/BackArrowIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
// We will delete this model after mock testing and we have created CandidateStatusModal common modal
// import CandidateQualifiedModal from "@/components/commonModals/CandidateQualifiedModal";
// import CandidateApprovalModal from "@/components/commonModals/CandidateApprovalModal";
// Services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/screenResumeServices.ts [app-ssr] (ecmascript)");
// CSS
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$CandidateStatusModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/CandidateStatusModal.tsx [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * CandidateQualification Component
 *
 * Displays a list of candidates pending qualification with options to approve, reject, or place on hold.
 * Includes infinite scrolling to load more candidates as the user scrolls down.
 *
 * @returns {JSX.Element} The rendered CandidateQualification component
 */ function CandidateQualification({ params, searchParams }) {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Modal states
    const [showCandidateApprovedModal, setShowCandidateApprovedModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showCandidateRejectedModal, setShowCandidateRejectedModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showCandidateHoldModal, setShowCandidateHoldModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const searchParamsPromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].use(searchParams);
    const paramsPromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].use(params);
    // Track the currently selected candidate for modals
    const [selectedCandidate, setSelectedCandidate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // State for candidate applications
    const [applications, setApplications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasMore, setHasMore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const STATUS = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"]; // Status to filter applications
    const initialFetchDone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {
            router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS);
        }
    }, [
        paramsPromise.jobId,
        searchParamsPromise?.title
    ]);
    // Pagination parameters
    const [pagination, setPagination] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        limit: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"],
        offset: 0,
        status: STATUS.PENDING,
        job_id: Number(paramsPromise.jobId)
    });
    // Observer for infinite scrolling
    const observer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const lastApplicationElementRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((node)=>{
        if (loading) return;
        if (observer.current) observer.current.disconnect();
        observer.current = new IntersectionObserver((entries)=>{
            if (entries[0].isIntersecting && hasMore) {
                loadMoreApplications();
            }
        });
        if (node) observer.current.observe(node);
    }, [
        loading,
        hasMore
    ]);
    /**
   * Fetches candidate applications from the API
   *
   * Makes an API call to get pending job applications based on pagination parameters.
   * Updates the applications state with the fetched data and handles pagination status.
   *
   * @returns {Promise<void>}
   */ const fetchApplications = async ()=>{
        try {
            setLoading(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllPendingJobApplications"])(pagination);
            if (response.data) {
                // Extract applications from response
                const newApplications = response.data.data || [];
                const paginationInfo = response.data.pagination || {
                    total: 0,
                    hasMore: false
                };
                // Append new applications to existing ones
                if (pagination.offset === 0) {
                    setApplications(newApplications);
                } else {
                    setApplications((prev)=>[
                            ...prev,
                            ...newApplications
                        ]);
                }
                // Check if we've loaded all applications based on hasMore flag
                setHasMore(paginationInfo.hasMore);
            }
        } catch (error) {
            console.error(t("error_fetching_applications"), error);
        } finally{
            setLoading(false);
        }
    };
    /**
   * Updates pagination to load more applications
   *
   * Increments the offset in the pagination state, which triggers the useEffect
   * to fetch the next batch of applications.
   */ const loadMoreApplications = ()=>{
        setPagination((prev)=>({
                ...prev,
                offset: prev.offset + prev.limit
            }));
    };
    // Fetch applications on component mount or when pagination changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!initialFetchDone.current) {
            initialFetchDone.current = true;
            fetchApplications();
        }
    }, [
        pagination.offset
    ]);
    /**
   * Handles closing the candidate approval modal
   *
   * Resets the modal state and clears the selected candidate.
   */ const onCancelCandidateApprovedModal = async ()=>{
        setShowCandidateApprovedModal(false);
        setSelectedCandidate(null);
    };
    /**
   * Handles closing the candidate rejection modal
   *
   * Resets the modal state and clears the selected candidate.
   */ const onCancelCandidateRejectedModal = async ()=>{
        setShowCandidateRejectedModal(false);
        setSelectedCandidate(null);
    };
    /**
   * Handles closing the candidate hold modal
   *
   * Resets the modal state and clears the selected candidate.
   */ const onCancelCandidateHoldModal = async ()=>{
        setShowCandidateHoldModal(false);
        setSelectedCandidate(null);
    };
    /**
   * Handles successful status change for a candidate
   *
   * Updates the application list by removing the candidate that was processed.
   * Closes any open modals and refreshes the application list if needed.
   *
   * @param {any} candidate - The candidate whose status was changed
   * @param {string} newStatus - The new status of the candidate (Approved, Rejected, On-Hold)
   */ const handleStatusChangeSuccess = (candidate, newStatus)=>{
        // 1. Optimistic UI update - update the candidate status in the local state immediately
        setApplications((prevApplications)=>{
            return prevApplications.map((app)=>{
                if (app.candidate_id === candidate.candidate_id) {
                    // Create a copy of the application with updated status
                    return {
                        ...app,
                        status: newStatus,
                        // Remove it from the list if it's no longer in pending status
                        hidden: app.status === STATUS.PENDING && newStatus !== STATUS.PENDING
                    };
                }
                return app;
            }).filter((app)=>!app.hidden);
        });
        // 2. Refresh data from server in the background to ensure data consistency
        fetchApplications();
    };
    /**
   * Opens the approval modal for a candidate
   *
   * Sets the selected candidate and shows the approval modal.
   *
   * @param {JobApplication} candidate - The candidate to be approved
   */ const handleApproveCandidate = (candidate)=>{
        setSelectedCandidate(candidate);
        setShowCandidateApprovedModal(true);
    };
    /**
   * Opens the rejection modal for a candidate
   *
   * Sets the selected candidate and shows the rejection modal.
   *
   * @param {JobApplication} candidate - The candidate to be rejected
   */ const handleRejectCandidate = (candidate)=>{
        setSelectedCandidate(candidate);
        setShowCandidateRejectedModal(true);
    };
    /**
   * Opens the on-hold modal for a candidate
   *
   * Sets the selected candidate and shows the on-hold modal.
   *
   * @param {JobApplication} candidate - The candidate to be placed on hold
   */ const onHoldCandidate = (candidate)=>{
        setSelectedCandidate(candidate);
        setShowCandidateHoldModal(true);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].resume_page} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].candidate_qualification_page}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].inner_page,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "common-page-header",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "common-page-head-section",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "main-heading",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: ()=>router.back()
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 255,
                                                            columnNumber: 21
                                                        }, this),
                                                        t("resume_analysis"),
                                                        " ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: searchParamsPromise?.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 256,
                                                            columnNumber: 44
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                    lineNumber: 254,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].approved_status_indicator,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 260,
                                                                    columnNumber: 23
                                                                }, this),
                                                                t("approved_by_s9")
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 259,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 264,
                                                                    columnNumber: 23
                                                                }, this),
                                                                t("rejected_by_s9")
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 263,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                            lineNumber: 253,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                        lineNumber: 252,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                    lineNumber: 251,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "row g-4",
                                    children: [
                                        applications.map((application, index)=>{
                                            // Check if this is the last element to attach the ref for infinite scrolling
                                            const isLastElement = index === applications.length - 1;
                                            const isRejected = application.ai_decision === STATUS.REJECTED;
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "col-md-6 col-lg-4",
                                                ref: isLastElement ? lastApplicationElementRef : null,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `qualification-card h-100 ${isRejected ? "rejected-card" : ""}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "qualification-card-top",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "name",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toTitleCase"])(application?.candidate_name)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 283,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        children: [
                                                                            application?.ai_decision,
                                                                            " by S9 InnerView"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 284,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                lineNumber: 282,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 281,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "qualification-card-mid",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                                                        children: [
                                                                            " ",
                                                                            application.ai_decision === STATUS.APPROVED ? t("reasons_for_good_match") : "Reasons why they are not good match:",
                                                                            " "
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 298,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 297,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: application?.ai_reason
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 303,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 296,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "qualification-buttons",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    className: "secondary-btn rounded-md rounded-md p-3",
                                                                    onClick: ()=>handleApproveCandidate(application),
                                                                    children: t("approve")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 306,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    className: "dark-outline-btn rounded-md rounded-md p-3",
                                                                    onClick: ()=>handleRejectCandidate(application),
                                                                    children: t("reject")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 309,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    className: "dark-outline-btn rounded-md rounded-md p-3",
                                                                    onClick: (e)=>{
                                                                        e.stopPropagation();
                                                                        onHoldCandidate(application);
                                                                    },
                                                                    children: t("hold")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                    lineNumber: 312,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                            lineNumber: 305,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                    lineNumber: 280,
                                                    columnNumber: 21
                                                }, this)
                                            }, application.application_id || index, false, {
                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                lineNumber: 279,
                                                columnNumber: 19
                                            }, this);
                                        }),
                                        loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                1,
                                                2,
                                                3
                                            ].map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-md-6 col-lg-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "qualification-card skeleton-card",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "qualification-card-top",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "name",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "skeleton-text skeleton-title"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                                lineNumber: 335,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "skeleton-text skeleton-subtitle"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                                lineNumber: 336,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 334,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "top-right",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "skeleton-circle"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                            lineNumber: 339,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 338,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                lineNumber: 333,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "qualification-card-mid",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "skeleton-text skeleton-label"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 343,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "skeleton-text"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 344,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "skeleton-text"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 345,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "skeleton-text"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 346,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                lineNumber: 342,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "button-align",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "skeleton-button"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 349,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "skeleton-button"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                        lineNumber: 350,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                                lineNumber: 348,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                        lineNumber: 332,
                                                        columnNumber: 23
                                                    }, this)
                                                }, `skeleton-${item}`, false, {
                                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                    lineNumber: 331,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false),
                                        !loading && applications.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "col-12 text-center py-3",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: t("no_candidates_found")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                                lineNumber: 361,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                            lineNumber: 360,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                    lineNumber: 271,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                            lineNumber: 250,
                            columnNumber: 11
                        }, this),
                        applications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "button-align py-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "primary-btn rounded-md",
                                    onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATE_LIST}/${paramsPromise.jobId}?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`),
                                    children: t("view_all_candidates")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                    lineNumber: 369,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "dark-outline-btn rounded-md",
                                    onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}` + `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`),
                                    children: t("back_to_screening")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                                    lineNumber: 379,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                            lineNumber: 368,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                lineNumber: 248,
                columnNumber: 7
            }, this),
            showCandidateApprovedModal && selectedCandidate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$CandidateStatusModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: onCancelCandidateApprovedModal,
                candidate: selectedCandidate,
                actionType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].APPROVED,
                aiDecision: selectedCandidate.ai_decision,
                onSuccess: ()=>handleStatusChangeSuccess(selectedCandidate, STATUS.APPROVED)
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                lineNumber: 396,
                columnNumber: 9
            }, this),
            showCandidateRejectedModal && selectedCandidate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$CandidateStatusModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: onCancelCandidateRejectedModal,
                candidate: selectedCandidate,
                actionType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].REJECTED,
                aiDecision: selectedCandidate.ai_decision,
                onSuccess: ()=>handleStatusChangeSuccess(selectedCandidate, STATUS.REJECTED)
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                lineNumber: 406,
                columnNumber: 9
            }, this),
            showCandidateHoldModal && selectedCandidate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$CandidateStatusModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: onCancelCandidateHoldModal,
                candidate: selectedCandidate,
                actionType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APPLICATION_STATUS"].ON_HOLD,
                onSuccess: ()=>handleStatusChangeSuccess(selectedCandidate, STATUS.ON_HOLD),
                title: selectedCandidate.ai_reason === "Approved" ? "Reasons why they are good match:" : "Reasons why they are not good match:"
            }, void 0, false, {
                fileName: "[project]/src/components/views/resume/CandidateQualification.tsx",
                lineNumber: 416,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
}
const __TURBOPACK__default__export__ = CandidateQualification;
}}),
"[project]/src/app/candidate-qualification/[jobId]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$resume$2f$CandidateQualification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/resume/CandidateQualification.tsx [app-ssr] (ecmascript)");
"use client";
;
;
const page = ({ params, searchParams })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$resume$2f$CandidateQualification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            params: params,
            searchParams: searchParams
        }, void 0, false, {
            fileName: "[project]/src/app/candidate-qualification/[jobId]/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/candidate-qualification/[jobId]/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = page;
}}),

};

//# sourceMappingURL=_8faf3305._.js.map