import Joi from "joi";
import { Status } from "../../schema/s9-innerview/job_applications";

const joiObject = Joi.object().options({ abortEarly: false });

// For GET /candidates
export const getAllCandidatesValidation = joiObject.keys({
  jobId: Joi.number().optional(),
  isActive: Joi.boolean().optional(),
  searchStr: Joi.string().optional().allow(""),
  page: Joi.number().optional().default(0),
  limit: Joi.number().optional().default(10),
});

// For PATCH /candidates/:applicationId/status
export const archiveActiveApplicationValidation = joiObject.keys({
  isActive: Joi.boolean().required(),
  reason: Joi.string().when("status", {
    is: false,
    then: Joi.string().required(),
    otherwise: Joi.string().optional(),
  }),
});

// For GET /candidates/top
export const getTopCandidatesValidation = joiObject.keys({
  jobId: Joi.number().required(),
});

// For POST /candidates/promote-demote
export const promoteDemoteCandidateValidation = joiObject.keys({
  candidateId: Joi.number().required(),
  applicationId: Joi.number().required(),
  action: Joi.string().valid("Promoted", "Demoted").required(),
});

// For GET /candidates/:candidateId
export const getCandidateDetailsValidation = joiObject.keys({
  jobApplicationId: Joi.number().required(),
});

// For PUT /candidate-job-application-status-change/:jobApplicationId
export const updateJobApplicationStatusValidation = joiObject.keys({
  status: Joi.string().valid(Status.HIRED, Status.FINAL_REJECT).required(),
});

// For POST /candidates/update-job-application-status/:jobApplicationId
export const updateJobApplicationIdValidation = joiObject.keys({
  jobApplicationId: Joi.number().required(),
});

// For GET /candidates/get-candidate-interview-history
export const getCandidateInterviewHistoryValidation = joiObject.keys({
  jobApplicationId: Joi.string().required(),
});

// For GET /candidates/get-skill-specific-assessment
export const getSkillSpecificAssessmentValidation = joiObject.keys({
  jobApplicationId: Joi.number().required(),
});

// For POST /candidates/additional-info
export const addApplicantAdditionalInfoValidation = joiObject.keys({
  applicationId: Joi.string().required(),
  description: Joi.string().required(),
  images: Joi.string().optional().allow(null),
});

// For GET /candidates/generate-final-summary
export const generateFinalSummaryValidation = joiObject.keys({
  jobApplicationId: Joi.string().required(),
});
