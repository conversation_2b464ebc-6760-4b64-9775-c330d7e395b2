{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Modal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC, ReactNode, useEffect } from \"react\";\nimport Button from \"./Button\";\n\ninterface ModalProps {\n  title: string;\n  isOpen: boolean;\n  onClose: () => void;\n  size?: \"sm\" | \"md\" | \"lg\";\n  children: ReactNode;\n}\n\nconst Modal: FC<ModalProps> = ({ title, isOpen, onClose, size = \"md\", children }) => {\n  // Add body class to prevent scrolling when modal is open\n  useEffect(() => {\n    if (isOpen) {\n      document.body.classList.add(\"modal-open\");\n    } else {\n      document.body.classList.remove(\"modal-open\");\n    }\n\n    return () => {\n      document.body.classList.remove(\"modal-open\");\n    };\n  }, [isOpen]);\n\n  if (!isOpen) return null;\n\n  const sizeClass =\n    {\n      sm: \"modal-sm\",\n      md: \"modal-md\",\n      lg: \"modal-lg\",\n    }[size] || \"modal-md\";\n\n  return (\n    <>\n      {/* Backdrop with click handler to close */}\n      <div className=\"modal-backdrop\" onClick={onClose}></div>\n\n      <div className=\"modal theme-modal show-modal\">\n        <div className={`modal-dialog modal-dialog-centered ${sizeClass}`}>\n          <div className=\"modal-content\">\n            <div className=\"modal-header pb-0\">\n              <h2 className=\"m-0\"> {title}</h2>\n              <Button className=\"modal-close-btn\" onClick={onClose}>\n                <span aria-hidden=\"true\">&times;</span>\n              </Button>\n            </div>\n            <div className=\"modal-body\">{children}</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Add modal styling */}\n      <style jsx global>{`\n        .modal-backdrop {\n          position: fixed;\n          top: 0;\n          left: 0;\n          z-index: 1040;\n          width: 100vw;\n          height: 100vh;\n          background-color: rgba(0, 0, 0, 0.5);\n          backdrop-filter: blur(2px);\n        }\n\n        .modal-open {\n          overflow: hidden;\n        }\n\n        .theme-modal.show-modal {\n          display: block;\n          position: fixed;\n          top: 0;\n          left: 0;\n          z-index: 1050;\n          width: 100%;\n          height: 100%;\n          overflow-x: hidden;\n          overflow-y: auto;\n          outline: 0;\n          padding: 1rem;\n        }\n\n        .modal-dialog {\n          position: relative;\n          margin: 1.75rem auto;\n          max-width: 500px;\n          animation: modalFadeIn 0.3s ease-out;\n        }\n\n        .modal-sm {\n          max-width: 300px;\n        }\n\n        .modal-md {\n          max-width: 500px;\n        }\n\n        .modal-lg {\n          max-width: 800px;\n        }\n\n        .modal-content {\n          position: relative;\n          display: flex;\n          flex-direction: column;\n          width: 100%;\n          background-color: #fff;\n          border-radius: 8px;\n          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n        }\n\n        .modal-header {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          padding: 1rem 1.5rem;\n          border-bottom: 1px solid #eeeeee;\n        }\n\n        .modal-header h2 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n        }\n\n        .modal-close-btn {\n          background: transparent;\n          border: none;\n          font-size: 1.5rem;\n          line-height: 1;\n          opacity: 0.7;\n          transition: opacity 0.15s;\n          top: 10px !important;\n          right: 20px !important;\n          border: 0;\n          font-size: 24px;\n        }\n\n        .modal-close-btn:hover {\n          opacity: 1;\n        }\n\n        .modal-body {\n          padding: 1.5rem;\n          overflow-y: auto;\n          max-height: calc(100vh - 200px);\n        }\n\n        @keyframes modalFadeIn {\n          from {\n            opacity: 0;\n            transform: translateY(-20px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default Modal;\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;AAFA;;;;AAYA,MAAM,QAAwB,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE;;IAC9E,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YAC9B,OAAO;gBACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,YACJ;QACE,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK,IAAI;IAEb,qBACE;;0BAEE,6LAAC;gBAA+B,SAAS;0DAA1B;;;;;;0BAEf,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAe,CAAC,mCAAmC,EAAE,WAAW;8BAC/D,cAAA,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;kDACb,6LAAC;kFAAa;;4CAAM;4CAAE;;;;;;;kDACtB,6LAAC,+IAAA,CAAA,UAAM;wCAAC,WAAU;wCAAkB,SAAS;kDAC3C,cAAA,6LAAC;4CAAK,eAAY;;sDAAO;;;;;;;;;;;;;;;;;0CAG7B,6LAAC;0EAAc;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHzC;GAxJM;KAAA;uCA0JS", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC,iKAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;KAtBwB;AAwBjB,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP;MARgB", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/EditSkillIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction EditSkillIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 27 26\" fill=\"none\" className={className}>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M25.6677 25.2539H15.9971C15.4451 25.2539 14.9971 24.8059 14.9971 24.2539C14.9971 23.7019 15.4451 23.2539 15.9971 23.2539H25.6677C26.2197 23.2539 26.6677 23.7019 26.6677 24.2539C26.6677 24.8059 26.2197 25.2539 25.6677 25.2539\"\n      />\n      <mask id=\"mask0_13013_355\" style={{ maskType: \"luminance\" }} maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"-1\" width=\"25\" height=\"27\">\n        <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M0.666992 0H23.5744V25.2527H0.666992V0Z\" />\n      </mask>\n      <g>\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M15.4807 2.68886L2.92736 18.3889C2.69936 18.6742 2.61536 19.0422 2.69936 19.3955L3.60736 23.2422L7.65936 23.1915C8.04469 23.1875 8.40069 23.0155 8.63669 22.7222C12.926 17.3555 21.1034 7.12352 21.3354 6.82352C21.554 6.46886 21.6394 5.96752 21.5247 5.48486C21.4074 4.99019 21.0994 4.57019 20.6554 4.30219C20.5607 4.23686 18.314 2.49286 18.2447 2.43819C17.3994 1.76086 16.166 1.87819 15.4807 2.68886V2.68886ZM2.81802 25.2529C2.35536 25.2529 1.95269 24.9355 1.84469 24.4835L0.752691 19.8555C0.527358 18.8969 0.751358 17.9075 1.36602 17.1395L13.926 1.43019C13.9314 1.42486 13.9354 1.41819 13.9407 1.41286C15.318 -0.23381 17.8087 -0.476476 19.4887 0.871523C19.5554 0.923523 21.786 2.65686 21.786 2.65686C22.5967 3.13952 23.23 4.00219 23.47 5.02352C23.7087 6.03419 23.5354 7.07686 22.9794 7.95819C22.938 8.02352 22.902 8.07952 10.198 23.9729C9.58603 24.7355 8.66869 25.1795 7.68336 25.1915L2.83136 25.2529H2.81802Z\"\n        />\n      </g>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M19.6306 11.5792C19.4172 11.5792 19.2039 11.5112 19.0212 11.3725L11.7519 5.78851C11.3146 5.45251 11.2319 4.82584 11.5679 4.38584C11.9052 3.94851 12.5319 3.86717 12.9706 4.20317L20.2412 9.78584C20.6786 10.1218 20.7612 10.7498 20.4239 11.1885C20.2279 11.4445 19.9306 11.5792 19.6306 11.5792\"\n      />\n    </svg>\n  );\n}\n\nexport default EditSkillIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,cAAc,EAAE,SAAS,EAA0B;IAC1D,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEJ,6LAAC;gBAAK,IAAG;gBAAkB,OAAO;oBAAE,UAAU;gBAAY;gBAAG,WAAU;gBAAiB,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAK,QAAO;0BACrH,cAAA,6LAAC;oBAAK,UAAS;oBAAU,UAAS;oBAAU,GAAE;;;;;;;;;;;0BAEhD,6LAAC;0BACC,cAAA,6LAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;;;;;;;;;;;0BAGN,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;;AAIV;KAzBS;uCA2BM", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/NoDataFoundIcon.tsx"], "sourcesContent": ["import React from \"react\";\nfunction NoDataFoundIcon({ width, height }: { width: number; height: number }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width={width} height={height} viewBox=\"0 0 500 500\" fill=\"none\">\n      <path d=\"M500 382.396H0V382.646H500V382.396Z\" fill=\"#EBEBEB\" />\n      <path d=\"M449.901 398.492H416.779V398.742H449.901V398.492Z\" fill=\"#EBEBEB\" />\n      <path d=\"M331.22 401.207H322.527V401.457H331.22V401.207Z\" fill=\"#EBEBEB\" />\n      <path d=\"M415.778 389.207H396.586V389.457H415.778V389.207Z\" fill=\"#EBEBEB\" />\n      <path d=\"M95.652 390.887H52.459V391.137H95.652V390.887Z\" fill=\"#EBEBEB\" />\n      <path d=\"M110.889 390.887H104.556V391.137H110.889V390.887Z\" fill=\"#EBEBEB\" />\n      <path d=\"M225.147 395.109H131.471V395.359H225.147V395.109Z\" fill=\"#EBEBEB\" />\n      <path\n        d=\"M237.014 337.799H43.915C40.768 337.799 38.207 335.238 38.207 332.091V60.6592C38.207 57.5122 40.768 54.9512 43.915 54.9512H237.014C240.16 54.9512 242.721 57.5122 242.721 60.6592V332.091C242.721 335.238 240.16 337.799 237.014 337.799ZM43.915 55.2022C40.905 55.2022 38.457 57.6502 38.457 60.6602V332.092C38.457 335.102 40.905 337.55 43.915 337.55H237.014C240.023 337.55 242.471 335.102 242.471 332.092V60.6592C242.471 57.6502 240.023 55.2012 237.014 55.2012L43.915 55.2022Z\"\n        fill=\"#EBEBEB\"\n      />\n      <path\n        d=\"M453.31 337.799H260.212C257.065 337.799 254.505 335.238 254.505 332.091V60.6592C254.505 57.5122 257.066 54.9512 260.212 54.9512H453.31C456.458 54.9512 459.018 57.5122 459.018 60.6592V332.091C459.019 335.238 456.458 337.799 453.31 337.799ZM260.212 55.2022C257.203 55.2022 254.755 57.6502 254.755 60.6602V332.092C254.755 335.102 257.203 337.55 260.212 337.55H453.31C456.32 337.55 458.768 335.102 458.768 332.092V60.6592C458.768 57.6502 456.32 55.2012 453.31 55.2012L260.212 55.2022Z\"\n        fill=\"#EBEBEB\"\n      />\n      <path d=\"M289.691 174.057L427.472 174.057V83.8296L289.691 83.8296V174.057Z\" fill=\"#E6E6E6\" />\n      <path d=\"M285.487 174.057L425.507 174.057V83.8296L285.487 83.8296V174.057Z\" fill=\"#F0F0F0\" />\n      <path d=\"M289.691 191.766L427.472 191.766V174.058L289.691 174.058V191.766Z\" fill=\"#E6E6E6\" />\n      <path d=\"M278.484 191.766L418.504 191.766V174.058L278.484 174.058V191.766Z\" fill=\"#F0F0F0\" />\n      <path d=\"M419.622 168.173V89.7168L291.373 89.7168V168.173L419.622 168.173Z\" fill=\"#FAFAFA\" />\n      <path d=\"M390.702 168.173L374.322 89.7168H348.761L365.141 168.173H390.702Z\" fill=\"white\" />\n      <path\n        d=\"M416.898 162.315C417.13 162.315 417.319 162.127 417.319 161.895V93.547C417.319 93.315 417.131 93.127 416.898 93.127C416.666 93.127 416.477 93.315 416.477 93.547V161.895C416.477 162.127 416.666 162.315 416.898 162.315Z\"\n        fill=\"#F0F0F0\"\n      />\n      <path d=\"M359.65 168.173L343.27 89.7168H333.304L349.685 168.173H359.65Z\" fill=\"white\" />\n      <path d=\"M292.12 168.173V89.7168H291.373V168.173H292.12Z\" fill=\"#E6E6E6\" />\n      <path opacity=\"0.6\" d=\"M284.095 98.5899H421.876L422.417 92.0039H284.636L284.095 98.5899Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M284.095 109.393H421.876L422.417 102.807H284.636L284.095 109.393Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M284.095 120.193H421.876L422.417 113.607H284.636L284.095 120.193Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M284.095 130.996H421.876L422.417 124.41H284.636L284.095 130.996Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M284.095 141.799H421.876L422.417 135.213H284.636L284.095 141.799Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M284.095 152.6H421.876L422.417 146.014H284.636L284.095 152.6Z\" fill=\"#EBEBEB\" />\n      <path d=\"M407.689 316.779H378.8V322.475H407.689V316.779Z\" fill=\"#E6E6E6\" />\n      <path d=\"M324.309 382.398H329.642V251.333H324.309V382.398Z\" fill=\"#E6E6E6\" />\n      <path d=\"M305.836 322.477H378.799V316.781H305.836V322.477Z\" fill=\"#F5F5F5\" />\n      <path d=\"M407.689 347.947H378.8V353.643H407.689V347.947Z\" fill=\"#E6E6E6\" />\n      <path d=\"M305.836 353.643H378.799V347.947H305.836V353.643Z\" fill=\"#F5F5F5\" />\n      <path d=\"M407.689 254.447H378.8V260.143H407.689V254.447Z\" fill=\"#E6E6E6\" />\n      <path d=\"M305.836 260.143H378.799V254.447H305.836V260.143Z\" fill=\"#F5F5F5\" />\n      <path d=\"M407.689 285.613H378.8V291.309H407.689V285.613Z\" fill=\"#E6E6E6\" />\n      <path d=\"M305.836 291.309H378.799V285.613H305.836V291.309Z\" fill=\"#F5F5F5\" />\n      <path d=\"M397.271 382.398H402.604V251.333H397.271V382.398Z\" fill=\"#E6E6E6\" />\n      <path d=\"M373.466 382.398H378.799V251.333H373.466V382.398Z\" fill=\"#F5F5F5\" />\n      <path d=\"M305.836 382.398H311.169V251.333H305.836V382.398Z\" fill=\"#F5F5F5\" />\n      <path d=\"M65.3732 382.398H119.951L119.951 276.506H65.3732L65.3732 382.398Z\" fill=\"#E6E6E6\" />\n      <path d=\"M79.947 382.397H65.373V367.779H95.227L79.947 382.397Z\" fill=\"#FAFAFA\" />\n      <path d=\"M214.183 382.398H268.761V276.506H214.183V382.398Z\" fill=\"#E6E6E6\" />\n      <path d=\"M65.3734 377.367H226.902V276.506H65.3734V377.367Z\" fill=\"#FAFAFA\" />\n      <path d=\"M212.329 382.397H226.903V367.779H197.048L212.329 382.397Z\" fill=\"#FAFAFA\" />\n      <path d=\"M76.6787 339.33H215.596V314.089H76.6787V339.33Z\" fill=\"#F0F0F0\" />\n      <path d=\"M76.6787 369.965H215.596V344.724H76.6787V369.965Z\" fill=\"#F0F0F0\" />\n      <path\n        d=\"M103.084 316.789H189.192C191.719 316.789 193.768 314.74 193.768 312.212V311.904H98.5078V312.212C98.5078 314.74 100.557 316.789 103.084 316.789Z\"\n        fill=\"#FAFAFA\"\n      />\n      <path d=\"M76.6787 308.695H215.596V283.454H76.6787V308.695Z\" fill=\"#F0F0F0\" />\n      <path\n        d=\"M103.084 286.153H189.192C191.719 286.153 193.768 284.104 193.768 281.576V281.268H98.5078V281.576C98.5078 284.104 100.557 286.153 103.084 286.153Z\"\n        fill=\"#FAFAFA\"\n      />\n      <path\n        d=\"M103.084 347.423H189.192C191.719 347.423 193.768 345.374 193.768 342.847V342.539H98.5078V342.847C98.5078 345.374 100.557 347.423 103.084 347.423Z\"\n        fill=\"#FAFAFA\"\n      />\n      <path d=\"M74.0686 174.057L211.85 174.057V83.8296L74.0686 83.8296V174.057Z\" fill=\"#E6E6E6\" />\n      <path d=\"M69.8648 174.057L209.885 174.057V83.8296L69.8648 83.8296V174.057Z\" fill=\"#F0F0F0\" />\n      <path d=\"M74.0686 191.766L211.85 191.766V174.058L74.0686 174.058V191.766Z\" fill=\"#E6E6E6\" />\n      <path d=\"M62.8609 191.766L202.881 191.766V174.058L62.8609 174.058V191.766Z\" fill=\"#F0F0F0\" />\n      <path d=\"M204 168.173L204 89.7168L75.751 89.7168L75.751 168.173L204 168.173Z\" fill=\"#FAFAFA\" />\n      <path d=\"M175.081 168.173L158.7 89.7168H133.139L149.52 168.173H175.081Z\" fill=\"white\" />\n      <path\n        d=\"M201.275 162.315C201.507 162.315 201.695 162.127 201.695 161.895V93.547C201.695 93.315 201.507 93.127 201.275 93.127C201.043 93.127 200.854 93.315 200.854 93.547V161.895C200.855 162.127 201.043 162.315 201.275 162.315Z\"\n        fill=\"#F0F0F0\"\n      />\n      <path d=\"M144.029 168.173L127.648 89.7168H117.683L134.064 168.173H144.029Z\" fill=\"white\" />\n      <path d=\"M76.498 168.173L76.498 89.7168H75.7511L75.7511 168.173H76.498Z\" fill=\"#E6E6E6\" />\n      <path opacity=\"0.6\" d=\"M68.4727 98.5899H206.254L206.796 92.0039H69.0147L68.4727 98.5899Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M68.4727 102.306H206.254L206.796 95.7188H69.0147L68.4727 102.306Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M68.4727 106.022H206.254L206.796 99.4355H69.0147L68.4727 106.022Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M68.4727 109.737H206.254L206.796 103.15H69.0147L68.4727 109.737Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M68.4727 113.453H206.254L206.796 106.867H69.0147L68.4727 113.453Z\" fill=\"#EBEBEB\" />\n      <path opacity=\"0.6\" d=\"M68.4727 117.168H206.254L206.796 110.582H69.0147L68.4727 117.168Z\" fill=\"#EBEBEB\" />\n      <path d=\"M96.0143 273.271H100.777V220.179H96.0143V273.271Z\" fill=\"#F5F5F5\" />\n      <path d=\"M96.6267 273.234H97.9717V220.142H96.6267V273.234Z\" fill=\"#FAFAFA\" />\n      <path d=\"M98.5355 273.234H99.0625V220.142H98.5355V273.234Z\" fill=\"#FAFAFA\" />\n      <path\n        d=\"M80.7637 276.507H116.028C116.028 274.053 114.039 272.064 111.585 272.064H85.2067C82.7527 272.064 80.7637 274.053 80.7637 276.507Z\"\n        fill=\"#F0F0F0\"\n      />\n      <path\n        d=\"M89.8348 253.138C90.5888 253.138 91.2048 252.521 91.2048 251.768V216.958C91.2048 216.205 90.5878 215.588 89.8348 215.588C89.0808 215.588 88.4648 216.205 88.4648 216.958V251.768C88.4648 252.522 89.0808 253.138 89.8348 253.138Z\"\n        fill=\"#F0F0F0\"\n      />\n      <path d=\"M77.0742 232.799H119.716L114.811 203H81.9792L77.0742 232.799Z\" fill=\"#E0E0E0\" />\n      <path\n        d=\"M250 427.56C357.082 427.56 443.889 422.491 443.889 416.237C443.889 409.984 357.082 404.914 250 404.914C142.918 404.914 56.1113 409.984 56.1113 416.237C56.1113 422.491 142.918 427.56 250 427.56Z\"\n        fill=\"#F5F5F5\"\n      />\n      <path d=\"M104.108 202.362L103.11 202.434L104.438 220.875L105.435 220.803L104.108 202.362Z\" fill=\"#436EB6\" />\n      <path d=\"M106.139 230.317L105.142 230.389L105.642 237.326L106.639 237.254L106.139 230.317Z\" fill=\"#436EB6\" />\n      <path\n        d=\"M337.711 315.162H123.553C118.709 315.162 114.461 311.199 114.112 306.355L103.589 160.225C103.24 155.381 106.918 151.418 111.762 151.418H325.92C330.764 151.418 335.012 155.381 335.361 160.225L345.884 306.355C346.232 311.199 342.555 315.162 337.711 315.162Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M338.532 315.162H124.374C119.53 315.162 115.282 311.199 114.933 306.355L104.41 160.225C104.061 155.381 107.739 151.418 112.583 151.418H326.741C331.585 151.418 335.833 155.381 336.182 160.225L346.705 306.355C347.053 311.199 343.376 315.162 338.532 315.162Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        opacity=\"0.5\"\n        d=\"M338.532 315.162H124.374C119.53 315.162 115.282 311.199 114.933 306.355L104.41 160.225C104.061 155.381 107.739 151.418 112.583 151.418H326.741C331.585 151.418 335.833 155.381 336.182 160.225L346.705 306.355C347.053 311.199 343.376 315.162 338.532 315.162Z\"\n        fill=\"white\"\n      />\n      <path\n        d=\"M327.058 155.822H112.9C112.658 155.822 112.419 155.832 112.183 155.851C106.709 156.302 107.827 164.629 113.352 164.629H327.875C333.4 164.629 333.319 156.302 327.78 155.851C327.541 155.832 327.3 155.822 327.058 155.822Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M118.478 160.226C118.558 161.341 117.72 162.245 116.604 162.245C115.489 162.245 114.52 161.341 114.44 160.226C114.36 159.111 115.198 158.207 116.314 158.207C117.43 158.207 118.398 159.11 118.478 160.226Z\"\n        fill=\"#FAFAFA\"\n      />\n      <path\n        d=\"M125.346 160.226C125.426 161.341 124.588 162.245 123.472 162.245C122.357 162.245 121.388 161.341 121.308 160.226C121.228 159.111 122.066 158.207 123.182 158.207C124.297 158.207 125.266 159.11 125.346 160.226Z\"\n        fill=\"#FAFAFA\"\n      />\n      <path\n        d=\"M132.213 160.226C132.293 161.341 131.454 162.245 130.339 162.245C129.224 162.245 128.255 161.341 128.174 160.226C128.094 159.111 128.933 158.207 130.048 158.207C131.163 158.207 132.132 159.11 132.213 160.226Z\"\n        fill=\"#FAFAFA\"\n      />\n      <path\n        d=\"M332.854 300.579H127.951C126.188 300.579 124.655 299.149 124.528 297.386L115.874 177.213C115.747 175.45 117.074 174.02 118.837 174.02H323.74C325.503 174.02 327.036 175.449 327.163 177.213L335.817 297.386C335.944 299.149 334.617 300.579 332.854 300.579Z\"\n        fill=\"white\"\n      />\n      <path d=\"M246.53 254.799L243.195 208.48L233.087 202.396H205.283L209.057 254.799H246.53Z\" fill=\"white\" />\n      <path\n        d=\"M246.53 255.284H209.057C208.803 255.284 208.592 255.088 208.574 254.834L204.8 202.431C204.79 202.297 204.837 202.164 204.929 202.066C205.021 201.967 205.149 201.912 205.284 201.912H233.088C233.176 201.912 233.263 201.936 233.338 201.982L243.445 208.065C243.58 208.146 243.667 208.288 243.678 208.445L247.013 254.764C247.023 254.898 246.976 255.031 246.884 255.129C246.793 255.228 246.664 255.284 246.53 255.284ZM209.508 254.315H246.009L242.729 208.766L232.953 202.881H205.804L209.508 254.315Z\"\n        fill=\"#436EB6\"\n      />\n      <path d=\"M243.195 208.48L233.087 202.396L236.798 210.456L243.195 208.48Z\" fill=\"#EBEBEB\" />\n      <path\n        d=\"M236.798 210.94C236.613 210.94 236.439 210.833 236.358 210.658L232.647 202.599C232.558 202.406 232.605 202.177 232.764 202.035C232.922 201.893 233.155 201.872 233.337 201.981L243.444 208.065C243.608 208.163 243.698 208.349 243.675 208.539C243.652 208.729 243.52 208.887 243.337 208.943L236.941 210.919C236.894 210.933 236.846 210.94 236.798 210.94ZM234.186 203.623L237.061 209.867L242.017 208.336L234.186 203.623Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M220.969 226.006C221.053 227.175 220.174 228.123 219.004 228.123C217.834 228.123 216.819 227.175 216.735 226.006C216.651 224.837 217.53 223.889 218.699 223.889C219.868 223.889 220.885 224.837 220.969 226.006Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M234.705 226.006C234.789 227.175 233.91 228.123 232.74 228.123C231.571 228.123 230.555 227.175 230.47 226.006C230.386 224.837 231.265 223.889 232.435 223.889C233.605 223.89 234.621 224.837 234.705 226.006Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M238.58 239.736C238.328 239.736 238.116 239.541 238.097 239.286C237.852 235.882 232.514 233.113 226.197 233.113C222.012 233.113 218.255 234.357 216.391 236.36C215.562 237.251 215.179 238.211 215.251 239.216C215.27 239.483 215.069 239.715 214.802 239.734C214.534 239.751 214.303 239.552 214.284 239.285C214.193 238.019 214.676 236.779 215.681 235.699C217.722 233.505 221.751 232.143 226.197 232.143C233.126 232.143 238.778 235.25 239.064 239.215C239.083 239.482 238.882 239.714 238.615 239.733C238.603 239.736 238.591 239.736 238.58 239.736Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M213.167 221.718C213.049 221.718 212.931 221.675 212.837 221.589C212.641 221.407 212.629 221.1 212.811 220.904L214.935 218.616C215.117 218.419 215.424 218.409 215.62 218.59C215.816 218.772 215.827 219.079 215.646 219.275L213.523 221.563C213.427 221.666 213.297 221.718 213.167 221.718Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M237.585 221.718C237.467 221.718 237.348 221.675 237.254 221.588L234.801 219.3C234.605 219.118 234.595 218.811 234.777 218.615C234.959 218.419 235.266 218.409 235.462 218.591L237.915 220.879C238.111 221.061 238.121 221.368 237.939 221.564C237.844 221.666 237.715 221.718 237.585 221.718Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M202.257 265.154H204.517L207.778 269.488L207.466 265.154H209.747L210.311 272.987H208.029L204.786 268.686L205.096 272.987H202.82L202.257 265.154Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M211.334 269.076C211.242 267.797 211.526 266.802 212.188 266.089C212.849 265.377 213.816 265.02 215.087 265.02C216.391 265.02 217.42 265.37 218.176 266.07C218.932 266.77 219.355 267.75 219.446 269.011C219.512 269.926 219.412 270.677 219.146 271.263C218.88 271.849 218.468 272.305 217.909 272.631C217.35 272.957 216.636 273.12 215.767 273.12C214.884 273.12 214.142 272.979 213.543 272.698C212.944 272.417 212.443 271.971 212.039 271.362C211.635 270.754 211.4 269.992 211.334 269.076ZM213.755 269.087C213.812 269.878 214 270.446 214.318 270.791C214.637 271.137 215.049 271.309 215.555 271.309C216.075 271.309 216.465 271.14 216.726 270.801C216.987 270.463 217.085 269.855 217.022 268.979C216.969 268.242 216.781 267.703 216.459 267.363C216.137 267.023 215.721 266.853 215.212 266.853C214.724 266.853 214.345 267.026 214.074 267.371C213.804 267.718 213.698 268.289 213.755 269.087Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M224.151 265.154H227.747C228.456 265.154 229.035 265.25 229.486 265.442C229.936 265.634 230.317 265.91 230.628 266.27C230.939 266.63 231.176 267.048 231.338 267.526C231.501 268.003 231.601 268.509 231.64 269.043C231.7 269.88 231.652 270.529 231.494 270.991C231.337 271.452 231.1 271.839 230.784 272.15C230.468 272.462 230.12 272.669 229.739 272.772C229.219 272.915 228.743 272.986 228.312 272.986H224.716L224.151 265.154ZM226.699 266.928L227.007 271.208H227.6C228.106 271.208 228.461 271.152 228.667 271.04C228.873 270.928 229.026 270.732 229.127 270.452C229.228 270.172 229.256 269.719 229.211 269.092C229.151 268.262 228.975 267.694 228.682 267.388C228.389 267.082 227.929 266.929 227.302 266.929H226.699V266.928Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M237.447 271.694H234.699L234.41 272.987H231.939L234.319 265.154H236.959L240.466 272.987H237.931L237.447 271.694ZM236.823 270.001L235.755 267.185L235.102 270.001H236.823Z\"\n        fill=\"#436EB6\"\n      />\n      <path d=\"M239.369 265.154H246.726L246.865 267.088H244.397L244.822 272.987H242.402L241.977 267.088H239.509L239.369 265.154Z\" fill=\"#436EB6\" />\n      <path\n        d=\"M252.364 271.694H249.616L249.327 272.987H246.856L249.236 265.154H251.876L255.383 272.987H252.848L252.364 271.694ZM251.74 270.001L250.673 267.185L250.02 270.001H251.74Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M353.688 173.081C354.582 172.633 355.647 172.054 356.632 171.513C357.635 170.962 358.633 170.387 359.615 169.789C361.574 168.583 363.549 167.391 365.423 166.067C369.205 163.462 372.748 160.571 375.936 157.414C376.354 157.032 376.709 156.608 377.095 156.204L377.665 155.592L377.951 155.286L378.094 155.133C378.132 155.093 378.123 155.101 378.105 155.125L378.073 155.174C377.931 155.441 378.049 155.36 378.104 155.102C378.171 154.854 378.227 154.449 378.245 154.011C378.309 152.195 377.951 149.906 377.516 147.693C376.626 143.208 375.279 138.595 373.947 134.075L377.831 132.371C380.265 136.717 382.293 141.156 383.944 145.969C384.73 148.407 385.452 150.855 385.663 153.862C385.702 154.628 385.719 155.433 385.58 156.372C385.433 157.296 385.2 158.402 384.357 159.656L384.19 159.887L384.061 160.055L383.908 160.243L383.599 160.617L382.982 161.367C382.565 161.861 382.17 162.382 381.727 162.845C378.279 166.674 374.442 170.056 370.347 172.997C368.309 174.486 366.227 175.9 364.064 177.187C362.987 177.839 361.896 178.465 360.787 179.072C359.664 179.696 358.596 180.239 357.32 180.838L353.688 173.081Z\"\n        fill=\"#FFB573\"\n      />\n      <path\n        d=\"M344.795 408.178C345.569 408.178 346.415 408.018 347.006 407.878C347.083 407.86 347.142 407.798 347.156 407.719C347.17 407.641 347.136 407.562 347.07 407.519C346.783 407.33 344.24 405.689 343.265 406.13C343.111 406.2 342.918 406.353 342.87 406.688C342.807 407.132 342.918 407.486 343.199 407.742C343.556 408.068 344.151 408.178 344.795 408.178ZM346.445 407.595C344.987 407.886 343.89 407.836 343.466 407.449C343.286 407.285 343.219 407.054 343.263 406.743C343.286 406.576 343.364 406.521 343.428 406.492C343.941 406.26 345.408 406.968 346.445 407.595Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M346.96 407.884C346.994 407.884 347.029 407.875 347.06 407.857C347.121 407.821 347.159 407.756 347.159 407.685C347.159 407.583 347.148 405.171 346.237 404.365C345.995 404.151 345.713 404.062 345.397 404.1C344.9 404.16 344.766 404.473 344.729 404.653C344.54 405.597 346.063 407.404 346.862 407.857C346.892 407.875 346.926 407.884 346.96 407.884ZM345.535 404.488C345.698 404.488 345.842 404.545 345.974 404.662C346.518 405.143 346.704 406.497 346.75 407.3C345.953 406.664 345.004 405.306 345.119 404.731C345.138 404.637 345.192 404.525 345.445 404.494C345.475 404.49 345.505 404.488 345.535 404.488Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M346.223 148.068C345.249 153.038 343.195 163.105 346.673 166.422C346.673 166.422 345.314 171.464 336.079 171.464C325.924 171.464 331.226 166.423 331.226 166.423C336.769 165.099 336.626 160.987 335.66 157.125L346.223 148.068Z\"\n        fill=\"#FFB573\"\n      />\n      <path\n        d=\"M329.284 168.424C327.69 168.64 329.046 164.511 329.694 164.083C331.19 163.094 350.545 161.694 350.419 164.043C350.339 165.054 349.864 167.027 349.018 167.697C348.171 168.367 343.199 166.342 329.284 168.424Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M332.437 167.035C331.171 167.463 331.291 163.302 331.722 162.801C332.72 161.643 348.386 157.618 348.851 159.895C349.03 160.878 349.116 162.842 348.579 163.605C348.042 164.367 343.439 163.106 332.437 167.035Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M326.61 139.108C326.487 139.115 326.363 139.066 326.279 138.963C325.159 137.593 323.703 137.733 323.689 137.735C323.468 137.758 323.275 137.601 323.252 137.383C323.228 137.165 323.386 136.969 323.604 136.946C323.681 136.938 325.504 136.761 326.894 138.46C327.033 138.63 327.008 138.88 326.838 139.018C326.771 139.074 326.691 139.103 326.61 139.108Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M324.672 144.002C324.672 144.002 323.816 147.009 322.697 148.529C323.666 149.27 325.138 148.743 325.138 148.743L324.672 144.002Z\"\n        fill=\"#FF5652\"\n      />\n      <path\n        d=\"M325.161 142.786C325.227 143.458 324.924 144.038 324.485 144.082C324.045 144.125 323.636 143.615 323.569 142.943C323.503 142.271 323.806 141.691 324.245 141.647C324.685 141.604 325.095 142.114 325.161 142.786Z\"\n        fill=\"#263238\"\n      />\n      <path d=\"M324.443 141.666L322.782 141.359C322.782 141.359 323.735 142.54 324.443 141.666Z\" fill=\"#263238\" />\n      <path d=\"M356.7 407.686H348.324L348.981 388.289H357.357L356.7 407.686Z\" fill=\"#FFB573\" />\n      <path\n        d=\"M347.662 406.717H357.072C357.414 406.717 357.692 406.955 357.74 407.289L358.812 414.734C358.923 415.506 358.259 416.237 357.469 416.224C354.188 416.168 352.607 415.975 348.469 415.975C345.923 415.975 342.203 416.239 338.688 416.239C335.249 416.239 334.985 412.762 336.449 412.448C343.013 411.036 344.05 409.089 346.262 407.232C346.66 406.897 347.161 406.717 347.662 406.717Z\"\n        fill=\"#263238\"\n      />\n      <g opacity=\"0.2\">\n        <path d=\"M357.36 388.293H348.98L348.646 398.292H357.026L357.36 388.293Z\" fill=\"black\" />\n      </g>\n      <path\n        d=\"M323.374 178.045C318.191 176.016 313.247 173.809 308.324 171.291C303.409 168.776 298.57 166.097 293.92 162.518C292.762 161.607 291.617 160.611 290.498 159.448C290.219 159.148 289.94 158.87 289.666 158.526C289.378 158.164 289.161 157.951 288.787 157.395C288.111 156.423 287.588 155.118 287.449 153.786C287.318 152.453 287.557 151.18 287.94 150.165C288.322 149.132 288.823 148.289 289.347 147.558C290.398 146.094 291.563 145.012 292.736 144.038C295.079 142.099 297.557 140.689 300.039 139.371C301.279 138.713 302.543 138.12 303.823 137.574C305.109 137.019 306.364 136.503 307.729 136.033L309.504 139.886C305.142 142.635 300.624 145.657 297.302 149.045C296.495 149.884 295.789 150.762 295.345 151.535C294.869 152.322 294.875 152.911 294.956 152.866C294.997 152.866 294.981 152.848 295.097 152.964L295.495 153.391C295.645 153.57 295.856 153.753 296.045 153.937C296.83 154.67 297.751 155.406 298.744 156.106C300.729 157.519 302.916 158.841 305.178 160.076C307.432 161.328 309.751 162.526 312.113 163.663C316.825 165.92 321.711 168.092 326.505 170.076L323.374 178.045Z\"\n        fill=\"#FFB573\"\n      />\n      <path\n        d=\"M311.969 137.155L313.354 134.151L307.058 131.965C307.058 131.965 305.097 137.868 307.434 140.592C309.424 140.273 311.125 138.985 311.969 137.155Z\"\n        fill=\"#FFB573\"\n      />\n      <path d=\"M313.355 127.656L308.322 126.205L307.059 131.963L313.355 134.15V127.656Z\" fill=\"#FFB573\" />\n      <path d=\"M378.35 134.442L379.084 127.422L372.596 128.947C372.596 128.947 372.501 135.527 375.933 136.576L378.35 134.442Z\" fill=\"#FFB573\" />\n      <path d=\"M375.958 122.508L371.731 124.482L372.595 128.947L379.083 127.422L375.958 122.508Z\" fill=\"#FFB573\" />\n      <path\n        d=\"M347.54 138.255C347.845 146.562 348.152 150.072 344.398 154.721C338.752 161.713 328.481 160.271 325.7 152.222C323.197 144.977 323.118 132.609 330.878 128.527C338.523 124.507 347.235 129.947 347.54 138.255Z\"\n        fill=\"#FFB573\"\n      />\n      <path\n        d=\"M343.921 154.706C352.076 151.182 357.44 143.751 355.176 132.132C353.01 121.016 345.505 119.918 342.405 122.224C339.305 124.53 331.578 121.093 326.941 124.995C318.906 131.756 326.502 139.045 330.459 143.364C332.823 148.222 335.919 158.164 343.921 154.706Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M340.535 130.138C343.287 134.123 348.565 135.248 352.325 132.651C356.085 130.055 356.902 124.72 354.15 120.735C351.398 116.75 346.12 115.625 342.36 118.222C338.6 120.818 337.783 126.153 340.535 130.138Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M342.652 123.002C341.475 116.304 347.813 111.139 356.288 113.635C364.763 116.131 360.28 123.644 358.542 129.644C356.804 135.644 361.365 141.359 363.271 136.789C365.177 132.22 361.973 130.809 361.973 130.809C361.973 130.809 370.972 133.142 362.612 143.286C354.252 153.431 346.684 142.229 348.731 135.618C350.385 130.276 343.765 129.333 342.652 123.002Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M334.181 125.534C330.312 123.516 323.76 121.884 320.031 128.157C318.268 131.123 318.951 135.146 318.951 135.146L330.34 135.904L334.181 125.534Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M317.612 133.179C317.607 133.179 317.602 133.179 317.596 133.178C317.458 133.17 317.354 133.051 317.362 132.913C317.372 132.743 317.648 128.72 320.076 126.196C325.945 120.098 332.826 125.156 334.795 126.852C334.9 126.942 334.912 127.1 334.821 127.205C334.73 127.31 334.573 127.321 334.468 127.231C332.583 125.608 326.001 120.761 320.436 126.543C318.137 128.932 317.863 132.904 317.86 132.944C317.853 133.076 317.743 133.179 317.612 133.179Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M331.853 143.034C331.841 144.652 331.116 146.236 330.198 147.311C328.816 148.927 327.232 148.131 326.869 146.339C326.542 144.726 326.866 141.942 328.613 140.966C330.333 140.005 331.867 141.184 331.853 143.034Z\"\n        fill=\"#FFB573\"\n      />\n      <path\n        d=\"M330.054 219.123C330.054 219.123 330.588 277.267 335.632 309.668C339.706 335.838 347.245 396.357 347.245 396.357H358.667C358.667 396.357 359.783 337.933 357.715 312.049C352.482 246.551 365.986 234.306 355.089 219.123H330.054Z\"\n        fill=\"#263238\"\n      />\n      <path\n        opacity=\"0.1\"\n        d=\"M330.054 219.123C330.054 219.123 330.588 277.267 335.632 309.668C339.706 335.838 347.245 396.357 347.245 396.357H358.667C358.667 396.357 359.783 337.933 357.715 312.049C352.482 246.551 365.986 234.306 355.089 219.123H330.054Z\"\n        fill=\"white\"\n      />\n      <path\n        opacity=\"0.3\"\n        d=\"M336.057 245.501C340.067 263.051 336.867 290.691 334.677 302.861C332.357 284.381 331.187 260.431 330.607 242.881C332.677 239.521 334.637 239.301 336.057 245.501Z\"\n        fill=\"black\"\n      />\n      <path d=\"M345.682 396.582H360.231L360.995 391.478L345.599 390.957L345.682 396.582Z\" fill=\"#436EB6\" />\n      <path\n        d=\"M349.833 170.042C351.201 167.321 358.558 165.609 362.579 165.615L365.589 178.98C365.589 178.98 357.577 190.87 354.246 189.609C350.31 188.122 346.901 175.872 349.833 170.042Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        opacity=\"0.2\"\n        d=\"M349.833 170.042C351.201 167.321 358.558 165.609 362.579 165.615L365.589 178.98C365.589 178.98 357.577 190.87 354.246 189.609C350.31 188.122 346.901 175.872 349.833 170.042Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M341.354 393.313C341.39 393.254 341.393 393.179 341.36 393.117C341.321 393.043 341.239 393.001 341.155 393.014C340.735 393.077 337.033 393.665 336.533 394.722C336.436 394.927 336.45 395.143 336.572 395.347C336.776 395.688 337.065 395.88 337.43 395.917C338.643 396.042 340.395 394.359 341.331 393.345C341.339 393.334 341.347 393.324 341.354 393.313ZM336.913 394.85C337.255 394.289 339.169 393.772 340.63 393.503C339.311 394.844 338.149 395.591 337.469 395.521C337.231 395.496 337.049 395.372 336.911 395.142C336.857 395.052 336.851 394.975 336.891 394.891C336.898 394.877 336.905 394.863 336.913 394.85Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M341.353 393.313C341.361 393.301 341.367 393.287 341.371 393.273C341.394 393.207 341.379 393.133 341.334 393.08C341.271 393.007 339.788 391.298 338.421 391.209C338.002 391.182 337.636 391.306 337.331 391.579C336.92 391.947 336.955 392.267 337.058 392.47C337.491 393.328 340.151 393.627 341.224 393.404C341.278 393.392 341.324 393.359 341.353 393.313ZM337.438 392.054C337.469 392.003 337.519 391.943 337.596 391.874C337.821 391.672 338.083 391.584 338.395 391.605C339.293 391.664 340.314 392.593 340.778 393.066C339.593 393.166 337.672 392.806 337.412 392.291C337.397 392.26 337.358 392.184 337.438 392.054Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        d=\"M350.083 389.766L342.422 393.169L339.008 386.064L334.547 376.76L334.024 375.678L341.676 372.27L342.268 373.488L346.593 382.498L350.083 389.766Z\"\n        fill=\"#FFB573\"\n      />\n      <path opacity=\"0.2\" d=\"M346.593 382.498L339.008 386.064L334.547 376.76L342.268 373.488L346.593 382.498Z\" fill=\"black\" />\n      <path\n        d=\"M321.069 219.123C321.069 219.123 296.595 279.958 303.229 308.605C309.224 334.495 335.733 383.526 335.733 383.526L345.983 378.381C345.983 378.381 329.595 319.193 327.817 306.335C324.358 281.317 346.593 246.002 346.593 219.123H321.069Z\"\n        fill=\"#263238\"\n      />\n      <path\n        opacity=\"0.1\"\n        d=\"M321.069 219.123C321.069 219.123 296.595 279.958 303.229 308.605C309.224 334.495 335.733 383.526 335.733 383.526L345.983 378.381C345.983 378.381 329.595 319.193 327.817 306.335C324.358 281.317 346.593 246.002 346.593 219.123H321.069Z\"\n        fill=\"white\"\n      />\n      <path\n        d=\"M341.056 392.104L348.664 386.566C348.941 386.364 349.306 386.393 349.541 386.635L354.789 392.024C355.333 392.583 355.226 393.565 354.58 394.019C351.894 395.904 350.502 396.679 347.157 399.114C345.098 400.612 340.996 403.925 338.154 405.994C335.374 408.017 333.114 405.362 334.113 404.246C338.59 399.242 339.533 396.147 340.228 393.344C340.353 392.839 340.651 392.399 341.056 392.104Z\"\n        fill=\"#263238\"\n      />\n      <path d=\"M335.073 385.695L348.304 379.643L346.606 374.252L332.438 380.714L335.073 385.695Z\" fill=\"#436EB6\" />\n      <path\n        d=\"M327.564 169.361C326.466 166.52 316.907 163.404 312.112 162.316L310.005 178.084C310.005 178.084 317.836 189.339 321.275 188.408C325.335 187.309 329.916 175.448 327.564 169.361Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        opacity=\"0.2\"\n        d=\"M327.564 169.361C326.466 166.52 316.907 163.404 312.112 162.316L310.005 178.084C310.005 178.084 317.836 189.339 321.275 188.408C325.335 187.309 329.916 175.448 327.564 169.361Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M317.066 168.568C317.066 168.568 313.076 169.973 321.069 219.123C334.568 219.123 348.52 219.123 355.088 219.123C354.517 205.277 354.501 196.74 361.118 168.325C361.118 168.325 353.99 166.774 346.672 166.422C340.947 166.146 336.257 165.963 331.224 166.423C324.591 167.028 317.066 168.568 317.066 168.568Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        opacity=\"0.2\"\n        d=\"M317.066 168.568C317.066 168.568 313.076 169.973 321.069 219.123C334.568 219.123 348.52 219.123 355.088 219.123C354.517 205.277 354.501 196.74 361.118 168.325C361.118 168.325 353.99 166.774 346.672 166.422C340.947 166.146 336.257 165.963 331.224 166.423C324.591 167.028 317.066 168.568 317.066 168.568Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M355.598 217.125L357.132 220.17C357.251 220.406 356.971 220.647 356.578 220.647H320.901C320.594 220.647 320.34 220.497 320.321 220.304L320.014 217.259C319.993 217.048 320.259 216.869 320.594 216.869H355.044C355.297 216.87 355.521 216.973 355.598 217.125Z\"\n        fill=\"#436EB6\"\n      />\n      <path\n        opacity=\"0.3\"\n        d=\"M355.598 217.125L357.132 220.17C357.251 220.406 356.971 220.647 356.578 220.647H320.901C320.594 220.647 320.34 220.497 320.321 220.304L320.014 217.259C319.993 217.048 320.259 216.869 320.594 216.869H355.044C355.297 216.87 355.521 216.973 355.598 217.125Z\"\n        fill=\"white\"\n      />\n      <path\n        d=\"M350.972 220.978H351.895C352.079 220.978 352.218 220.883 352.205 220.768L351.773 216.812C351.76 216.696 351.6 216.602 351.416 216.602H350.493C350.309 216.602 350.169 216.697 350.182 216.812L350.615 220.768C350.628 220.884 350.789 220.978 350.972 220.978Z\"\n        fill=\"#263238\"\n      />\n      <path\n        d=\"M328.608 220.978H329.531C329.715 220.978 329.855 220.883 329.842 220.768L329.409 216.812C329.396 216.696 329.236 216.602 329.052 216.602H328.129C327.945 216.602 327.805 216.697 327.818 216.812L328.251 220.768C328.263 220.884 328.424 220.978 328.608 220.978Z\"\n        fill=\"#263238\"\n      />\n    </svg>\n  );\n}\nexport default NoDataFoundIcon;\n"], "names": [], "mappings": ";;;;;AACA,SAAS,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAqC;IAC3E,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAO;QAAO,QAAQ;QAAQ,SAAQ;QAAc,MAAK;;0BAC/F,6LAAC;gBAAK,GAAE;gBAAsC,MAAK;;;;;;0BACnD,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAiD,MAAK;;;;;;0BAC9D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAiE,MAAK;;;;;;0BAC9E,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAmE,MAAK;;;;;;0BAC9F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAgE,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAwD,MAAK;;;;;;0BACrE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAA4D,MAAK;;;;;;0BACzE,6LAAC;gBAAK,GAAE;gBAAkD,MAAK;;;;;;0BAC/D,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAmE,MAAK;;;;;;0BAChF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAmE,MAAK;;;;;;0BAChF,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAsE,MAAK;;;;;;0BACnF,6LAAC;gBAAK,GAAE;gBAAiE,MAAK;;;;;;0BAC9E,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAoE,MAAK;;;;;;0BACjF,6LAAC;gBAAK,GAAE;gBAAiE,MAAK;;;;;;0BAC9E,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAmE,MAAK;;;;;;0BAC9F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAoE,MAAK;;;;;;0BAC/F,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBAAK,GAAE;gBAAoD,MAAK;;;;;;0BACjE,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAgE,MAAK;;;;;;0BAC7E,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAmF,MAAK;;;;;;0BAChG,6LAAC;gBAAK,GAAE;gBAAoF,MAAK;;;;;;0BACjG,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAiF,MAAK;;;;;;0BAC9F,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAkE,MAAK;;;;;;0BAC/E,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAoH,MAAK;;;;;;0BACjI,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAmF,MAAK;;;;;;0BAChG,6LAAC;gBAAK,GAAE;gBAAgE,MAAK;;;;;;0BAC7E,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAE,SAAQ;0BACT,cAAA,6LAAC;oBAAK,GAAE;oBAAiE,MAAK;;;;;;;;;;;0BAEhF,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAA2E,MAAK;;;;;;0BACxF,6LAAC;gBAAK,GAAE;gBAAkH,MAAK;;;;;;0BAC/H,6LAAC;gBAAK,GAAE;gBAAoF,MAAK;;;;;;0BACjG,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAA4E,MAAK;;;;;;0BACzF,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,SAAQ;gBAAM,GAAE;gBAAmF,MAAK;;;;;;0BAC9G,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBAAK,GAAE;gBAAoF,MAAK;;;;;;0BACjG,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KAxWS;uCAyWM", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1587, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/jobRequirement/CareerBasedSkills.tsx"], "sourcesContent": ["// Internal libraries\r\nimport { useState, useEffect } from \"react\";\r\n\r\n// External libraries\r\nimport { redirect, useRouter } from \"next/navigation\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\n// Components\r\nimport Button from \"@/components/formElements/Button\";\r\nimport Modal from \"@/components/formElements/Modal\";\r\nimport Loader from \"@/components/loader/Loader\";\r\nimport { CommonInput } from \"@/components/formElements/Textbox\";\r\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\r\nimport EditSkillIcon from \"@/components/svgComponents/EditSkillIcon\";\r\n\r\n// Services (Redux, constants, interfaces)\r\nimport { selectJobDetails } from \"@/redux/slices/jobDetailsSlice\";\r\nimport { selectCareerSkills, setSkillsData } from \"@/redux/slices/jobSkillsSlice\";\r\nimport { ISkillData } from \"@/interfaces/jobRequirementesInterfaces\";\r\nimport ROUTES from \"@/constants/routes\";\r\n\r\n// CSS\r\nimport style from \"@/styles/commonPage.module.scss\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport NoDataFoundIcon from \"@/components/svgComponents/NoDataFoundIcon\";\r\nimport { toastMessageError } from \"@/utils/helper\";\r\n\r\nfunction CareerBasedSkills() {\r\n  const router = useRouter();\r\n  const dispatch = useDispatch();\r\n  const t = useTranslations(\"careerBasedSkills\");\r\n  const tJobRequirement = useTranslations(\"jobRequirement\");\r\n  const tCommon = useTranslations();\r\n  // Get career skills from Redux store\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  const reduxCareerSkills = useSelector(selectCareerSkills) || [];\r\n  const jobDetails = useSelector(selectJobDetails);\r\n\r\n  // Loading states\r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n  // Local state for skills that can be edited before saving to Redux\r\n  const [localCareerSkills, setLocalCareerSkills] = useState<ISkillData[]>([]);\r\n\r\n  // Edit modal state\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [editingSkill, setEditingSkill] = useState<{ index: number; skill: ISkillData | null }>({\r\n    index: -1,\r\n    skill: null,\r\n  });\r\n\r\n  // Form values for the modal\r\n  const [formValues, setFormValues] = useState({\r\n    name: \"\",\r\n    description: \"\",\r\n  });\r\n  const [formErrors, setFormErrors] = useState({\r\n    name: \"\",\r\n    description: \"\",\r\n  });\r\n\r\n  /**\r\n   * Redirects to career-based skills page if required data is missing\r\n   *\r\n   * @effect\r\n   * @description Checks if the skill type is valid and if necessary Redux data is available\r\n   *              Redirects to the career-based skills page if any validation fails\r\n   *              Runs only once on component mount due to empty dependency array\r\n   */\r\n\r\n  useEffect(() => {\r\n    if (!reduxCareerSkills || reduxCareerSkills.length === 0) redirect(`${ROUTES.JOBS.GENERATE_JOB}`);\r\n  }, []);\r\n\r\n  /**\r\n   * Initializes local state with career skills from Redux store\r\n   * @description Sets up local state with career skills when component mounts or Redux state changes\r\n   * @param {ISkillData[]} reduxCareerSkills - Career skills from Redux store\r\n   */\r\n  useEffect(() => {\r\n    setLocalCareerSkills([...reduxCareerSkills]);\r\n  }, [reduxCareerSkills]);\r\n\r\n  /**\r\n   * Opens the edit modal for a specific skill\r\n   * @description Initializes the edit modal with the skill's data and opens the modal\r\n   * @param {ISkillData} skill - The skill to edit\r\n   * @param {number} index - The index of the skill in the local state array\r\n   */\r\n  const handleEditClick = (skill: ISkillData, index: number) => {\r\n    // Set the editing skill reference\r\n    setEditingSkill({ index, skill: { ...skill } });\r\n\r\n    // Initialize form values from the skill with safe null checking\r\n    // This ensures the descriptions and names are shown correctly\r\n    setFormValues({\r\n      name: skill.name !== undefined && skill.name !== null ? skill.name : \"\",\r\n      description: skill.description !== undefined && skill.description !== null ? skill.description : \"\",\r\n    });\r\n\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  /**\r\n   * State for form validation errors\r\n   * @type {{ name: string; description: string }}\r\n   */\r\n\r\n  /**\r\n   * Handles input changes in modal - only updates form values, not the skill list\r\n   * @param {\"name\" | \"description\"} field - The field to update\r\n   * @param {string} value - The new value for the field\r\n   */\r\n  const handleInputChange = (field: \"name\" | \"description\", value: string) => {\r\n    // Update form values\r\n    const updatedFormValues = {\r\n      ...formValues,\r\n      [field]: value,\r\n    };\r\n    setFormValues(updatedFormValues);\r\n\r\n    // Clear validation error when user types\r\n    if (formErrors[field]) {\r\n      setFormErrors({\r\n        ...formErrors,\r\n        [field]: \"\",\r\n      });\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Saves edited skill to local state with validation\r\n   */\r\n  const handleSaveSkill = () => {\r\n    // Validate form values\r\n    const errors = {\r\n      name: \"\",\r\n      description: \"\",\r\n    };\r\n\r\n    if (!formValues.name || formValues.name.trim() === \"\") {\r\n      errors.name = t(\"skill_name_required\");\r\n    } else if (/^\\d+$/.test(formValues.name.trim())) {\r\n      errors.name = t(\"skill_name_not_numeric\");\r\n    } else if (formValues.name.trim().length < 3) {\r\n      errors.name = t(\"skill_name_length\");\r\n    } else if (formValues.name.trim().length > 40) {\r\n      errors.name = t(\"skill_name_max_length\");\r\n    }\r\n\r\n    if (!formValues.description || formValues.description.trim() === \"\") {\r\n      errors.description = t(\"skill_description_required\");\r\n    } else if (formValues.description.trim().length > 200) {\r\n      errors.description = t(\"skill_description_max_length\");\r\n    } else if (formValues.description.trim().length < 3) {\r\n      errors.description = t(\"skill_description_length\");\r\n    } else if (/^\\d+$/.test(formValues.description.trim())) {\r\n      errors.description = t(\"skill_description_not_numeric\");\r\n    }\r\n\r\n    // If there are validation errors, update error state and stop\r\n    if (errors.name || errors.description) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    // If validation passes, create updated skill with form values\r\n    const updatedSkill: ISkillData = {\r\n      name: formValues.name.trim(), // Trim whitespace\r\n      description: formValues.description.trim(), // Trim whitespace\r\n    };\r\n\r\n    // we will check if the skill name already exists\r\n    for (let i = 0; i < localCareerSkills.length; i++) {\r\n      // Skip the current skill being edited\r\n      if (editingSkill.index !== -1 && i === editingSkill.index) continue;\r\n\r\n      if (localCareerSkills[i].name.toLowerCase() === updatedSkill.name.toLowerCase()) {\r\n        toastMessageError(t(\"skill_name_exists\"));\r\n        return;\r\n      }\r\n    }\r\n\r\n    if (editingSkill.index !== -1) {\r\n      // Update existing skill only when save is clicked\r\n      const updatedSkills = [...localCareerSkills];\r\n      updatedSkills[editingSkill.index] = updatedSkill;\r\n      setLocalCareerSkills(updatedSkills);\r\n    }\r\n\r\n    // Reset state and close modal\r\n    setShowEditModal(false);\r\n    setEditingSkill({ index: -1, skill: null });\r\n    setFormValues({ name: \"\", description: \"\" });\r\n    setFormErrors({ name: \"\", description: \"\" }); // Clear validation errors\r\n  };\r\n\r\n  /**\r\n   * Handles Save & Next button - save to Redux and navigate\r\n   */\r\n  const handleSaveAndNext = () => {\r\n    // Set loading state\r\n    setIsSaving(true);\r\n\r\n    // Save local skills to Redux\r\n    dispatch(\r\n      setSkillsData({\r\n        careerSkills: localCareerSkills,\r\n      })\r\n    );\r\n    setIsSaving(false);\r\n    // Navigate to next page\r\n    router.push(ROUTES.JOBS.ROLE_BASED_SKILLS);\r\n  };\r\n\r\n  return (\r\n    <div className={style.job_page}>\r\n      <div className=\"container\">\r\n        <div className=\"common-page-header\">\r\n          <div className=\"common-page-head-section\">\r\n            <div className=\"main-heading\">\r\n              <h2>\r\n                <BackArrowIcon\r\n                  onClick={() => {\r\n                    router.push(ROUTES.JOBS.GENERATE_JOB);\r\n                  }}\r\n                />\r\n                {tJobRequirement(\"top_performance_based_skills\")}\r\n                <span> {jobDetails?.title || \"\"}</span>\r\n              </h2>\r\n            </div>\r\n            <p className=\"description\">\r\n              {tCommon(\"performance_skills_part1\")} <span>{jobDetails?.title || \"\"} </span> {tCommon(\"performance_skills_part2\")}{\" \"}\r\n              <strong className=\"color-primary\">{tCommon(\"performance_skills_part3\")}</strong>.\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"inner-section career-based-skills\">\r\n          <h3 className={style.inner_heading}>\r\n            {tCommon(\"top\")} <span>{tCommon(\"carrer_based_skills\")}</span>\r\n          </h3>\r\n          <div className=\"row g-4\">\r\n            {localCareerSkills.length > 0 ? (\r\n              localCareerSkills.map((skill: ISkillData, index: number) => (\r\n                <div className=\"col-md-4\" key={index}>\r\n                  <div className=\"career-skill-card\">\r\n                    <div className=\"head\">\r\n                      <h3>{skill.name || `Career Skill ${index + 1}`}</h3>\r\n                      <Button className=\"clear-btn p-0 m-0\" onClick={() => handleEditClick(skill, index)}>\r\n                        <EditSkillIcon className=\"right-img\" />\r\n                      </Button>\r\n                    </div>\r\n                    <div className=\"skill-content\">\r\n                      <p>{skill.description || tCommon(\"no_description_available\")}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <div className=\"col-12\">\r\n                <NoDataFoundIcon width={300} height={300} />\r\n                <p> {tCommon(\"no_carrer_skills_available\")} </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"button-align py-5\">\r\n          <Button className=\"primary-btn rounded-md\" onClick={handleSaveAndNext} disabled={isSaving}>\r\n            {tCommon(\"save_and_next\")} {isSaving && <Loader />}\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Edit/Add Skill Modal */}\r\n        {showEditModal && (\r\n          <Modal\r\n            title={\"Edit Career Skill\"}\r\n            isOpen={showEditModal}\r\n            onClose={() => {\r\n              // Close modal and reset states\r\n              setShowEditModal(false);\r\n              setFormValues({ name: \"\", description: \"\" });\r\n              setFormErrors({ name: \"\", description: \"\" }); // Clear validation errors\r\n            }}\r\n            size=\"md\"\r\n          >\r\n            <div className=\"p-4\">\r\n              <div className=\"form-group mb-4\">\r\n                <label htmlFor=\"skillName\" className=\"form-label fw-bold mb-2\">\r\n                  {tCommon(\"skill_name\")}\r\n                </label>\r\n                <div className=\"input-wrapper\">\r\n                  <CommonInput\r\n                    id=\"skillName\"\r\n                    value={formValues.name}\r\n                    onChange={(e) => handleInputChange(\"name\", e.target.value)}\r\n                    placeholder=\"Enter skill name\"\r\n                    className={`form-control ${formErrors.name ? \"is-invalid\" : \"\"}`}\r\n                  />\r\n                  {formErrors.name && <div className=\"invalid-feedback d-block auth-msg error\">{formErrors.name}</div>}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"form-group mb-4\">\r\n                <label htmlFor=\"skillDescription\" className=\"form-label fw-bold  mb-2\">\r\n                  {tCommon(\"discription\")}\r\n                </label>\r\n                <div className=\"input-wrapper\">\r\n                  <textarea\r\n                    id=\"skillDescription\"\r\n                    value={formValues.description}\r\n                    onChange={(e) => handleInputChange(\"description\", e.target.value)}\r\n                    placeholder=\"Enter skill description\"\r\n                    className={`form-control ${formErrors.description ? \"is-invalid\" : \"\"}`}\r\n                    rows={5}\r\n                  />\r\n                  {formErrors.description && <div className=\"invalid-feedback d-block auth-msg error\">{formErrors.description}</div>}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-end mt-5\">\r\n                <Button className=\"primary-btn w-100 rounded-md\" onClick={handleSaveSkill}>\r\n                  {tCommon(\"save_cahnges\")}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </Modal>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CareerBasedSkills;\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;AACrB;AAEA,qBAAqB;AACrB;AACA;AAEA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AAEA,0CAA0C;AAC1C;AACA;AAEA;AAEA,MAAM;AACN;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAEA,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IACxC,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAC9B,qCAAqC;IACrC,uDAAuD;IACvD,MAAM,oBAAoB,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE,2IAAA,CAAA,qBAAkB,KAAK,EAAE;IAC/D,MAAM,aAAa,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE,4IAAA,CAAA,mBAAgB;IAE/C,iBAAiB;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,mEAAmE;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE3E,mBAAmB;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;QAC5F,OAAO,CAAC;QACR,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,aAAa;IACf;IAEA;;;;;;;GAOC,GAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,EAAE;QAClG;sCAAG,EAAE;IAEL;;;;GAIC,GACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,qBAAqB;mBAAI;aAAkB;QAC7C;sCAAG;QAAC;KAAkB;IAEtB;;;;;GAKC,GACD,MAAM,kBAAkB,CAAC,OAAmB;QAC1C,kCAAkC;QAClC,gBAAgB;YAAE;YAAO,OAAO;gBAAE,GAAG,KAAK;YAAC;QAAE;QAE7C,gEAAgE;QAChE,8DAA8D;QAC9D,cAAc;YACZ,MAAM,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,GAAG;YACrE,aAAa,MAAM,WAAW,KAAK,aAAa,MAAM,WAAW,KAAK,OAAO,MAAM,WAAW,GAAG;QACnG;QAEA,iBAAiB;IACnB;IAEA;;;GAGC,GAED;;;;GAIC,GACD,MAAM,oBAAoB,CAAC,OAA+B;QACxD,qBAAqB;QACrB,MAAM,oBAAoB;YACxB,GAAG,UAAU;YACb,CAAC,MAAM,EAAE;QACX;QACA,cAAc;QAEd,yCAAyC;QACzC,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,cAAc;gBACZ,GAAG,UAAU;gBACb,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB;QACtB,uBAAuB;QACvB,MAAM,SAAS;YACb,MAAM;YACN,aAAa;QACf;QAEA,IAAI,CAAC,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,IAAI,OAAO,IAAI;YACrD,OAAO,IAAI,GAAG,EAAE;QAClB,OAAO,IAAI,QAAQ,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,KAAK;YAC/C,OAAO,IAAI,GAAG,EAAE;QAClB,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC5C,OAAO,IAAI,GAAG,EAAE;QAClB,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAC7C,OAAO,IAAI,GAAG,EAAE;QAClB;QAEA,IAAI,CAAC,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,IAAI,OAAO,IAAI;YACnE,OAAO,WAAW,GAAG,EAAE;QACzB,OAAO,IAAI,WAAW,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK;YACrD,OAAO,WAAW,GAAG,EAAE;QACzB,OAAO,IAAI,WAAW,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YACnD,OAAO,WAAW,GAAG,EAAE;QACzB,OAAO,IAAI,QAAQ,IAAI,CAAC,WAAW,WAAW,CAAC,IAAI,KAAK;YACtD,OAAO,WAAW,GAAG,EAAE;QACzB;QAEA,8DAA8D;QAC9D,IAAI,OAAO,IAAI,IAAI,OAAO,WAAW,EAAE;YACrC,cAAc;YACd;QACF;QAEA,8DAA8D;QAC9D,MAAM,eAA2B;YAC/B,MAAM,WAAW,IAAI,CAAC,IAAI;YAC1B,aAAa,WAAW,WAAW,CAAC,IAAI;QAC1C;QAEA,iDAAiD;QACjD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;YACjD,sCAAsC;YACtC,IAAI,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,aAAa,KAAK,EAAE;YAE3D,IAAI,iBAAiB,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,OAAO,aAAa,IAAI,CAAC,WAAW,IAAI;gBAC/E,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;gBACpB;YACF;QACF;QAEA,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG;YAC7B,kDAAkD;YAClD,MAAM,gBAAgB;mBAAI;aAAkB;YAC5C,aAAa,CAAC,aAAa,KAAK,CAAC,GAAG;YACpC,qBAAqB;QACvB;QAEA,8BAA8B;QAC9B,iBAAiB;QACjB,gBAAgB;YAAE,OAAO,CAAC;YAAG,OAAO;QAAK;QACzC,cAAc;YAAE,MAAM;YAAI,aAAa;QAAG;QAC1C,cAAc;YAAE,MAAM;YAAI,aAAa;QAAG,IAAI,0BAA0B;IAC1E;IAEA;;GAEC,GACD,MAAM,oBAAoB;QACxB,oBAAoB;QACpB,YAAY;QAEZ,6BAA6B;QAC7B,SACE,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD,EAAE;YACZ,cAAc;QAChB;QAEF,YAAY;QACZ,wBAAwB;QACxB,OAAO,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB;IAC3C;IAEA,qBACE,6LAAC;QAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,QAAQ;kBAC5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC,uJAAA,CAAA,UAAa;4CACZ,SAAS;gDACP,OAAO,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY;4CACtC;;;;;;wCAED,gBAAgB;sDACjB,6LAAC;;gDAAK;gDAAE,YAAY,SAAS;;;;;;;;;;;;;;;;;;0CAGjC,6LAAC;gCAAE,WAAU;;oCACV,QAAQ;oCAA4B;kDAAC,6LAAC;;4CAAM,YAAY,SAAS;4CAAG;;;;;;;oCAAQ;oCAAE,QAAQ;oCAA6B;kDACpH,6LAAC;wCAAO,WAAU;kDAAiB,QAAQ;;;;;;oCAAqC;;;;;;;;;;;;;;;;;;8BAItF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAW,4JAAA,CAAA,UAAK,CAAC,aAAa;;gCAC/B,QAAQ;gCAAO;8CAAC,6LAAC;8CAAM,QAAQ;;;;;;;;;;;;sCAElC,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,OAAmB,sBACxC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAI,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,QAAQ,GAAG;;;;;;kEAC9C,6LAAC,+IAAA,CAAA,UAAM;wDAAC,WAAU;wDAAoB,SAAS,IAAM,gBAAgB,OAAO;kEAC1E,cAAA,6LAAC,uJAAA,CAAA,UAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG7B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAG,MAAM,WAAW,IAAI,QAAQ;;;;;;;;;;;;;;;;;mCATR;;;;0DAejC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yJAAA,CAAA,UAAe;wCAAC,OAAO;wCAAK,QAAQ;;;;;;kDACrC,6LAAC;;4CAAE;4CAAE,QAAQ;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+IAAA,CAAA,UAAM;wBAAC,WAAU;wBAAyB,SAAS;wBAAmB,UAAU;;4BAC9E,QAAQ;4BAAiB;4BAAE,0BAAY,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;gBAKlD,+BACC,6LAAC,8IAAA,CAAA,UAAK;oBACJ,OAAO;oBACP,QAAQ;oBACR,SAAS;wBACP,+BAA+B;wBAC/B,iBAAiB;wBACjB,cAAc;4BAAE,MAAM;4BAAI,aAAa;wBAAG;wBAC1C,cAAc;4BAAE,MAAM;4BAAI,aAAa;wBAAG,IAAI,0BAA0B;oBAC1E;oBACA,MAAK;8BAEL,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAY,WAAU;kDAClC,QAAQ;;;;;;kDAEX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gJAAA,CAAA,cAAW;gDACV,IAAG;gDACH,OAAO,WAAW,IAAI;gDACtB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;gDACZ,WAAW,CAAC,aAAa,EAAE,WAAW,IAAI,GAAG,eAAe,IAAI;;;;;;4CAEjE,WAAW,IAAI,kBAAI,6LAAC;gDAAI,WAAU;0DAA2C,WAAW,IAAI;;;;;;;;;;;;;;;;;;0CAIjG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAmB,WAAU;kDACzC,QAAQ;;;;;;kDAEX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,IAAG;gDACH,OAAO,WAAW,WAAW;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gDAChE,aAAY;gDACZ,WAAW,CAAC,aAAa,EAAE,WAAW,WAAW,GAAG,eAAe,IAAI;gDACvE,MAAM;;;;;;4CAEP,WAAW,WAAW,kBAAI,6LAAC;gDAAI,WAAU;0DAA2C,WAAW,WAAW;;;;;;;;;;;;;;;;;;0CAI/G,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+IAAA,CAAA,UAAM;oCAAC,WAAU;oCAA+B,SAAS;8CACvD,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;GAhTS;;QACQ,qIAAA,CAAA,YAAS;QACP,4JAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;QACD,yMAAA,CAAA,kBAAe;QACvB,yMAAA,CAAA,kBAAe;QAGL,4JAAA,CAAA,cAAW;QAClB,4JAAA,CAAA,cAAW;;;KATvB;uCAkTM", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/career-based-skills/page.tsx"], "sourcesContent": ["\"use client\";\nimport CareerBasedSkills from \"@/components/views/jobRequirement/CareerBasedSkills\";\nimport React from \"react\";\n\nconst page = () => {\n  return (\n    <div>\n      <CareerBasedSkills />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO;IACX,qBACE,6LAAC;kBACC,cAAA,6LAAC,qKAAA,CAAA,UAAiB;;;;;;;;;;AAGxB;uCAEe", "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}