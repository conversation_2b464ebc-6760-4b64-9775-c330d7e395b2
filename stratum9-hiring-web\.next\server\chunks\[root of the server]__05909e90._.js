module.exports = {

"[externals]/next/dist/compiled/next-server/pages-api.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages-api.runtime.dev.js", () => require("next/dist/compiled/next-server/pages-api.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next-auth [external] (next-auth, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next-auth", () => require("next-auth"));

module.exports = mod;
}}),
"[externals]/next-auth/providers/credentials [external] (next-auth/providers/credentials, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next-auth/providers/credentials", () => require("next-auth/providers/credentials"));

module.exports = mod;
}}),
"[project]/src/constants/endpoint.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// import config from "@/config/config";
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const URL = ("TURBOPACK compile-time value", "http://localhost:3001/api/v1");
const endpoint = {
    auth: {
        SIGNIN: `${URL}/auth/sign-in`,
        VERIFY_OTP: `${URL}/auth/verify-otp`,
        RESEND_OTP: `${URL}/auth/resend-otp`,
        FORGOT_PASSWORD: `${URL}/auth/forgot-password`,
        RESET_PASSWORD: `${URL}/auth/reset-password`,
        DELETE_SESSION: `${URL}/auth/delete-session`,
        UPDATE_TIMEZONE: `${URL}/auth/update-timezone`
    },
    interview: {
        UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,
        GET_INTERVIEWS: `${URL}/interview/get-interviews`,
        GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,
        GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,
        UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,
        GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,
        GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,
        UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,
        ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,
        GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,
        GET_JOB_LIST: `${URL}/interview/get-job-list`,
        GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,
        END_INTERVIEW: `${URL}/interview/end-interview`,
        CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`
    },
    common: {
        REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,
        GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`
    },
    jobRequirements: {
        GENERATE_SKILL: `${URL}/jobs/generate-skills`,
        UPLOAD_URL: `${URL}/jobs/upload-url`,
        PARSE_PDF: `${URL}/jobs/parse-pdf`,
        GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,
        GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,
        SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,
        GET_JOBS_META: `${URL}/jobs/get-jobs-meta`,
        UPDATE_JOB_STATUS: "/jobs/updateJob",
        GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,
        UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,
        GENERATE_PDF: `${URL}/jobs/generate-pdf`
    },
    Dashboard: {
        GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`
    },
    resumeScreen: {
        MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,
        GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,
        GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,
        CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`
    },
    employee: {
        ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,
        GET_EMPLOYEES: `${URL}/employee-management`,
        GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,
        UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,
        UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,
        // this task is for future use
        // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original
        DELETE_EMPLOYEE: `${URL}/employee-management/dummy`,
        UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`
    },
    userprofile: {
        GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,
        UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`
    },
    roles: {
        GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,
        GET_ROLES: `${URL}/access-management/user-roles`,
        ADD_USER_ROLE: `${URL}/access-management/add-user-role`,
        UPDATE_USER_ROLE: `${URL}/access-management/user-role`,
        DELETE_USER_ROLE: `${URL}/access-management/user-role`,
        GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,
        GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,
        UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,
        USER_PERMISSIONS: `${URL}/access-management/user-permissions`
    },
    notification: {
        UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,
        GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,
        DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,
        GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`
    },
    departments: {
        GET_DEPARTMENTS: `${URL}/employee-management/departments`,
        ADD_DEPARTMENT: `${URL}/employee-management/add-department`,
        UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,
        DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`
    },
    assessment: {
        CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,
        GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,
        CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,
        SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,
        SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,
        GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,
        SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,
        GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,
        VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,
        GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`
    },
    candidatesApplication: {
        ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,
        PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`,
        GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,
        GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,
        ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,
        GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`,
        UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`,
        GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:applicationId`,
        GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:jobApplicationId`,
        GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:jobApplicationId`,
        GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`
    },
    subscription: {
        GET_ALL_PLANS: `${URL}/subscription/all`,
        GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,
        CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,
        GET_TRANSACTIONS: `${URL}/subscription/transactions`,
        BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`,
        JOB_POSTING_QUOTA: `${URL}/subscription/job-posting-quota`
    }
};
const __TURBOPACK__default__export__ = endpoint;
}}),
"[project]/src/constants/routes.ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BEFORE_LOGIN_ROUTES": (()=>BEFORE_LOGIN_ROUTES),
    "UNRESTRICTED_ROUTES": (()=>UNRESTRICTED_ROUTES),
    "default": (()=>__TURBOPACK__default__export__)
});
const ROUTES = {
    LOGIN: "/login",
    FORGOT_PASSWORD: "/forgot-password",
    VERIFY: "/verify",
    RESET_PASSWORD: "/reset-password",
    CANDIDATE_ASSESSMENT: "/candidate-assessment",
    DASHBOARD: "/dashboard",
    HOME: "/",
    BUY_SUBSCRIPTION: "/buy-subscription",
    PROFILE: {
        MY_PROFILE: "/my-profile"
    },
    SUBSCRIPTIONS: {
        SUCCESS: "/subscriptions/success",
        CANCEL: "/subscriptions/cancel"
    },
    JOBS: {
        CAREER_BASED_SKILLS: "/career-based-skills",
        ROLE_BASED_SKILLS: "/role-based-skills",
        CULTURE_BASED_SKILLS: "/culture-based-skills",
        GENERATE_JOB: "/generate-job",
        EDIT_SKILLS: "/edit-skills",
        HIRING_TYPE: "/hiring-type",
        JOB_EDITOR: "/job-editor",
        ACTIVE_JOBS: "/active-jobs",
        CANDIDATE_PROFILE: "/candidate-profile",
        ARCHIVE: "/archive"
    },
    SCREEN_RESUME: {
        MANUAL_CANDIDATE_UPLOAD: "/manual-upload-resume",
        CANDIDATE_QUALIFICATION: "/candidate-qualification",
        CANDIDATE_LIST: "/candidates-list",
        CANDIDATES: "/candidates"
    },
    INTERVIEW: {
        ADD_CANDIDATE_INFO: "/additional-submission",
        SCHEDULE_INTERVIEW: "/schedule-interview",
        PRE_INTERVIEW_QUESTIONS_OVERVIEW: "/pre-interview-questions-overview",
        INTERVIEW_QUESTION: "/interview-question",
        CALENDAR: "/calendar",
        INTERVIEW_SUMMARY: "/interview-summary"
    },
    ROLE_EMPLOYEES: {
        ROLES_PERMISSIONS: "/roles-permissions",
        EMPLOYEE_MANAGEMENT: "/employee-management",
        EMPLOYEE_MANAGEMENT_DETAIL: "/employee-management-detail",
        ADD_EMPLOYEE: "/add-employees",
        ADD_DEPARTMENT: "/add-department"
    },
    FINAL_ASSESSMENT: {
        FINAL_ASSESSMENT: "/final-assessment"
    }
};
const BEFORE_LOGIN_ROUTES = [
    ROUTES.LOGIN,
    ROUTES.FORGOT_PASSWORD,
    ROUTES.VERIFY,
    ROUTES.RESET_PASSWORD,
    ROUTES.CANDIDATE_ASSESSMENT
];
const UNRESTRICTED_ROUTES = [
    ROUTES.SUBSCRIPTIONS.SUCCESS,
    ROUTES.SUBSCRIPTIONS.CANCEL
];
const __TURBOPACK__default__export__ = ROUTES;
}}),
"[project]/src/pages/api/auth/[...nextauth].ts [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$auth__$5b$external$5d$__$28$next$2d$auth$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next-auth [external] (next-auth, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$auth$2f$providers$2f$credentials__$5b$external$5d$__$28$next$2d$auth$2f$providers$2f$credentials$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next-auth/providers/credentials [external] (next-auth/providers/credentials, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [api] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [api] (ecmascript)");
;
;
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$auth__$5b$external$5d$__$28$next$2d$auth$2c$__cjs$29$__["default"])({
    providers: [
        (0, __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$auth$2f$providers$2f$credentials__$5b$external$5d$__$28$next$2d$auth$2f$providers$2f$credentials$2c$__cjs$29$__["default"])({
            name: "Credentials",
            credentials: {
                email: {
                    label: "Email",
                    type: "email"
                },
                password: {
                    label: "Password",
                    type: "password"
                }
            },
            async authorize (credentials) {
                const { email, password } = credentials;
                const res = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$api$5d$__$28$ecmascript$29$__["default"].auth.SIGNIN, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        email,
                        password
                    })
                });
                const user = await res.json();
                if (user) {
                    return user;
                } else return null;
            }
        })
    ],
    session: {
        maxAge: 3 * 24 * 60 * 60
    },
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$api$5d$__$28$ecmascript$29$__["default"].LOGIN
    },
    callbacks: {
        async jwt ({ token, user }) {
            return {
                ...token,
                ...user
            };
        },
        async session ({ session, token }) {
            session.user = token;
            return session;
        }
    }
});
}}),
"[project]/node_modules/next/dist/esm/server/route-modules/pages-api/module.compiled.js [api] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time truthy", 1) {
        module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/pages-api.runtime.dev.js [external] (next/dist/compiled/next-server/pages-api.runtime.dev.js, cjs)");
    } else {
        "TURBOPACK unreachable";
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/esm/server/route-kind.js [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RouteKind": (()=>RouteKind)
});
var RouteKind = /*#__PURE__*/ function(RouteKind) {
    /**
   * `PAGES` represents all the React pages that are under `pages/`.
   */ RouteKind["PAGES"] = "PAGES";
    /**
   * `PAGES_API` represents all the API routes under `pages/api/`.
   */ RouteKind["PAGES_API"] = "PAGES_API";
    /**
   * `APP_PAGE` represents all the React pages that are under `app/` with the
   * filename of `page.{j,t}s{,x}`.
   */ RouteKind["APP_PAGE"] = "APP_PAGE";
    /**
   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the
   * filename of `route.{j,t}s{,x}`.
   */ RouteKind["APP_ROUTE"] = "APP_ROUTE";
    /**
   * `IMAGE` represents all the images that are generated by `next/image`.
   */ RouteKind["IMAGE"] = "IMAGE";
    return RouteKind;
}({}); //# sourceMappingURL=route-kind.js.map
}}),
"[project]/node_modules/next/dist/esm/build/templates/helpers.js [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * Hoists a name from a module or promised module.
 *
 * @param module the module to hoist the name from
 * @param name the name to hoist
 * @returns the value on the module (or promised module)
 */ __turbopack_context__.s({
    "hoist": (()=>hoist)
});
function hoist(module, name) {
    // If the name is available in the module, return it.
    if (name in module) {
        return module[name];
    }
    // If a property called `then` exists, assume it's a promise and
    // return a promise that resolves to the name.
    if ('then' in module && typeof module.then === 'function') {
        return module.then((mod)=>hoist(mod, name));
    }
    // If we're trying to hoise the default export, and the module is a function,
    // return the module itself.
    if (typeof module === 'function' && name === 'default') {
        return module;
    }
    // Otherwise, return undefined.
    return undefined;
} //# sourceMappingURL=helpers.js.map
}}),
"[project]/node_modules/next/dist/esm/build/templates/pages-api.js { INNER_PAGE => \"[project]/src/pages/api/auth/[...nextauth].ts [api] (ecmascript)\" } [api] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>__TURBOPACK__default__export__),
    "routeModule": (()=>routeModule)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$route$2d$modules$2f$pages$2d$api$2f$module$2e$compiled$2e$js__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/route-modules/pages-api/module.compiled.js [api] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$route$2d$kind$2e$js__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/route-kind.js [api] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$templates$2f$helpers$2e$js__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/build/templates/helpers.js [api] (ecmascript)");
// Import the userland code.
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$pages$2f$api$2f$auth$2f5b2e2e2e$nextauth$5d2e$ts__$5b$api$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/pages/api/auth/[...nextauth].ts [api] (ecmascript)");
;
;
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$templates$2f$helpers$2e$js__$5b$api$5d$__$28$ecmascript$29$__["hoist"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$pages$2f$api$2f$auth$2f5b2e2e2e$nextauth$5d2e$ts__$5b$api$5d$__$28$ecmascript$29$__, 'default');
const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$build$2f$templates$2f$helpers$2e$js__$5b$api$5d$__$28$ecmascript$29$__["hoist"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$pages$2f$api$2f$auth$2f5b2e2e2e$nextauth$5d2e$ts__$5b$api$5d$__$28$ecmascript$29$__, 'config');
const routeModule = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$route$2d$modules$2f$pages$2d$api$2f$module$2e$compiled$2e$js__$5b$api$5d$__$28$ecmascript$29$__["PagesAPIRouteModule"]({
    definition: {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$route$2d$kind$2e$js__$5b$api$5d$__$28$ecmascript$29$__["RouteKind"].PAGES_API,
        page: "/api/auth/[...nextauth]",
        pathname: "/api/auth/[...nextauth]",
        // The following aren't used in production.
        bundlePath: '',
        filename: ''
    },
    userland: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$pages$2f$api$2f$auth$2f5b2e2e2e$nextauth$5d2e$ts__$5b$api$5d$__$28$ecmascript$29$__
}); //# sourceMappingURL=pages-api.js.map
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__05909e90._.js.map