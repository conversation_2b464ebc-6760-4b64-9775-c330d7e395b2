(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root of the server]__9de05221._.js", {

"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/src/constants/commonConstants.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACCESS_TOKEN_KEY": (()=>ACCESS_TOKEN_KEY),
    "ACTIVE": (()=>ACTIVE),
    "ASSESSMENT_INSTRUCTIONS": (()=>ASSESSMENT_INSTRUCTIONS),
    "DEFAULT_LIMIT": (()=>DEFAULT_LIMIT),
    "DEFAULT_MCQ_OPTIONS": (()=>DEFAULT_MCQ_OPTIONS),
    "DEFAULT_OFFSET": (()=>DEFAULT_OFFSET),
    "DEFAULT_TRUE_FALSE_OPTIONS": (()=>DEFAULT_TRUE_FALSE_OPTIONS),
    "EMAIL_REGEX": (()=>EMAIL_REGEX),
    "EMPTY_CONTENT_PATTERNS": (()=>EMPTY_CONTENT_PATTERNS),
    "FILE_EXTENSION": (()=>FILE_EXTENSION),
    "IMAGE_EXTENSIONS": (()=>IMAGE_EXTENSIONS),
    "INTERVIEW_SCHEDULE_ROUND_TYPE": (()=>INTERVIEW_SCHEDULE_ROUND_TYPE),
    "MAX_IMAGE_SIZE": (()=>MAX_IMAGE_SIZE),
    "MessageType": (()=>MessageType),
    "NAME_REGEX": (()=>NAME_REGEX),
    "ONE_TO_ONE_INTERVIEW_INSTRUCTIONS": (()=>ONE_TO_ONE_INTERVIEW_INSTRUCTIONS),
    "OPTION_ID": (()=>OPTION_ID),
    "PASSWORD_REGEX": (()=>PASSWORD_REGEX),
    "PDF_ADDITIONAL_SUBMISSION_LIMIT": (()=>PDF_ADDITIONAL_SUBMISSION_LIMIT),
    "PDF_FILE_NAME": (()=>PDF_FILE_NAME),
    "PDF_FILE_SIZE_LIMIT": (()=>PDF_FILE_SIZE_LIMIT),
    "PDF_FILE_TYPE": (()=>PDF_FILE_TYPE),
    "PERMISSION": (()=>PERMISSION),
    "PERMISSIONS_COOKIES_KEY": (()=>PERMISSIONS_COOKIES_KEY),
    "PLAN_CONFIG": (()=>PLAN_CONFIG),
    "PLAN_NAMES": (()=>PLAN_NAMES),
    "QUESTION_TYPE": (()=>QUESTION_TYPE),
    "QUESTION_TYPES": (()=>QUESTION_TYPES),
    "QuestionType": (()=>QuestionType),
    "S3_PATHS": (()=>S3_PATHS),
    "SKILL_CONSTANTS": (()=>SKILL_CONSTANTS),
    "STANDARD_LIMIT": (()=>STANDARD_LIMIT),
    "ScheduleInterviewFormSubmissionType": (()=>ScheduleInterviewFormSubmissionType),
    "TOKEN_EXPIRED": (()=>TOKEN_EXPIRED),
    "VIDEO_CALL_INTERVIEW_INSTRUCTIONS": (()=>VIDEO_CALL_INTERVIEW_INSTRUCTIONS),
    "commonConstants": (()=>commonConstants),
    "initialState": (()=>initialState)
});
const ACCESS_TOKEN_KEY = "__ATK__";
const EMAIL_REGEX = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;
const NAME_REGEX = /^[a-zA-Z0-9\s.'-]+$/;
const MAX_IMAGE_SIZE = 5242880;
const ScheduleInterviewFormSubmissionType = {
    SCHEDULE: "schedule",
    UPDATE: "update"
};
const S3_PATHS = {
    PROFILE_IMAGE: "profile-images/:path"
};
const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [
    "Arrive at the interview location on time with a government-issued ID.",
    "Ensure your phone is on silent mode and distractions are minimized.",
    "Bring a printed copy of your resume and any supporting documents.",
    "Dress professionally and maintain proper body language.",
    "Listen carefully, answer honestly, and ask for clarification if needed.",
    "Respect the interview flow and do not interrupt the interviewer.",
    "Take brief notes if necessary, but focus on active conversation.",
    "If you need assistance or face any issues, notify the interview coordinator."
];
const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [
    "Join the interview on time using the link provided.",
    "Ensure a stable internet connection and a quiet, well-lit space.",
    "Test your camera, microphone, and audio settings in advance.",
    "Keep your video on unless instructed otherwise by the interviewer.",
    "Minimize background noise and avoid multitasking during the session.",
    "Use headphones if possible for better audio clarity.",
    "Be attentive, respond clearly, and maintain professional posture.",
    "Contact support if you face technical difficulties before or during the interview."
];
const PERMISSION = {
    CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
    SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
    VIEW_HIRED_CANDIDATES: "view-hired-candidates",
    ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
    ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
    MANUAL_RESUME_SCREENING: "manual-resume-screening",
    EDIT_SCHEDULED_INTERVIEWS: "edit-scheduled-interviews",
    ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
    ADD_OR_EDIT_INTERVIEW_NOTES: "add-or-edit-interview-notes",
    MANAGE_TOP_CANDIDATES: "manage-top-candidates",
    MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
    CREATE_FINAL_ASSESSMENT: "create-final-assessment",
    VIEW_FINAL_ASSESSMENT: "view-final-assessment",
    VIEW_CANDIDATE_PROFILE_SUMMARY: "view-candidate-profile-summary",
    HIRE_CANDIDATE: "hire-candidate",
    CREATE_NEW_ROLE: "create-new-role",
    MANAGE_USER_PERMISSIONS: "manage-user-permissions",
    CREATE_NEW_DEPARTMENT: "create-new-department",
    ADD_INTERVIEW_PARTICIPANTS: "add-interview-participants",
    VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
    MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
    VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
    VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews"
};
const PLAN_CONFIG = {
    free: {
        maxCandidates: 1,
        showAddButton: false,
        planDisplayName: "Free"
    },
    pro: {
        maxCandidates: 1,
        showAddButton: false,
        planDisplayName: "Pro"
    },
    growth: {
        maxCandidates: 5,
        showAddButton: true,
        planDisplayName: "Growth"
    },
    enterprise: {
        maxCandidates: 5,
        showAddButton: true,
        planDisplayName: "Enterprise"
    }
};
const PLAN_NAMES = {
    FREE: "Free",
    PRO: "Pro",
    GROWTH: "Growth",
    ENTERPRISE: "Enterprise"
};
const SKILL_CONSTANTS = {
    REQUIRED_ROLE_SKILLS: 10,
    REQUIRED_CULTURE_SKILLS: 5
};
const commonConstants = {
    finalAssessmentId: "finalAssessmentId",
    token: "token",
    isShared: "isShared",
    isSubmitted: "isSubmitted",
    jobId: "jobId",
    jobApplicationId: "jobApplicationId"
};
const QuestionType = {
    MCQ: "mcq",
    TRUE_FALSE: "true_false"
};
const OPTION_ID = {
    A: "A",
    B: "B",
    C: "C",
    D: "D",
    TRUE: "true",
    FALSE: "false"
};
const QUESTION_TYPE = {
    MCQ: "mcq",
    TRUE_FALSE: "true_false"
};
const DEFAULT_MCQ_OPTIONS = [
    {
        id: OPTION_ID.A,
        text: ""
    },
    {
        id: OPTION_ID.B,
        text: ""
    },
    {
        id: OPTION_ID.C,
        text: ""
    },
    {
        id: OPTION_ID.D,
        text: ""
    }
];
const DEFAULT_TRUE_FALSE_OPTIONS = [
    {
        id: OPTION_ID.TRUE,
        text: "True"
    },
    {
        id: OPTION_ID.FALSE,
        text: "False"
    }
];
const INTERVIEW_SCHEDULE_ROUND_TYPE = [
    {
        label: "One-On-One",
        value: "One-On-One"
    },
    {
        label: "Video Call",
        value: "Video Call"
    }
];
const QUESTION_TYPES = {
    ROLE_SPECIFIC: "role_specific",
    CULTURE_SPECIFIC: "culture_specific",
    CAREER_BASED: "career_based"
};
const EMPTY_CONTENT_PATTERNS = [
    "<p><br></p>",
    "<p></p>",
    "<div><br></div>",
    "<div></div>",
    "<p>&nbsp;</p>"
];
const initialState = {
    title: "",
    employment_type: "",
    department_id: "",
    salary_range: "",
    salary_cycle: "",
    location_type: "",
    state: "",
    city: "",
    role_overview: "",
    experience_level: "",
    responsibilities: "",
    educations_requirement: "",
    certifications: undefined,
    skills_and_software_expertise: "",
    experience_required: "",
    ideal_candidate_traits: "",
    about_company: "",
    perks_benefits: undefined,
    tone_style: "",
    additional_info: undefined,
    compliance_statement: [],
    show_compliance: false,
    hiring_type: ""
};
const FILE_EXTENSION = [
    "pdf",
    "plain",
    "csv",
    "vnd.ms-excel.sheet.macroEnabled.12",
    "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "vnd.openxmlformats-officedocument.wordprocessingml.document",
    "vnd.openxmlformats-officedocument.presentationml.presentation"
];
const ACTIVE = "active";
const TOKEN_EXPIRED = "Session Expired! Please log in again.";
const DEFAULT_LIMIT = 15;
const STANDARD_LIMIT = 18;
const DEFAULT_OFFSET = 0;
var MessageType = /*#__PURE__*/ function(MessageType) {
    MessageType["success"] = "success";
    MessageType["error"] = "error";
    return MessageType;
}({});
const IMAGE_EXTENSIONS = [
    "png",
    "jpg",
    "jpeg",
    "gif",
    "webp"
];
const ASSESSMENT_INSTRUCTIONS = {
    instructions: [
        "Do not refresh or close the browser",
        "Check your internet connection",
        "Ensure a distraction-free environment",
        "Click 'Submit' only once when finished",
        "Read each question carefully",
        "Manage your time efficiently",
        "Avoid any form of plagiarism",
        "Reach out to support if needed"
    ]
};
const PERMISSIONS_COOKIES_KEY = "permissions_data";
const PDF_FILE_NAME = "pdf";
const PDF_FILE_TYPE = "application/pdf";
const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;
const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;
}}),
"[project]/src/constants/routes.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BEFORE_LOGIN_ROUTES": (()=>BEFORE_LOGIN_ROUTES),
    "UNRESTRICTED_ROUTES": (()=>UNRESTRICTED_ROUTES),
    "default": (()=>__TURBOPACK__default__export__)
});
const ROUTES = {
    LOGIN: "/login",
    FORGOT_PASSWORD: "/forgot-password",
    VERIFY: "/verify",
    RESET_PASSWORD: "/reset-password",
    CANDIDATE_ASSESSMENT: "/candidate-assessment",
    DASHBOARD: "/dashboard",
    HOME: "/",
    BUY_SUBSCRIPTION: "/buy-subscription",
    PROFILE: {
        MY_PROFILE: "/my-profile"
    },
    SUBSCRIPTIONS: {
        SUCCESS: "/subscriptions/success",
        CANCEL: "/subscriptions/cancel"
    },
    JOBS: {
        CAREER_BASED_SKILLS: "/career-based-skills",
        ROLE_BASED_SKILLS: "/role-based-skills",
        CULTURE_BASED_SKILLS: "/culture-based-skills",
        GENERATE_JOB: "/generate-job",
        EDIT_SKILLS: "/edit-skills",
        HIRING_TYPE: "/hiring-type",
        JOB_EDITOR: "/job-editor",
        ACTIVE_JOBS: "/active-jobs",
        CANDIDATE_PROFILE: "/candidate-profile",
        ARCHIVE: "/archive"
    },
    SCREEN_RESUME: {
        MANUAL_CANDIDATE_UPLOAD: "/manual-upload-resume",
        CANDIDATE_QUALIFICATION: "/candidate-qualification",
        CANDIDATE_LIST: "/candidates-list",
        CANDIDATES: "/candidates"
    },
    INTERVIEW: {
        ADD_CANDIDATE_INFO: "/additional-submission",
        SCHEDULE_INTERVIEW: "/schedule-interview",
        PRE_INTERVIEW_QUESTIONS_OVERVIEW: "/pre-interview-questions-overview",
        INTERVIEW_QUESTION: "/interview-question",
        CALENDAR: "/calendar",
        INTERVIEW_SUMMARY: "/interview-summary"
    },
    ROLE_EMPLOYEES: {
        ROLES_PERMISSIONS: "/roles-permissions",
        EMPLOYEE_MANAGEMENT: "/employee-management",
        EMPLOYEE_MANAGEMENT_DETAIL: "/employee-management-detail",
        ADD_EMPLOYEE: "/add-employees",
        ADD_DEPARTMENT: "/add-department"
    },
    FINAL_ASSESSMENT: {
        FINAL_ASSESSMENT: "/final-assessment"
    }
};
const BEFORE_LOGIN_ROUTES = [
    ROUTES.LOGIN,
    ROUTES.FORGOT_PASSWORD,
    ROUTES.VERIFY,
    ROUTES.RESET_PASSWORD,
    ROUTES.CANDIDATE_ASSESSMENT
];
const UNRESTRICTED_ROUTES = [
    ROUTES.SUBSCRIPTIONS.SUCCESS,
    ROUTES.SUBSCRIPTIONS.CANCEL
];
const __TURBOPACK__default__export__ = ROUTES;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$jwt$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/jwt/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [middleware-edge] (ecmascript)");
;
;
;
;
// import { IUserData, Role } from "./interfaces/authInterfaces";
// Define session data structure with permissions
// type SessionData = {
//   data?: {
//     token?: string;
//     authData: { userData: IUserData; role: Role; permissions?: string[] };
//     [key: string]: unknown;
//   };
//   [key: string]: unknown;
// };
// Route permission mapping
const routePermissionMap = {
    // User role routes
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].ROLE_EMPLOYEES.ROLES_PERMISSIONS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_NEW_ROLE,
    // Archive route (contains both candidates and jobs tabs)
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.ARCHIVE]: [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].ARCHIVE_RESTORE_CANDIDATES,
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].ARCHIVE_RESTORE_JOB_POSTS
    ],
    // Manual Resume Screening routes - protect all paths including dynamic routes
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].MANUAL_RESUME_SCREENING,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD + "/[...path]"]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].MANUAL_RESUME_SCREENING,
    // Department management - these routes are accessed from employee-management
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_NEW_DEPARTMENT,
    // Job routes - require job post creation/editing permission
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.JOB_EDITOR]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.GENERATE_JOB]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.CAREER_BASED_SKILLS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.ROLE_BASED_SKILLS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.CULTURE_BASED_SKILLS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.EDIT_SKILLS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].JOBS.HIRING_TYPE]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].CREATE_OR_EDIT_JOB_POST,
    // Buy subscription routes
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].BUY_SUBSCRIPTION]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSION"].MANAGE_SUBSCRIPTIONS
};
// Helper function to normalize route path
const normalizePath = (path)=>{
    // Remove trailing slash except for root path
    return path === "/" ? path : path.replace(/\/$/, "");
};
// Helper function to convert Next.js dynamic route pattern to regex pattern
const routeToRegex = (pattern)=>{
    // Normalize and escape the pattern for regex
    const regexPattern = normalizePath(pattern)// Escape special regex characters except [ and ]
    .replace(/[.+?^${}()|\\\/]/g, "\\$&")// Replace [...param] catch-all routes
    .replace(/\[\.\.\.[^\]]+\]/g, ".*")// Replace [param] dynamic segments
    .replace(/\[[^\]]+\]/g, "[^/]+");
    return new RegExp(`^${regexPattern}$`);
};
// Pre-compile regex patterns for dynamic routes
const dynamicRoutePatterns = new Map(Object.entries(routePermissionMap).filter(([pattern])=>pattern.includes("[")).map(([pattern, permissions])=>[
        routeToRegex(pattern),
        permissions
    ])// Sort patterns by specificity (more specific routes first)
.sort((a, b)=>{
    const segmentsA = a[0].toString().split("/").length;
    const segmentsB = b[0].toString().split("/").length;
    return segmentsB - segmentsA;
}));
// Convert routePermissionMap to Map for faster lookups
const staticRoutePermissions = new Map(Object.entries(routePermissionMap).filter(([pattern])=>!pattern.includes("[")));
// Helper function to check if user has any of the required permissions
const hasAnyPermission = (userPermissions, required)=>{
    return Array.isArray(required) ? required.some((perm)=>userPermissions.includes(perm)) : userPermissions.includes(required);
};
// Helper function to check if a user has permission for a given route
const hasPermissionForRoute = (path, userPermissions)=>{
    console.log("userPermissions>>>>>>>", userPermissions);
    // Early return for empty permissions array
    if (!userPermissions.length) return false;
    // Check static routes first (faster lookup)
    const staticPermissions = staticRoutePermissions.get(path);
    if (staticPermissions) {
        return hasAnyPermission(userPermissions, staticPermissions);
    }
    // Check dynamic routes
    for (const [pattern, requiredPermissions] of dynamicRoutePatterns){
        console.log(`>>>>>>>>>>>>>Checking dynamic route: ${pattern} for path: ${path}`);
        console.log(">>>>>>>>>>>>>requiredPermissions", requiredPermissions);
        if (pattern.test(path)) {
            return hasAnyPermission(userPermissions, requiredPermissions);
        }
    }
    // No match found in either static or dynamic routes - allow unrestricted access
    return true;
};
// Helper function to parse cookie data
const parseCookieData = (cookieString, cookieName)=>{
    if (!cookieString) return null;
    const cookieValue = cookieString.split(";").find((c)=>c.trim().startsWith(`${cookieName}=`));
    if (!cookieValue) return null;
    try {
        // Extract the value part (after '=') and decode it
        const encodedValue = cookieValue.split("=")[1];
        if (!encodedValue) return null;
        // First decode the URL encoded string, then parse as JSON
        return JSON.parse(decodeURIComponent(encodedValue));
    } catch (error) {
        console.error(`Error parsing ${cookieName} cookie:`, error);
        return null;
    }
};
async function middleware(req) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$jwt$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getToken"])({
            req,
            secret: process.env.NEXTAUTH_SECRET
        });
        const isAuthenticated = !!session?.data;
        const path = req.nextUrl.pathname;
        const isPublicRoute = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["BEFORE_LOGIN_ROUTES"].includes(path);
        // Check if the current path is in the unrestricted routes list
        const isUnrestrictedRoute = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["UNRESTRICTED_ROUTES"].includes(path);
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        if (isAuthenticated) {
            if (isPublicRoute) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].DASHBOARD, req.url));
            } else {
                if (isUnrestrictedRoute) {
                    return response; // Allow unrestricted routes without permission check
                }
                const cookieHeader = req.headers.get("cookie");
                const authDataFromCookies = parseCookieData(cookieHeader, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PERMISSIONS_COOKIES_KEY"]);
                const userPermissions = authDataFromCookies?.length ? authDataFromCookies : [];
                // Check if user has permission for the route
                if (path === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].DASHBOARD || isUnrestrictedRoute || userPermissions.length && hasPermissionForRoute(path, userPermissions)) {
                    return response; // Allow access to the route if permissions are valid
                } else {
                    // If authenticated user doesn't have permission for the route
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].DASHBOARD, req.url));
                }
            }
        } else if (!isPublicRoute) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].LOGIN, req.url));
        }
        return response;
    } catch (error) {
        console.error("Error in middleware:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].error();
    }
}
const config = {
    matcher: [
        "/((?!api|_next/static|_next/image|favicon.ico).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__9de05221._.js.map