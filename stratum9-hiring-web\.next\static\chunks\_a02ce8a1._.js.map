{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\n// Dynamic uploading messages for job generation\nexport const JOB_GENERATION_UPLOAD_MESSAGES = [\n  \"Analyzing your job description...\",\n  \"Extracting key requirements...\",\n  \"Processing document content...\",\n  \"Identifying skills and qualifications...\",\n  \"Parsing job details...\",\n  \"Almost ready...\",\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB;AAGxB,MAAM,iCAAiC;IAC5C;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/screenResumeServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { http } from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { IUploadManualCandidate } from \"@/interfaces/screenResumeInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\n// Define interface for pagination parameters\ninterface PaginationParams {\n  limit?: number;\n  offset?: number;\n  job_id?: number;\n  status?: string;\n}\n\ninterface JobApplication {\n  application_id: number;\n  job_id: number;\n  hiring_manager_id: number;\n  candidate_id: number;\n  candidate_name: string;\n  ai_decision: string;\n  ai_reason: string;\n  status?: string;\n  created_ts: string;\n}\n\ninterface JobApplicationResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication[];\n  pagination: {\n    limit: number;\n    offset: number;\n    totalCount: number;\n    hasMore: boolean;\n  };\n}\n\ninterface ChangeApplicationStatusParams {\n  job_id: number;\n  candidate_id: number;\n  hiring_manager_id: number;\n  status: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];\n  hiring_manager_reason: string;\n}\n\ninterface ChangeApplicationStatusResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication;\n}\n/**\n * Upload resume and assessment files and get presigned URLs\n * @param file - The file to upload (resume or assessment)\n * @returns Promise with presigned URL response\n */\nexport const getPresignedUrl = async (file: File): Promise<ApiResponse> => {\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  formData.append(\"fileType\", file.type);\n  formData.append(\"fileName\", file.name);\n\n  return http.post(endpoint.resumeScreen.GET_PRESIGNED_URL, formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n};\n\n/**\n * Upload file to S3 using presigned URL\n * @param presignedUrl - The presigned URL for S3 upload\n * @param file - The file to upload\n * @returns Promise with upload response\n */\nexport const uploadToS3 = async (presignedUrl: string, file: File): Promise<Response> => {\n  return fetch(presignedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\n/**\n * Process the file upload to get presigned URL and upload to S3\n * @param file - The file to upload\n * @returns Object with file URL and parsed text\n */\nexport const processFileUpload = async (file: File): Promise<{ fileUrl: string; fileText: string; presignedUrl: string }> => {\n  try {\n    // Get presigned URL\n    const presignedUrlResponse = await getPresignedUrl(file);\n\n    if (!presignedUrlResponse.data) {\n      throw new Error(\"Failed to get presigned URL\");\n    }\n    const responseData = presignedUrlResponse.data;\n\n    // The response might have data nested inside another data property\n    const urlData = responseData.data;\n\n    if (!urlData.presignedUrl || !urlData.fileUrl) {\n      console.error(\"Missing URL information in response:\", urlData);\n      throw new Error(\"Missing URL information in response\");\n    }\n\n    const { presignedUrl, fileUrl, fileText } = urlData;\n\n    // Upload file to S3\n    const uploadResponse = await uploadToS3(presignedUrl, file);\n    if (!uploadResponse.ok) {\n      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);\n    }\n    // Return the file URL and flag for backend extraction\n    return {\n      fileUrl,\n      fileText: fileText, // Special flag to indicate backend should extract text\n      presignedUrl,\n    };\n  } catch (error) {\n    console.error(\"Error processing file upload:\", error);\n    // Include error details in the console for debugging\n    if (error instanceof Error) {\n      console.error(\"Error message:\", error.message);\n      console.error(\"Error stack:\", error.stack);\n    }\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidate data with resume and assessment\n * @param data - The form values with candidate information\n * @returns Promise with API response\n */\n/**\n * Get all job applications with pagination (not just pending)\n * @param params - Pagination parameters (limit, offset, filters)\n * @returns Promise with job applications response\n */\nexport const getAllPendingJobApplications = async (params: PaginationParams): Promise<ApiResponse<JobApplicationResponse>> => {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    if (params.limit) queryParams.append(\"limit\", params.limit.toString());\n    // Always include offset parameter, even when it's 0\n    queryParams.append(\"offset\", params.offset !== undefined ? params.offset.toString() : \"0\");\n    if (params.job_id) queryParams.append(\"job_id\", params.job_id.toString());\n    if (params.status) queryParams.append(\"status\", params.status);\n\n    // Make API request\n    const url = `${endpoint.resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;\n    return http.get(url);\n  } catch (error) {\n    console.error(\"Error fetching job applications:\", error);\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidates to the backend for processing\n *\n * This function sends candidate data to the backend API for manual candidate upload.\n * The candidates should already have their files uploaded to S3 and contain URLs\n * instead of File objects.\n *\n * @param {Object} uploadManualCandidateData - The data object containing candidates and job information\n * @param {IUploadManualCandidate[]} uploadManualCandidateData.candidates - Array of candidate objects with uploaded file URLs\n * @param {number} uploadManualCandidateData.jobId - The ID of the job to associate candidates with\n *\n * @returns {Promise<ApiResponse>} Promise that resolves to the API response containing upload results\n *\n * @example\n * ```typescript\n * const candidateData = {\n *   candidates: [\n *     {\n *       name: \"John Doe\",\n *       email: \"<EMAIL>\",\n *       gender: \"male\",\n *       resume: \"https://cdn.example.com/resume.pdf\",\n *       assessment: \"https://cdn.example.com/assessment.pdf\",\n *       additionalInfo: \"Experienced developer\"\n *     }\n *   ],\n *   jobId: 123\n * };\n *\n * const response = await uploadManualCandidate(candidateData);\n * ```\n */\nexport const uploadManualCandidate = async (uploadManualCandidateData: {\n  candidates: IUploadManualCandidate[];\n  job_id: number;\n}): Promise<ApiResponse> => {\n  return http.post(endpoint.resumeScreen.MANUAL_CANDIDATE_UPLOAD, uploadManualCandidateData);\n};\n\n/**\n * Change the status of a job application (Approve, Reject, or Hold)\n * @param params - Parameters containing job_id, candidate_id, hiring_manager_id, and status\n * @param data - Data containing hiring_manager_reason\n * @returns Promise with API response\n */\nexport const changeApplicationStatus = async (data: ChangeApplicationStatusParams): Promise<ApiResponse<ChangeApplicationStatusResponse>> => {\n  return http.post(endpoint.resumeScreen.CHANGE_APPLICATION_STATUS, data);\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAuDO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IACrC,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IAErC,OAAO,uHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE,UAAU;QAClE,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAQO,MAAM,aAAa,OAAO,cAAsB;IACrD,OAAO,MAAM,cAAc;QACzB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,oBAAoB;QACpB,MAAM,uBAAuB,MAAM,gBAAgB;QAEnD,IAAI,CAAC,qBAAqB,IAAI,EAAE;YAC9B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,qBAAqB,IAAI;QAE9C,mEAAmE;QACnE,MAAM,UAAU,aAAa,IAAI;QAEjC,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,OAAO,EAAE;YAC7C,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;QAE5C,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,WAAW,cAAc;QACtD,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,eAAe,MAAM,EAAE;QACzE;QACA,sDAAsD;QACtD,OAAO;YACL;YACA,UAAU;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,qDAAqD;QACrD,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;YAC7C,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C;QACA,MAAM;IACR;AACF;AAYO,MAAM,+BAA+B,OAAO;IACjD,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACnE,oDAAoD;QACpD,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,CAAC,QAAQ,KAAK;QACtF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAE7D,mBAAmB;QACnB,MAAM,MAAM,GAAG,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;QACjG,OAAO,uHAAA,CAAA,OAAI,CAAC,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAkCO,MAAM,wBAAwB,OAAO;IAI1C,OAAO,uHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAE;AAClE;AAQO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,uHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,+HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,yBAAyB,EAAE;AACpE", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,6LAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;KAD9C;AAIN;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,6LAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,6LAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,6LAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,6LAAC,+IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,6LAAC,iKAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,6LAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB;KATwB", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/GreenCheckIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype GreenCheckIconProps = {\n  className?: string;\n};\n\nfunction GreenCheckIcon({ className }: GreenCheckIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" className={className}>\n      <path\n        d=\"M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9972 7.34869 18.9427 4.80678 17.068 2.93202C15.1932 1.05727 12.6513 0.00279983 10 0ZM14.3904 8.23654L9.00577 13.6212C8.93433 13.6927 8.84949 13.7494 8.75611 13.7881C8.66273 13.8268 8.56263 13.8468 8.46154 13.8468C8.36045 13.8468 8.26035 13.8268 8.16697 13.7881C8.07359 13.7494 7.98875 13.6927 7.91731 13.6212L5.60962 11.3135C5.46528 11.1691 5.38419 10.9734 5.38419 10.7692C5.38419 10.5651 5.46528 10.3693 5.60962 10.225C5.75396 10.0807 5.94972 9.99957 6.15385 9.99957C6.35798 9.99957 6.55374 10.0807 6.69808 10.225L8.46154 11.9894L13.3019 7.14808C13.3734 7.07661 13.4582 7.01991 13.5516 6.98123C13.645 6.94256 13.7451 6.92265 13.8462 6.92265C13.9472 6.92265 14.0473 6.94256 14.1407 6.98123C14.2341 7.01991 14.3189 7.07661 14.3904 7.14808C14.4619 7.21954 14.5185 7.30439 14.5572 7.39777C14.5959 7.49115 14.6158 7.59123 14.6158 7.69231C14.6158 7.79338 14.5959 7.89346 14.5572 7.98684C14.5185 8.08022 14.4619 8.16507 14.3904 8.23654Z\"\n        fill=\"#007733\"\n      />\n    </svg>\n  );\n}\n\nexport default GreenCheckIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,eAAe,EAAE,SAAS,EAAuB;IACxD,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;kBACxG,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;KATS;uCAWM", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/RoundCrossIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction RoundCrossIcon({ DangerColor }: { DangerColor?: boolean }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"25\" height=\"24\" viewBox=\"0 0 25 24\" fill=\"none\">\n      <circle cx=\"12.3331\" cy=\"11.9996\" r=\"10.9254\" fill=\"white\" />\n      <path\n        d=\"M12.3325 1C10.1569 1 8.03019 1.64514 6.22125 2.85383C4.41231 4.06253 3.00241 5.7805 2.16985 7.79048C1.33729 9.80047 1.11945 12.0122 1.54389 14.146C1.96832 16.2798 3.01597 18.2398 4.55435 19.7782C6.09273 21.3166 8.05274 22.3642 10.1865 22.7886C12.3203 23.2131 14.5321 22.9952 16.542 22.1627C18.552 21.3301 20.27 19.9202 21.4787 18.1113C22.6874 16.3023 23.3325 14.1756 23.3325 12C23.3291 9.08368 22.1691 6.28778 20.1069 4.22563C18.0447 2.16347 15.2489 1.00344 12.3325 1ZM16.5745 14.829C16.67 14.9212 16.7462 15.0316 16.7986 15.1536C16.851 15.2756 16.8786 15.4068 16.8798 15.5396C16.8809 15.6724 16.8556 15.8041 16.8053 15.927C16.7551 16.0499 16.6808 16.1615 16.5869 16.2554C16.493 16.3493 16.3814 16.4235 16.2585 16.4738C16.1356 16.5241 16.0039 16.5494 15.8711 16.5483C15.7383 16.5471 15.6071 16.5195 15.4851 16.4671C15.3631 16.4147 15.2528 16.3385 15.1605 16.243L12.3325 13.414L9.50453 16.243C9.31592 16.4252 9.06332 16.526 8.80112 16.5237C8.53893 16.5214 8.28812 16.4162 8.10271 16.2308C7.9173 16.0454 7.81213 15.7946 7.80985 15.5324C7.80757 15.2702 7.90837 15.0176 8.09053 14.829L10.9185 12L8.09053 9.171C7.99501 9.07875 7.91883 8.96841 7.86642 8.84641C7.81401 8.7244 7.78643 8.59318 7.78527 8.4604C7.78412 8.32762 7.80942 8.19594 7.8597 8.07305C7.90998 7.95015 7.98424 7.8385 8.07813 7.74461C8.17202 7.65071 8.28367 7.57646 8.40657 7.52618C8.52947 7.4759 8.66115 7.4506 8.79393 7.45175C8.92671 7.4529 9.05793 7.48049 9.17993 7.5329C9.30193 7.58531 9.41228 7.66149 9.50453 7.757L12.3325 10.586L15.1605 7.757C15.2528 7.66149 15.3631 7.58531 15.4851 7.5329C15.6071 7.48049 15.7383 7.4529 15.8711 7.45175C16.0039 7.4506 16.1356 7.4759 16.2585 7.52618C16.3814 7.57646 16.493 7.65071 16.5869 7.74461C16.6808 7.8385 16.7551 7.95015 16.8053 8.07305C16.8556 8.19594 16.8809 8.32762 16.8798 8.4604C16.8786 8.59318 16.851 8.7244 16.7986 8.84641C16.7462 8.96841 16.67 9.07875 16.5745 9.171L13.7465 12L16.5745 14.829Z\"\n        fill={DangerColor ? \"#d00000\" : \"#333333\"}\n      />\n    </svg>\n  );\n}\n\nexport default RoundCrossIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,eAAe,EAAE,WAAW,EAA6B;IAChE,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BACtF,6LAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAU,GAAE;gBAAU,MAAK;;;;;;0BACnD,6LAAC;gBACC,GAAE;gBACF,MAAM,cAAc,YAAY;;;;;;;;;;;;AAIxC;KAVS;uCAYM", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/CandidateStatusModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC, useState } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textarea from \"../formElements/Textarea\";\nimport { useForm } from \"react-hook-form\";\nimport GreenCheckIcon from \"../svgComponents/GreenCheckIcon\";\nimport RoundCrossIcon from \"../svgComponents/RoundCrossIcon\";\nimport { changeApplicationStatus } from \"@/services/screenResumeServices\";\nimport { useSelector } from \"react-redux\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { JobApplication } from \"@/interfaces/jobRequirementesInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\nimport Lottie from \"lottie-react\";\nimport rejected from \"../../../public/assets/images/rejected.json\";\nimport hurray from \"../../../public/assets/images/hurray.json\";\nimport { toastMessageSuccess, toTitleCase } from \"@/utils/helper\";\n\ninterface IProps {\n  onClickCancel: () => void;\n  disabled?: boolean;\n  candidate?: JobApplication;\n  aiDecision?: string;\n  actionType: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];\n  onSuccess?: () => void;\n  title?: string;\n}\n\nconst CandidateStatusModal: FC<IProps> = ({ onClickCancel, candidate, actionType = APPLICATION_STATUS.APPROVED, onSuccess, aiDecision, title }) => {\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(false);\n\n  const {\n    control,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<{ reason: string }>({\n    defaultValues: {\n      reason: \"\",\n    },\n    mode: \"onSubmit\",\n    criteriaMode: \"firstError\",\n    shouldFocusError: true,\n    reValidateMode: \"onChange\",\n    resolver: (values) => {\n      const errors: Record<string, { type: string; message: string }> = {};\n\n      // Required validation for reason field\n      if (!values.reason || values.reason.trim() === \"\") {\n        errors.reason = {\n          type: \"required\",\n          message: \"Please provide a reason\",\n        };\n      } else if (values.reason.trim().length < 5) {\n        errors.reason = {\n          type: \"minLength\",\n          message: \"Reason should be at least 5 characters long\",\n        };\n      } else if (values.reason.trim().length > 300) {\n        errors.reason = {\n          type: \"maxLength\",\n          message: \"Reason should not exceed 300 characters\",\n        };\n      }\n\n      return {\n        values,\n        errors,\n      };\n    },\n  });\n\n  const onSubmit = async (formData: { reason: string }) => {\n    if (!candidate || !authData) return;\n\n    try {\n      setIsSubmitting(true);\n      setError(\"\");\n\n      const data = {\n        job_id: candidate.job_id,\n        candidate_id: candidate.candidate_id,\n        hiring_manager_id: authData.id,\n        status: actionType,\n        hiring_manager_reason: formData.reason,\n      };\n\n      const response = await changeApplicationStatus(data);\n\n      if (response.data && response.data.success) {\n        let successMessage = \"\";\n\n        switch (actionType) {\n          case APPLICATION_STATUS.APPROVED:\n            successMessage = \"Candidate successfully marked as approved.\";\n            break;\n          case APPLICATION_STATUS.REJECTED:\n            successMessage = \"Candidate successfully marked as rejected.\";\n            break;\n          case APPLICATION_STATUS.ON_HOLD:\n            successMessage = \"Candidate has been placed on hold successfully!\";\n            break;\n          default:\n            successMessage = `Candidate status updated to ${actionType.toLowerCase()}.`;\n        }\n\n        toastMessageSuccess(successMessage);\n        setSuccess(true);\n\n        // Call the onSuccess callback if provided\n        if (onSuccess) {\n          setTimeout(() => {\n            onClickCancel();\n            onSuccess();\n          }, 1500);\n        }\n      } else {\n        setError(response.data?.message || \"Failed to update candidate status\");\n      }\n    } catch (err) {\n      console.error(\"Error updating candidate status:\", err);\n      setError(\"An unexpected error occurred. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Helper function to determine modal header content\n  const renderModalHeader = () => {\n    if (actionType === APPLICATION_STATUS.APPROVED) {\n      return (\n        <>\n          <h2 className=\"model-heading-lottie\">\n            Hurray! <Lottie animationData={hurray} className=\"lottie-icon\" />\n          </h2>\n          <p>Candidate marked as a good fit, shortlisted for the next step.</p>\n        </>\n      );\n    } else if (actionType === APPLICATION_STATUS.REJECTED) {\n      return (\n        <>\n          <h2 className=\"model-heading-lottie\">\n            Uh-Oh! <Lottie animationData={rejected} className=\"lottie-icon\" />\n          </h2>\n          <p>The candidate is unfit for the job.</p>\n        </>\n      );\n    } else if (actionType === APPLICATION_STATUS.ON_HOLD) {\n      return (\n        <>\n          <h2>On-Hold Confirmation</h2>\n          <p>You are about to place this candidate on hold for further review.</p>\n        </>\n      );\n    }\n  };\n\n  // Helper function to determine the status class\n  const getStatusClass = () => {\n    switch (actionType) {\n      case APPLICATION_STATUS.APPROVED:\n        return \"approved-status\";\n      case APPLICATION_STATUS.REJECTED:\n        return \"approved-status rejected-status\";\n      case APPLICATION_STATUS.ON_HOLD:\n        return \"on-hold-status\";\n      default:\n        return \"\";\n    }\n  };\n\n  // Helper function to determine status label\n  const getStatusLabel = () => {\n    switch (actionType) {\n      case APPLICATION_STATUS.APPROVED:\n        return (\n          <p>\n            <GreenCheckIcon />\n            {actionType} By You\n          </p>\n        );\n      case APPLICATION_STATUS.REJECTED:\n        return (\n          <p>\n            <RoundCrossIcon DangerColor />\n            {actionType} By You\n          </p>\n        );\n      case APPLICATION_STATUS.ON_HOLD:\n        return <p>On-Hold For Review</p>;\n      default:\n        return <p>{actionType}</p>;\n    }\n  };\n\n  // Helper function to determine reason label\n  const getReasonLabel = () => {\n    switch (actionType) {\n      case APPLICATION_STATUS.APPROVED:\n        return \"Reason for approval\";\n      case APPLICATION_STATUS.REJECTED:\n        return \"Reason for rejection\";\n      case APPLICATION_STATUS.ON_HOLD:\n        return \"Reason for putting on-hold\";\n      default:\n        return \"Reason\";\n    }\n  };\n\n  // Helper function to determine reason placeholder\n  const getReasonPlaceholder = () => {\n    switch (actionType) {\n      case APPLICATION_STATUS.APPROVED:\n        return \"Please enter the reason for approving this candidate\";\n      case APPLICATION_STATUS.REJECTED:\n        return \"Please enter the reason for rejecting this candidate\";\n      case APPLICATION_STATUS.ON_HOLD:\n        return \"Enter reason for putting candidate on-hold\";\n      default:\n        return \"Enter reason\";\n    }\n  };\n\n  // Helper for the AI reason title\n  const getAiReasonTitle = () => {\n    if (title) {\n      return title;\n    }\n\n    if (actionType === APPLICATION_STATUS.REJECTED || aiDecision === APPLICATION_STATUS.REJECTED) {\n      return \"Why the candidate is unfit for the role\";\n    }\n\n    return \"Why the candidate is a good fit for the role\";\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className={`modal-dialog modal-dialog-centered ${actionType === APPLICATION_STATUS.ON_HOLD ? \"modal-md\" : \"\"}`}>\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center pb-0\">\n            {renderModalHeader()}\n            {!isSubmitting && (\n              <Button className=\"modal-close-btn\" onClick={onClickCancel}>\n                <ModalCloseIcon />\n              </Button>\n            )}\n          </div>\n          <div className=\"modal-body\">\n            {/* qualification-card */}\n            <div className=\"qualification-card\">\n              <div className=\"qualification-card-top\">\n                <div className=\"name\">\n                  <h3>{toTitleCase(candidate?.candidate_name || \"Candidate\")}</h3>\n                  <p>{candidate?.ai_decision || \"Pending\"} by S9 InnerView</p>\n                </div>\n                <div className=\"top-right\">\n                  <div className={getStatusClass()}>{getStatusLabel()}</div>\n                </div>\n              </div>\n              <div className=\"qualification-card-mid\">\n                <p>\n                  <b>{getAiReasonTitle()}</b>\n                </p>\n                <p>{candidate?.ai_reason || \"No reason provided by AI evaluation.\"}</p>\n              </div>\n            </div>\n\n            {!success && (\n              <form onSubmit={handleSubmit(onSubmit)}>\n                <InputWrapper>\n                  <InputWrapper.Label htmlFor=\"reason\" required>\n                    {getReasonLabel()}\n                  </InputWrapper.Label>\n                  <Textarea rows={4} name=\"reason\" control={control} placeholder={getReasonPlaceholder()} className=\"form-control\" />\n                  <InputWrapper.Error message={errors?.reason?.message || \"\"} />\n                </InputWrapper>\n\n                {error && <div className=\"error-message alert alert-danger my-3\">{error}</div>}\n\n                <Button type=\"submit\" className=\"primary-btn rounded-md w-100\" disabled={isSubmitting}>\n                  {isSubmitting ? \"Submitting...\" : \"Submit\"}\n                </Button>\n              </form>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CandidateStatusModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;;AA6BA,MAAM,uBAAmC,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,aAAa,6IAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE;;IAC5I,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,eAAe;YACb,QAAQ;QACV;QACA,MAAM;QACN,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,QAAQ;4CAAE,CAAC;gBACT,MAAM,SAA4D,CAAC;gBAEnE,uCAAuC;gBACvC,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,IAAI;oBACjD,OAAO,MAAM,GAAG;wBACd,MAAM;wBACN,SAAS;oBACX;gBACF,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;oBAC1C,OAAO,MAAM,GAAG;wBACd,MAAM;wBACN,SAAS;oBACX;gBACF,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK;oBAC5C,OAAO,MAAM,GAAG;wBACd,MAAM;wBACN,SAAS;oBACX;gBACF;gBAEA,OAAO;oBACL;oBACA;gBACF;YACF;;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,aAAa,CAAC,UAAU;QAE7B,IAAI;YACF,gBAAgB;YAChB,SAAS;YAET,MAAM,OAAO;gBACX,QAAQ,UAAU,MAAM;gBACxB,cAAc,UAAU,YAAY;gBACpC,mBAAmB,SAAS,EAAE;gBAC9B,QAAQ;gBACR,uBAAuB,SAAS,MAAM;YACxC;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,0IAAA,CAAA,0BAAuB,AAAD,EAAE;YAE/C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBAC1C,IAAI,iBAAiB;gBAErB,OAAQ;oBACN,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;wBAC9B,iBAAiB;wBACjB;oBACF,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;wBAC9B,iBAAiB;wBACjB;oBACF,KAAK,6IAAA,CAAA,qBAAkB,CAAC,OAAO;wBAC7B,iBAAiB;wBACjB;oBACF;wBACE,iBAAiB,CAAC,4BAA4B,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;gBAC/E;gBAEA,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;gBACpB,WAAW;gBAEX,0CAA0C;gBAC1C,IAAI,WAAW;oBACb,WAAW;wBACT;wBACA;oBACF,GAAG;gBACL;YACF,OAAO;gBACL,SAAS,SAAS,IAAI,EAAE,WAAW;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oDAAoD;IACpD,MAAM,oBAAoB;QACxB,IAAI,eAAe,6IAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;YAC9C,qBACE;;kCACE,6LAAC;wBAAG,WAAU;;4BAAuB;0CAC3B,6LAAC,2JAAA,CAAA,UAAM;gCAAC,eAAe,4GAAA,CAAA,UAAM;gCAAE,WAAU;;;;;;;;;;;;kCAEnD,6LAAC;kCAAE;;;;;;;;QAGT,OAAO,IAAI,eAAe,6IAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;YACrD,qBACE;;kCACE,6LAAC;wBAAG,WAAU;;4BAAuB;0CAC5B,6LAAC,2JAAA,CAAA,UAAM;gCAAC,eAAe,8GAAA,CAAA,UAAQ;gCAAE,WAAU;;;;;;;;;;;;kCAEpD,6LAAC;kCAAE;;;;;;;;QAGT,OAAO,IAAI,eAAe,6IAAA,CAAA,qBAAkB,CAAC,OAAO,EAAE;YACpD,qBACE;;kCACE,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAE;;;;;;;;QAGT;IACF;IAEA,gDAAgD;IAChD,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,OAAO;YACT,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,OAAO;YACT,KAAK,6IAAA,CAAA,qBAAkB,CAAC,OAAO;gBAC7B,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,qBACE,6LAAC;;sCACC,6LAAC,wJAAA,CAAA,UAAc;;;;;wBACd;wBAAW;;;;;;;YAGlB,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,qBACE,6LAAC;;sCACC,6LAAC,wJAAA,CAAA,UAAc;4BAAC,WAAW;;;;;;wBAC1B;wBAAW;;;;;;;YAGlB,KAAK,6IAAA,CAAA,qBAAkB,CAAC,OAAO;gBAC7B,qBAAO,6LAAC;8BAAE;;;;;;YACZ;gBACE,qBAAO,6LAAC;8BAAG;;;;;;QACf;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,OAAO;YACT,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,OAAO;YACT,KAAK,6IAAA,CAAA,qBAAkB,CAAC,OAAO;gBAC7B,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kDAAkD;IAClD,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,OAAO;YACT,KAAK,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBAC9B,OAAO;YACT,KAAK,6IAAA,CAAA,qBAAkB,CAAC,OAAO;gBAC7B,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,mBAAmB;QACvB,IAAI,OAAO;YACT,OAAO;QACT;QAEA,IAAI,eAAe,6IAAA,CAAA,qBAAkB,CAAC,QAAQ,IAAI,eAAe,6IAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;YAC5F,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,CAAC,mCAAmC,EAAE,eAAe,6IAAA,CAAA,qBAAkB,CAAC,OAAO,GAAG,aAAa,IAAI;sBACjH,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ;4BACA,CAAC,8BACA,6LAAC,+IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;0CAC3C,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAIrB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAI,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,WAAW,kBAAkB;;;;;;kEAC9C,6LAAC;;4DAAG,WAAW,eAAe;4DAAU;;;;;;;;;;;;;0DAE1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAW;8DAAmB;;;;;;;;;;;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DACC,cAAA,6LAAC;8DAAG;;;;;;;;;;;0DAEN,6LAAC;0DAAG,WAAW,aAAa;;;;;;;;;;;;;;;;;;4BAI/B,CAAC,yBACA,6LAAC;gCAAK,UAAU,aAAa;;kDAC3B,6LAAC,qJAAA,CAAA,UAAY;;0DACX,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAS,QAAQ;0DAC1C;;;;;;0DAEH,6LAAC,iJAAA,CAAA,UAAQ;gDAAC,MAAM;gDAAG,MAAK;gDAAS,SAAS;gDAAS,aAAa;gDAAwB,WAAU;;;;;;0DAClG,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,QAAQ,QAAQ,WAAW;;;;;;;;;;;;oCAGzD,uBAAS,6LAAC;wCAAI,WAAU;kDAAyC;;;;;;kDAElE,6LAAC,+IAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,WAAU;wCAA+B,UAAU;kDACtE,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GAxQM;;QACa,4JAAA,CAAA,cAAW;QASxB,iKAAA,CAAA,UAAO;;;KAVP;uCA0QS", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/resume/CandidateQualification.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\n// Internal libraries\nimport React, { useState, useEffect, useRef, useCallback } from \"react\";\n\n// External libraries\nimport { useRouter } from \"next/navigation\";\n\n// Redux, constants, interfaces\nimport { DEFAULT_LIMIT } from \"@/constants/commonConstants\";\nimport ROUTES from \"@/constants/routes\";\nimport { JobApplication } from \"@/interfaces/jobRequirementesInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\n// Components\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\nimport But<PERSON> from \"@/components/formElements/Button\";\n// We will delete this model after mock testing and we have created CandidateStatusModal common modal\n// import CandidateQualifiedModal from \"@/components/commonModals/CandidateQualifiedModal\";\n// import CandidateApprovalModal from \"@/components/commonModals/CandidateApprovalModal\";\n\n// Services\nimport { getAllPendingJobApplications } from \"@/services/screenResumeServices\";\n\n// CSS\nimport style from \"@/styles/commonPage.module.scss\";\nimport { useTranslations } from \"next-intl\";\nimport { toTitleCase } from \"@/utils/helper\";\nimport CandidateStatusModal from \"@/components/commonModals/CandidateStatusModal\";\n\n/**\n * CandidateQualification Component\n *\n * Displays a list of candidates pending qualification with options to approve, reject, or place on hold.\n * Includes infinite scrolling to load more candidates as the user scrolls down.\n *\n * @returns {JSX.Element} The rendered CandidateQualification component\n */\nfunction CandidateQualification({\n  params,\n  searchParams,\n}: {\n  params: Promise<{ jobId: string }>;\n  searchParams: Promise<{ title: string; jobUniqueId: string }>;\n}) {\n  const router = useRouter();\n  // Modal states\n  const [showCandidateApprovedModal, setShowCandidateApprovedModal] = useState(false);\n  const [showCandidateRejectedModal, setShowCandidateRejectedModal] = useState(false);\n  const [showCandidateHoldModal, setShowCandidateHoldModal] = useState(false);\n  const searchParamsPromise = React.use(searchParams);\n  const paramsPromise = React.use(params);\n\n  // Track the currently selected candidate for modals\n  const [selectedCandidate, setSelectedCandidate] = useState<JobApplication | null>(null);\n  // State for candidate applications\n  const [applications, setApplications] = useState<JobApplication[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [hasMore, setHasMore] = useState(true);\n  const STATUS = APPLICATION_STATUS; // Status to filter applications\n  const initialFetchDone = useRef(false);\n  const t = useTranslations();\n  useEffect(() => {\n    if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {\n      router.push(ROUTES.JOBS.ACTIVE_JOBS);\n    }\n  }, [paramsPromise.jobId, searchParamsPromise?.title]);\n  // Pagination parameters\n  const [pagination, setPagination] = useState({\n    limit: DEFAULT_LIMIT,\n    offset: 0,\n    status: STATUS.PENDING,\n    job_id: Number(paramsPromise.jobId),\n    // You can add other filter parameters like job_id if needed\n  });\n\n  // Observer for infinite scrolling\n  const observer = useRef<IntersectionObserver | null>(null);\n  const lastApplicationElementRef = useCallback(\n    (node: HTMLDivElement | null) => {\n      if (loading) return;\n      if (observer.current) observer.current.disconnect();\n      observer.current = new IntersectionObserver((entries) => {\n        if (entries[0].isIntersecting && hasMore) {\n          loadMoreApplications();\n        }\n      });\n      if (node) observer.current.observe(node);\n    },\n    [loading, hasMore]\n  );\n\n  /**\n   * Fetches candidate applications from the API\n   *\n   * Makes an API call to get pending job applications based on pagination parameters.\n   * Updates the applications state with the fetched data and handles pagination status.\n   *\n   * @returns {Promise<void>}\n   */\n  const fetchApplications = async () => {\n    try {\n      setLoading(true);\n      const response = await getAllPendingJobApplications(pagination);\n\n      if (response.data) {\n        // Extract applications from response\n        const newApplications = response.data.data || [];\n        const paginationInfo = response.data.pagination || { total: 0, hasMore: false };\n\n        // Append new applications to existing ones\n        if (pagination.offset === 0) {\n          setApplications(newApplications);\n        } else {\n          setApplications((prev) => [...prev, ...newApplications]);\n        }\n\n        // Check if we've loaded all applications based on hasMore flag\n        setHasMore(paginationInfo.hasMore);\n      }\n    } catch (error) {\n      console.error(t(\"error_fetching_applications\"), error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Updates pagination to load more applications\n   *\n   * Increments the offset in the pagination state, which triggers the useEffect\n   * to fetch the next batch of applications.\n   */\n  const loadMoreApplications = () => {\n    setPagination((prev) => ({\n      ...prev,\n      offset: prev.offset + prev.limit,\n    }));\n  };\n\n  // Fetch applications on component mount or when pagination changes\n  useEffect(() => {\n    if (!initialFetchDone.current) {\n      initialFetchDone.current = true;\n      fetchApplications();\n    }\n  }, [pagination.offset]);\n\n  /**\n   * Handles closing the candidate approval modal\n   *\n   * Resets the modal state and clears the selected candidate.\n   */\n  const onCancelCandidateApprovedModal = async () => {\n    setShowCandidateApprovedModal(false);\n    setSelectedCandidate(null);\n  };\n\n  /**\n   * Handles closing the candidate rejection modal\n   *\n   * Resets the modal state and clears the selected candidate.\n   */\n  const onCancelCandidateRejectedModal = async () => {\n    setShowCandidateRejectedModal(false);\n    setSelectedCandidate(null);\n  };\n\n  /**\n   * Handles closing the candidate hold modal\n   *\n   * Resets the modal state and clears the selected candidate.\n   */\n  const onCancelCandidateHoldModal = async () => {\n    setShowCandidateHoldModal(false);\n    setSelectedCandidate(null);\n  };\n\n  /**\n   * Handles successful status change for a candidate\n   *\n   * Updates the application list by removing the candidate that was processed.\n   * Closes any open modals and refreshes the application list if needed.\n   *\n   * @param {any} candidate - The candidate whose status was changed\n   * @param {string} newStatus - The new status of the candidate (Approved, Rejected, On-Hold)\n   */\n  const handleStatusChangeSuccess = (candidate: JobApplication, newStatus: string) => {\n    // 1. Optimistic UI update - update the candidate status in the local state immediately\n    setApplications((prevApplications) => {\n      return prevApplications\n        .map((app) => {\n          if (app.candidate_id === candidate.candidate_id) {\n            // Create a copy of the application with updated status\n            return {\n              ...app,\n              status: newStatus,\n              // Remove it from the list if it's no longer in pending status\n              hidden: app.status === STATUS.PENDING && newStatus !== STATUS.PENDING,\n            };\n          }\n          return app;\n        })\n        .filter((app) => !app.hidden);\n    });\n\n    // 2. Refresh data from server in the background to ensure data consistency\n    fetchApplications();\n  };\n\n  /**\n   * Opens the approval modal for a candidate\n   *\n   * Sets the selected candidate and shows the approval modal.\n   *\n   * @param {JobApplication} candidate - The candidate to be approved\n   */\n  const handleApproveCandidate = (candidate: JobApplication) => {\n    setSelectedCandidate(candidate);\n    setShowCandidateApprovedModal(true);\n  };\n\n  /**\n   * Opens the rejection modal for a candidate\n   *\n   * Sets the selected candidate and shows the rejection modal.\n   *\n   * @param {JobApplication} candidate - The candidate to be rejected\n   */\n  const handleRejectCandidate = (candidate: JobApplication) => {\n    setSelectedCandidate(candidate);\n    setShowCandidateRejectedModal(true);\n  };\n\n  /**\n   * Opens the on-hold modal for a candidate\n   *\n   * Sets the selected candidate and shows the on-hold modal.\n   *\n   * @param {JobApplication} candidate - The candidate to be placed on hold\n   */\n  const onHoldCandidate = (candidate: JobApplication) => {\n    setSelectedCandidate(candidate);\n    setShowCandidateHoldModal(true);\n  };\n\n  return (\n    <>\n      <div className={`${style.resume_page} ${style.candidate_qualification_page}`}>\n        <div className=\"container\">\n          <div className={style.inner_page}>\n            <div className=\"common-page-header\">\n              <div className=\"common-page-head-section\">\n                <div className=\"main-heading\">\n                  <h2>\n                    <BackArrowIcon onClick={() => router.back()} />\n                    {t(\"resume_analysis\")} <span>{searchParamsPromise?.title}</span>\n                  </h2>\n                  <div className={style.approved_status_indicator}>\n                    <p>\n                      <span />\n                      {t(\"approved_by_s9\")}\n                    </p>\n                    <p>\n                      <span />\n                      {t(\"rejected_by_s9\")}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"row g-4\">\n              {/* Map through the applications array to render candidate cards */}\n              {applications.map((application, index) => {\n                // Check if this is the last element to attach the ref for infinite scrolling\n                const isLastElement = index === applications.length - 1;\n                const isRejected = application.ai_decision === STATUS.REJECTED;\n\n                return (\n                  <div className=\"col-md-6 col-lg-4\" key={application.application_id || index} ref={isLastElement ? lastApplicationElementRef : null}>\n                    <div className={`qualification-card h-100 ${isRejected ? \"rejected-card\" : \"\"}`}>\n                      <div className=\"qualification-card-top\">\n                        <div className=\"name\">\n                          <h3>{toTitleCase(application?.candidate_name)}</h3>\n                          <p>{application?.ai_decision} by S9 InnerView</p>\n                        </div>\n                        {/* <div\n                          className=\"top-right\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            onHoldCandidate(application);\n                          }}\n                        >\n                          <HoldIcon className=\"hold-icon cursor-pointer\" />\n                        </div> */}\n                      </div>\n                      <div className=\"qualification-card-mid\">\n                        <p>\n                          <b>\n                            {\" \"}\n                            {application.ai_decision === STATUS.APPROVED ? t(\"reasons_for_good_match\") : \"Reasons why they are not good match:\"}{\" \"}\n                          </b>\n                        </p>\n                        <p>{application?.ai_reason}</p>\n                      </div>\n                      <div className=\"qualification-buttons\">\n                        <Button className=\"secondary-btn rounded-md rounded-md p-3\" onClick={() => handleApproveCandidate(application)}>\n                          {t(\"approve\")}\n                        </Button>\n                        <Button className=\"dark-outline-btn rounded-md rounded-md p-3\" onClick={() => handleRejectCandidate(application)}>\n                          {t(\"reject\")}\n                        </Button>\n                        <Button\n                          className=\"dark-outline-btn rounded-md rounded-md p-3\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            onHoldCandidate(application);\n                          }}\n                        >\n                          {t(\"hold\")}\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n\n              {/* Skeleton loader */}\n              {loading && (\n                <>\n                  {[1, 2, 3].map((item) => (\n                    <div className=\"col-md-6 col-lg-4\" key={`skeleton-${item}`}>\n                      <div className=\"qualification-card skeleton-card\">\n                        <div className=\"qualification-card-top\">\n                          <div className=\"name\">\n                            <div className=\"skeleton-text skeleton-title\"></div>\n                            <div className=\"skeleton-text skeleton-subtitle\"></div>\n                          </div>\n                          <div className=\"top-right\">\n                            <div className=\"skeleton-circle\"></div>\n                          </div>\n                        </div>\n                        <div className=\"qualification-card-mid\">\n                          <div className=\"skeleton-text skeleton-label\"></div>\n                          <div className=\"skeleton-text\"></div>\n                          <div className=\"skeleton-text\"></div>\n                          <div className=\"skeleton-text\"></div>\n                        </div>\n                        <div className=\"button-align\">\n                          <div className=\"skeleton-button\"></div>\n                          <div className=\"skeleton-button\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* No results message */}\n              {!loading && applications.length === 0 && (\n                <div className=\"col-12 text-center py-3\">\n                  <p>{t(\"no_candidates_found\")}</p>\n                </div>\n              )}\n            </div>\n          </div>\n          {/* Only show buttons if there are pending applications */}\n          {applications.length > 0 && (\n            <div className=\"button-align py-5\">\n              <Button\n                className=\"primary-btn rounded-md\"\n                onClick={() =>\n                  router.push(\n                    `${ROUTES.SCREEN_RESUME.CANDIDATE_LIST}/${paramsPromise.jobId}?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`\n                  )\n                }\n              >\n                {t(\"view_all_candidates\")}\n              </Button>\n              <Button\n                className=\"dark-outline-btn rounded-md\"\n                onClick={() =>\n                  router.push(\n                    `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}` +\n                      `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`\n                  )\n                }\n              >\n                {t(\"back_to_screening\")}\n              </Button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {showCandidateApprovedModal && selectedCandidate && (\n        <CandidateStatusModal\n          onClickCancel={onCancelCandidateApprovedModal}\n          candidate={selectedCandidate}\n          actionType={APPLICATION_STATUS.APPROVED}\n          aiDecision={selectedCandidate.ai_decision}\n          onSuccess={() => handleStatusChangeSuccess(selectedCandidate, STATUS.APPROVED)}\n        />\n      )}\n\n      {showCandidateRejectedModal && selectedCandidate && (\n        <CandidateStatusModal\n          onClickCancel={onCancelCandidateRejectedModal}\n          candidate={selectedCandidate}\n          actionType={APPLICATION_STATUS.REJECTED}\n          aiDecision={selectedCandidate.ai_decision}\n          onSuccess={() => handleStatusChangeSuccess(selectedCandidate, STATUS.REJECTED)}\n        />\n      )}\n\n      {showCandidateHoldModal && selectedCandidate && (\n        <CandidateStatusModal\n          onClickCancel={onCancelCandidateHoldModal}\n          candidate={selectedCandidate}\n          actionType={APPLICATION_STATUS.ON_HOLD}\n          onSuccess={() => handleStatusChangeSuccess(selectedCandidate, STATUS.ON_HOLD)}\n          title={selectedCandidate.ai_reason === \"Approved\" ? \"Reasons why they are good match:\" : \"Reasons why they are not good match:\"}\n        />\n      )}\n    </>\n  );\n}\n\nexport default CandidateQualification;\n"], "names": [], "mappings": "AAAA,8CAA8C,GAC9C,qBAAqB;;;;;AACrB;AAEA,qBAAqB;AACrB;AAEA,+BAA+B;AAC/B;AACA;AAEA;AAEA,aAAa;AACb;AACA;AACA,qGAAqG;AACrG,2FAA2F;AAC3F,yFAAyF;AAEzF,WAAW;AACX;AAEA,MAAM;AACN;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA;;;;;;;CAOC,GACD,SAAS,uBAAuB,EAC9B,MAAM,EACN,YAAY,EAIb;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,eAAe;IACf,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IACtC,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAEhC,oDAAoD;IACpD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAClF,mCAAmC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,6IAAA,CAAA,qBAAkB,EAAE,gCAAgC;IACnE,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,OAAO,cAAc,KAAK,KAAK,CAAC,qBAAqB,SAAS,qBAAqB,MAAM,WAAW,GAAG;gBAC1G,OAAO,IAAI,CAAC,6HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;YACrC;QACF;2CAAG;QAAC,cAAc,KAAK;QAAE,qBAAqB;KAAM;IACpD,wBAAwB;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO,sIAAA,CAAA,gBAAa;QACpB,QAAQ;QACR,QAAQ,OAAO,OAAO;QACtB,QAAQ,OAAO,cAAc,KAAK;IAEpC;IAEA,kCAAkC;IAClC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA+B;IACrD,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yEAC1C,CAAC;YACC,IAAI,SAAS;YACb,IAAI,SAAS,OAAO,EAAE,SAAS,OAAO,CAAC,UAAU;YACjD,SAAS,OAAO,GAAG,IAAI;iFAAqB,CAAC;oBAC3C,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,IAAI,SAAS;wBACxC;oBACF;gBACF;;YACA,IAAI,MAAM,SAAS,OAAO,CAAC,OAAO,CAAC;QACrC;wEACA;QAAC;QAAS;KAAQ;IAGpB;;;;;;;GAOC,GACD,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,0IAAA,CAAA,+BAA4B,AAAD,EAAE;YAEpD,IAAI,SAAS,IAAI,EAAE;gBACjB,qCAAqC;gBACrC,MAAM,kBAAkB,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;gBAChD,MAAM,iBAAiB,SAAS,IAAI,CAAC,UAAU,IAAI;oBAAE,OAAO;oBAAG,SAAS;gBAAM;gBAE9E,2CAA2C;gBAC3C,IAAI,WAAW,MAAM,KAAK,GAAG;oBAC3B,gBAAgB;gBAClB,OAAO;oBACL,gBAAgB,CAAC,OAAS;+BAAI;+BAAS;yBAAgB;gBACzD;gBAEA,+DAA+D;gBAC/D,WAAW,eAAe,OAAO;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,EAAE,gCAAgC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA;;;;;GAKC,GACD,MAAM,uBAAuB;QAC3B,cAAc,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,GAAG,KAAK,KAAK;YAClC,CAAC;IACH;IAEA,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,iBAAiB,OAAO,EAAE;gBAC7B,iBAAiB,OAAO,GAAG;gBAC3B;YACF;QACF;2CAAG;QAAC,WAAW,MAAM;KAAC;IAEtB;;;;GAIC,GACD,MAAM,iCAAiC;QACrC,8BAA8B;QAC9B,qBAAqB;IACvB;IAEA;;;;GAIC,GACD,MAAM,iCAAiC;QACrC,8BAA8B;QAC9B,qBAAqB;IACvB;IAEA;;;;GAIC,GACD,MAAM,6BAA6B;QACjC,0BAA0B;QAC1B,qBAAqB;IACvB;IAEA;;;;;;;;GAQC,GACD,MAAM,4BAA4B,CAAC,WAA2B;QAC5D,uFAAuF;QACvF,gBAAgB,CAAC;YACf,OAAO,iBACJ,GAAG,CAAC,CAAC;gBACJ,IAAI,IAAI,YAAY,KAAK,UAAU,YAAY,EAAE;oBAC/C,uDAAuD;oBACvD,OAAO;wBACL,GAAG,GAAG;wBACN,QAAQ;wBACR,8DAA8D;wBAC9D,QAAQ,IAAI,MAAM,KAAK,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO;oBACvE;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,MAAQ,CAAC,IAAI,MAAM;QAChC;QAEA,2EAA2E;QAC3E;IACF;IAEA;;;;;;GAMC,GACD,MAAM,yBAAyB,CAAC;QAC9B,qBAAqB;QACrB,8BAA8B;IAChC;IAEA;;;;;;GAMC,GACD,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,8BAA8B;IAChC;IAEA;;;;;;GAMC,GACD,MAAM,kBAAkB,CAAC;QACvB,qBAAqB;QACrB,0BAA0B;IAC5B;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAW,GAAG,4JAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,4JAAA,CAAA,UAAK,CAAC,4BAA4B,EAAE;0BAC1E,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,UAAU;;8CAC9B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,uJAAA,CAAA,UAAa;4DAAC,SAAS,IAAM,OAAO,IAAI;;;;;;wDACxC,EAAE;wDAAmB;sEAAC,6LAAC;sEAAM,qBAAqB;;;;;;;;;;;;8DAErD,6LAAC;oDAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,yBAAyB;;sEAC7C,6LAAC;;8EACC,6LAAC;;;;;gEACA,EAAE;;;;;;;sEAEL,6LAAC;;8EACC,6LAAC;;;;;gEACA,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMb,6LAAC;oCAAI,WAAU;;wCAEZ,aAAa,GAAG,CAAC,CAAC,aAAa;4CAC9B,6EAA6E;4CAC7E,MAAM,gBAAgB,UAAU,aAAa,MAAM,GAAG;4CACtD,MAAM,aAAa,YAAY,WAAW,KAAK,OAAO,QAAQ;4CAE9D,qBACE,6LAAC;gDAAI,WAAU;gDAA8D,KAAK,gBAAgB,4BAA4B;0DAC5H,cAAA,6LAAC;oDAAI,WAAW,CAAC,yBAAyB,EAAE,aAAa,kBAAkB,IAAI;;sEAC7E,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAI,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE,aAAa;;;;;;kFAC9B,6LAAC;;4EAAG,aAAa;4EAAY;;;;;;;;;;;;;;;;;;sEAYjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EACC,cAAA,6LAAC;;4EACE;4EACA,YAAY,WAAW,KAAK,OAAO,QAAQ,GAAG,EAAE,4BAA4B;4EAAwC;;;;;;;;;;;;8EAGzH,6LAAC;8EAAG,aAAa;;;;;;;;;;;;sEAEnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+IAAA,CAAA,UAAM;oEAAC,WAAU;oEAA0C,SAAS,IAAM,uBAAuB;8EAC/F,EAAE;;;;;;8EAEL,6LAAC,+IAAA,CAAA,UAAM;oEAAC,WAAU;oEAA6C,SAAS,IAAM,sBAAsB;8EACjG,EAAE;;;;;;8EAEL,6LAAC,+IAAA,CAAA,UAAM;oEACL,WAAU;oEACV,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,gBAAgB;oEAClB;8EAEC,EAAE;;;;;;;;;;;;;;;;;;+CAxC6B,YAAY,cAAc,IAAI;;;;;wCA8C1E;wCAGC,yBACC;sDACG;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;;;;;;;;;;;;kFAEjB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;0EAGnB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;0EAEjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;mDAnBmB,CAAC,SAAS,EAAE,MAAM;;;;;;wCA4B/D,CAAC,WAAW,aAAa,MAAM,KAAK,mBACnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;wBAMb,aAAa,MAAM,GAAG,mBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+IAAA,CAAA,UAAM;oCACL,WAAU;oCACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,EAAE,cAAc,KAAK,CAAC,OAAO,EAAE,qBAAqB,MAAM,aAAa,EAAE,qBAAqB,aAAa;8CAItJ,EAAE;;;;;;8CAEL,6LAAC,+IAAA,CAAA,UAAM;oCACL,WAAU;oCACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,6HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE,GACtE,CAAC,OAAO,EAAE,qBAAqB,MAAM,aAAa,EAAE,qBAAqB,aAAa;8CAI3F,EAAE;;;;;;;;;;;;;;;;;;;;;;;YAOZ,8BAA8B,mCAC7B,6LAAC,6JAAA,CAAA,UAAoB;gBACnB,eAAe;gBACf,WAAW;gBACX,YAAY,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBACvC,YAAY,kBAAkB,WAAW;gBACzC,WAAW,IAAM,0BAA0B,mBAAmB,OAAO,QAAQ;;;;;;YAIhF,8BAA8B,mCAC7B,6LAAC,6JAAA,CAAA,UAAoB;gBACnB,eAAe;gBACf,WAAW;gBACX,YAAY,6IAAA,CAAA,qBAAkB,CAAC,QAAQ;gBACvC,YAAY,kBAAkB,WAAW;gBACzC,WAAW,IAAM,0BAA0B,mBAAmB,OAAO,QAAQ;;;;;;YAIhF,0BAA0B,mCACzB,6LAAC,6JAAA,CAAA,UAAoB;gBACnB,eAAe;gBACf,WAAW;gBACX,YAAY,6IAAA,CAAA,qBAAkB,CAAC,OAAO;gBACtC,WAAW,IAAM,0BAA0B,mBAAmB,OAAO,OAAO;gBAC5E,OAAO,kBAAkB,SAAS,KAAK,aAAa,qCAAqC;;;;;;;;AAKnG;GApYS;;QAOQ,qIAAA,CAAA,YAAS;QAgBd,yMAAA,CAAA,kBAAe;;;KAvBlB;uCAsYM", "debugId": null}}, {"offset": {"line": 1936, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/candidate-qualification/%5BjobId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport CandidateQualification from \"@/components/views/resume/CandidateQualification\";\nimport React from \"react\";\n\nconst page = ({ params, searchParams }: { params: Promise<{ jobId: string }>; searchParams: Promise<{ title: string; jobUniqueId: string }> }) => {\n  return (\n    <div>\n      <CandidateQualification params={params} searchParams={searchParams} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAyG;IAC3I,qBACE,6LAAC;kBACC,cAAA,6LAAC,kKAAA,CAAA,UAAsB;YAAC,QAAQ;YAAQ,cAAc;;;;;;;;;;;AAG5D;uCAEe", "debugId": null}}, {"offset": {"line": 1970, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}