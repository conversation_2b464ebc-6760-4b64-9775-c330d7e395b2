// import config from "@/config/config";

const URL = process.env.NEXT_PUBLIC_BASE_URL;

const endpoint = {
  auth: {
    SIGNIN: `${URL}/auth/sign-in`,
    VERIFY_OTP: `${URL}/auth/verify-otp`,
    RESEND_OTP: `${URL}/auth/resend-otp`,
    FORGOT_PASSWORD: `${URL}/auth/forgot-password`,
    RESET_PASSWORD: `${URL}/auth/reset-password`,
    DELETE_SESSION: `${URL}/auth/delete-session`,
    UPDATE_TIMEZONE: `${URL}/auth/update-timezone`,
  },
  interview: {
    UPDATE_OR_SCHEDULE_INTERVIEW: `${URL}/interview/update-or-schedule-interview`,
    GET_INTERVIEWS: `${URL}/interview/get-interviews`,
    GET_INTERVIEWERS: `${URL}/interview/get-interviewers`,
    GET_MY_INTERVIEWS: `${URL}/interview/get-my-interviews`,
    UPDATE_INTERVIEW_ANSWERS: `${URL}/interview/update-interview-answers`,

    GET_UPCOMING_OR_PAST_INTERVIEW: `${URL}/interview/get-upcoming-or-past-interviews`,
    GET_INTERVIEW_SKILL_QUESTIONS: `${URL}/interview/get-interview-skill-questions`,
    UPDATE_INTERVIEW_SKILL_QUESTION: `${URL}/interview/update-interview-skill-question`,
    ADD_INTERVIEW_SKILL_QUESTION: `${URL}/interview/add-interview-skill-question`,
    GET_COMPLETED_SKILLS: `${URL}/interview/get-completed-skills`,

    GET_JOB_LIST: `${URL}/interview/get-job-list`,
    GET_CANDIDATE_LIST: `${URL}/interview/get-candidate-list`,
    END_INTERVIEW: `${URL}/interview/end-interview`,
    CONDUCT_INTERVIEW_STATIC_INFORMATION: `${URL}/interview/conduct-interview-static-information`,
  },
  common: {
    REMOVE_ATTACHMENTS_FROM_S3: `${URL}/remove-attachments-from-s3`,
    GENERATE_PRESIGNED_URL: `${URL}/generate-presignedurl`,
  },
  jobRequirements: {
    GENERATE_SKILL: `${URL}/jobs/generate-skills`,
    UPLOAD_URL: `${URL}/jobs/upload-url`,
    PARSE_PDF: `${URL}/jobs/parse-pdf`,
    GET_ALL_SKILLS: `${URL}/jobs/get-all-skills`,
    GENERATE_JOB_REQUIREMENT: `${URL}/jobs/generate-job-requirement`,
    SAVE_JOB_DETAILS: `${URL}/jobs/save-job-details`,
    GET_JOBS_META: `${URL}/jobs/get-jobs-meta`, // <-- Full URL here
    UPDATE_JOB_STATUS: "/jobs/updateJob",
    GET_JOB_HTML_DESCRIPTION: `${URL}/jobs/get-job-html-description`,
    UPDATE_JOB_DESCRIPTION: `${URL}/jobs/update-job-description`,
    GENERATE_PDF: `${URL}/jobs/generate-pdf`,
  },
  Dashboard: {
    GET_DASHBOARD_COUNTS: `${URL}/jobs/dashboard-counts`,
  },
  resumeScreen: {
    MANUAL_CANDIDATE_UPLOAD: `${URL}/resume-screen/manual-candidate-upload`,
    GET_PRESIGNED_URL: `${URL}/resume-screen/get-presigned-url`,
    GET_ALL_PENDING_JOB_APPLICATIONS: `${URL}/resume-screen/get-all-pending-job-applications`,
    CHANGE_APPLICATION_STATUS: `${URL}/resume-screen/change-application-status`,
  },
  employee: {
    ADD_EMPLOYEES: `${URL}/employee-management/add-hiring-employee`,
    GET_EMPLOYEES: `${URL}/employee-management`,
    GET_EMPLOYEES_BY_DEPARTMENT: `${URL}/employee-management/employees`,
    UPDATE_EMPLOYEE_ROLE: `${URL}/employee-management/employee/:employeeId/role`,
    UPDATE_EMPLOYEE_STATUS: `${URL}/employee-management/employee/change-status/:employeeId`,
    // this task is for future use
    // DELETE_EMPLOYEE: `${URL}/employee-management/employee/:employeeId`, // original
    DELETE_EMPLOYEE: `${URL}/employee-management/dummy`, //dummy

    UPDATE_EMPLOYEE_INTERVIEW_ORDER: `${URL}/employee-management/employee/:employeeId/interview-order`,
  },
  userprofile: {
    GET_MY_PROFILE: `${URL}/user-profile/get-my-profile`,
    UPDATE_MY_PROFILE: `${URL}/user-profile/update-my-profile`,
  },

  roles: {
    GET_ROLES_WITH_PAGINATION: `${URL}/access-management/user-roles-pagination`,
    GET_ROLES: `${URL}/access-management/user-roles`,
    ADD_USER_ROLE: `${URL}/access-management/add-user-role`,
    UPDATE_USER_ROLE: `${URL}/access-management/user-role`,
    DELETE_USER_ROLE: `${URL}/access-management/user-role`,
    GET_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions`,
    GET_ROLE_PERMISSIONS_BY_ID: `${URL}/access-management/role-permissions/:roleId`,
    UPDATE_ROLE_PERMISSIONS: `${URL}/access-management/role-permissions/:roleId`,
    USER_PERMISSIONS: `${URL}/access-management/user-permissions`,
  },
  notification: {
    UPDATE_NOTIFICATION: `${URL}/notifications/mark-as-watched`,
    GET_NOTIFICATIONS: `${URL}/notifications/get-notifications`,
    DELETE_ALL_NOTIFICATIONS: `${URL}/notifications/delete-users-all-notifications`,
    GET_UNREAD_NOTIFICATIONS_COUNT: `${URL}/notifications/get-unread-notifications-count`,
  },

  departments: {
    GET_DEPARTMENTS: `${URL}/employee-management/departments`,
    ADD_DEPARTMENT: `${URL}/employee-management/add-department`,
    UPDATE_DEPARTMENT: `${URL}/employee-management/update-department/:departmentId`,
    DELETE_DEPARTMENT: `${URL}/employee-management/delete-department/:departmentId`,
  },

  assessment: {
    CREATE_FINAL_ASSESSMENT: `${URL}/final-assessment/create-final-assessment`,
    GET_FINAL_ASSESSMENT_QUESTIONS: `${URL}/final-assessment/assessment/questions`,
    CREATE_ASSESSMENT_QUESTION: `${URL}/final-assessment/assessment/create-question`,
    SUBMIT_CANDIDATE_ANSWERS: `${URL}/final-assessment/candidate/:candidateId/submit`,
    SHARE_ASSESSMENT: `${URL}/final-assessment/assessment/share`,
    GET_FINAL_ASSESSMENT_BY_CANDIDATE: `${URL}/final-assessment/candidate/assessment`,
    SUBMIT_ASSESSMENT: `${URL}/final-assessment/candidate/assessment/submit`,
    GET_ASSESSMENT_STATUS: `${URL}/final-assessment/assessment-status`,
    VERIFY_CANDIDATE_EMAIL: `${URL}/final-assessment/candidate/verify-email`,
    GENERATE_ASSESSMENT_TOKEN: `${URL}/final-assessment/assessment/generate-token`,
  },
  candidatesApplication: {
    ADDITIONAL_INFO: `${URL}/candidates/add-applicant-additional-info`,
    PROMOTE_DEMOTE_CANDIDATE: `${URL}/candidates/update-candidate-rank-status`, // Base URL for candidates-related endpoints
    GET_TOP_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/top-candidates`,
    GET_CANDIDATES_WITH_APPLICATIONS: `${URL}/candidates/get-candidates`,
    ARCHIVE_ACTIVE_APPLICATION: `${URL}/candidates/archive-active-application/:applicationId`,
    GET_CANDIDATE_DETAILS: `${URL}/candidates/get-candidate-details`, // New endpoint for individual candidate details
    UPDATE_JOB_APPLICATION_STATUS: `${URL}/candidates/update-job-application-status/:jobApplicationId`, // Endpoint for updating job application status
    GET_CANDIDATE_INTERVIEW_HISTORY: `${URL}/candidates/get-candidate-interview-history/:applicationId`,
    GET_APPLICATION_FINAL_SUMMARY: `${URL}/candidates/application-final-summary/:jobApplicationId`,
    GET_APPLICATION_SKILL_SCORE_DATA: `${URL}/candidates/application-skill-score-data/:jobApplicationId`,
    GENERATE_FINAL_SUMMARY: `${URL}/candidates/generate-final-summary`, // Endpoint for generating final summary
  },
  subscription: {
    GET_ALL_PLANS: `${URL}/subscription/all`,
    GET_CURRENT_SUBSCRIPTION: `${URL}/subscription/current`,
    CANCEL_SUBSCRIPTION: `${URL}/subscription/cancel`,
    GET_TRANSACTIONS: `${URL}/subscription/transactions`,
    BUY_SUBSCRIPTION: `${URL}/subscription/buy-subscription`,
    JOB_POSTING_QUOTA: `${URL}/subscription/job-posting-quota`,
  },
};

export default endpoint;
