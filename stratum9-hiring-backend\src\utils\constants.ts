export const EMAIL_REGEX = /^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$/;

export const DEFAULT_VALUE = 0;

export const PASSWORD_REGEX =
  /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;

export const PHONE_REGEX =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;

export const IMAGE_REGEX =
  /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/;

export const LIMIT = "5mb";
export const ASSESSMENT_ID_REGEX = /assessment_(\d+)_\d+/;

export const ENV_VARIABLE = {
  LOCAL: "local",
};

export const DB_CONST = {
  TYPE: "mysql",
  CHARSET: "utf8mb4_unicode_ci",
  S9_NAME: "s9_db",
  S9_INNERVIEW_NAME: "s9iv_db",
};

export const BASE_ROUTES = {
  ACCESS_MANAGEMENT: "/api/v1/access-management",
  EMPLOYEE_MANAGEMENT: "/api/v1/employee-management",
  AUTH: "/api/v1/auth",
  INTERVIEW: "/api/v1/interview",
  JOB_REQUIREMENT: "/api/v1/jobs",
  FINAL_ASSESSMENT: "/api/v1/final-assessment",
  RESUME_SCREEN: "/api/v1/resume-screen",
  CANDIDATES: "/api/v1/candidates",
  NOTIFICATIONS: "/api/v1/notifications",
  USER_PROFILE: "/api/v1/user-profile",
  COMMON: "/api/v1",
  SUBSCRIPTION: "/api/v1/subscription",
  STRIPE: "/api/v1/stripe",
  WEBHOOKS: "/api/v1/webhooks",
};

export const SOCKET_ROUTES = {
  CONDUCT_INTERVIEW: "/conduct-interview",
};

export const INTERVIEW_TRANSCRIPT_KEY = "interview:transcript:";

export const ROUTES = {
  // Access Management Routes
  ADD_ROLE_DEPARTMENT_AND_PERMISSION_FOR_ADMIN:
    "/add-role-department-and-permission-for-admin",
  GET_USER_ROLES: "/user-roles",
  GET_USER_ROLES_PAGINATION: "/user-roles-pagination",
  ADD_USER_ROLE: "/add-user-role",

  // Subscription Routes
  SUBSCRIPTION: {
    CURRENT: "/current",
    ALL_PLANS: "/all",
    CANCEL: "/cancel",
    TRANSACTIONS: "/transactions",
    GET_SUBSCRIPTIONS_NAME: "/get-subscriptions-name", // not in use
    BUY_SUBSCRIPTION: "/buy-subscription",
    JOB_POSTING_QUOTA: "/job-posting-quota",
  },

  // Webhook Routes
  WEBHOOKS: {
    STRIPE: "/stripe",
  },
  USER_PERMISSIONS: "/user-permissions",
  JOBS: {
    GENERATE_SKILLS: "/generate-skills",
    UPLOAD_URL: "/upload-url",
    PARSE_PDF: "/parse-pdf",
    GET_ALL_SKILLS: "/get-all-skills",
    GENERATE_JOB_REQUIREMENT: "/generate-job-requirement",
    SAVE_JOB_DETAILS: "/save-job-details",
    GET_JOBS_META: "/get-jobs-meta",
    UPDATE_JOB: "/updateJob/:id",
    DASHBOARD_COUNTS: "/dashboard-counts",
    GET_JOB_HTML_DESCRIPTION: "/get-job-html-description",
    UPDATE_JOB_DESCRIPTION: "/update-job-description",
    GENERATE_PDF: "/generate-pdf",
  },
  CANDIDATES: {
    GET_ALL_HIRED_CANDIDATE: "/get-all-hired-candidate",
    ADD_APPLICANT_ADDITIONAL_INFO: "/add-applicant-additional-info",
    GET_CANDIDATE_DETAILS: "/get-candidate-details",
    GET_TOP_CANDIDATES: "/top-candidates",
    PROMOTE_DEMOTE_CANDIDATE: "/update-candidate-rank-status",
    GET_CANDIDATES: "/get-candidates",
    ARCHIVE_ACTIVE_APPLICATION: "/archive-active-application/:applicationId",
    UPDATE_JOB_APPLICATION_STATUS:
      "/update-job-application-status/:jobApplicationId",
    GET_CANDIDATE_INTERVIEW_HISTORY:
      "/get-candidate-interview-history/:jobApplicationId",
    APPLICATION_FINAL_SUMMARY: "/application-final-summary/:jobApplicationId",
    APPLICATION_SKILL_SCORE_DATA:
      "/application-skill-score-data/:jobApplicationId",
    GENERATE_FINAL_SUMMARY: "/generate-final-summary",
  },
  RESUME_SCREEN: {
    MANUAL_CANDIDATE_UPLOAD: "/manual-candidate-upload",
    GET_ALL_PENDING_JOB_APPLICATIONS: "/get-all-pending-job-applications",
    CHANGE_APPLICATION_STATUS: "/change-application-status",
    GET_PRESIGNED_URL: "/get-presigned-url",
  },

  INTERVIEW: {
    UPDATE_OR_SCHEDULE_INTERVIEW: "/update-or-schedule-interview",
    GET_INTERVIEWS: "/get-interviews",
    GET_INTERVIEWERS: "/get-interviewers",
    GET_MY_INTERVIEWS: "/get-my-interviews",
    GET_UPCOMING_OR_PAST_INTERVIEWS: "/get-upcoming-or-past-interviews",
    GET_INTERVIEW_SKILL_QUESTIONS: "/get-interview-skill-questions",
    UPDATE_INTERVIEW_SKILL_QUESTION: "/update-interview-skill-question",
    ADD_INTERVIEW_SKILL_QUESTION: "/add-interview-skill-question",
    UPDATE_INTERVIEW_ANSWERS: "/update-interview-answers",
    GET_JOB_LIST: "/get-job-list",
    GET_CANDIDATE_LIST: "/get-candidate-list",
    END_INTERVIEW: "/end-interview",
    CONDUCT_INTERVIEW_STATIC_INFORMATION:
      "/conduct-interview-static-information",
  },
  NOTIFICATIONS: {
    DELETE_USERS_ALL_NOTIFICATIONS: "/delete-users-all-notifications",
    GET_NOTIFICATIONS: "/get-notifications",
    MARK_AS_WATCHED: "/mark-as-watched",
    GET_UNREAD_NOTIFICATIONS_COUNT: "/get-unread-notifications-count",
  },

  USER_ROLE: "/user-role",
  USER_ROLE_WITH_ID: "/user-role/:roleId",
  ROLE_PERMISSIONS: "/role-permissions",
  ROLE_PERMISSIONS_WITH_ID: "/role-permissions/:roleId",

  // Auth Routes
  AUTH: {
    SIGN_IN: "/sign-in",
    FORGOT_PASSWORD: "/forgot-password",
    RESET_PASSWORD: "/reset-password",
    DELETE_SESSION: "/delete-session/:userId",
    USER_EXISTS: "/user-exists",
    VERIFY_OTP: "/verify-otp",
    RESEND_OTP: "/resend-otp",
    UPDATE_TIMEZONE: "/update-timezone",
  },

  COMMON: {
    REMOVE_ATTACHMENTS_FROM_S3: "/remove-attachments-from-s3",
    GENERATE_PRE_SIGNED_URL: "/generate-presignedurl",
  },

  // Employee Management Routes
  DEPARTMENTS: "/departments",
  ADD_DEPARTMENT: "/add-department",
  DEPARTMENT_WITH_ID: "/update-department/:departmentId",
  DELETE_DEPARTMENT: "/delete-department/:departmentId",
  DEPARTMENTS_BY_ORGANIZATION: "/departments/organization/:organizationId",
  UPDATE_EMPLOYEE_ROLE: "/employee/:employeeId/role",
  EMPLOYEES: "/employees",
  ADD_EMPLOYEES: "/add-hiring-employee",
  EMPLOYEE_WITH_ID: "/employee/:employeeId",
  EMPLOYEE_ROLE: "/employee/:employeeId/role",
  DELETE_EMPLOYEE: "/employee/:employeeId",
  UPDATE_EMPLOYEE_INTERVIEW_ORDER: "/employee/:employeeId/interview-order",
  UPDATE_EMPLOYEE_STATUS: "/employee/change-status/:employeeId",
  GET_EMPLOYEES_BY_ORGANIZATION_ID: "/get-employees-by-organization-id",

  FINAL_ASSESSMENT: {
    CREATE_FINAL_ASSESSMENT: "/create-final-assessment",
    GENERATE_QUESTIONS: "/generate-questions",
    ADD_MANUAL_QUESTION: "/assessment/create-question",
    GET_FINAL_ASSESSMENT_QUESTION: "/assessment/questions",
    SHARE_ASSESSMENT: "/assessment/share",
    GET_FINAL_ASSESSMENT_BY_CANDIDATE: "/candidate/assessment",
    SUBMIT_ASSESSMENT: "/candidate/assessment/submit",
    GET_ASSESSMENT_STATUS: "/assessment-status",
    VERIFY_CANDIDATE_EMAIL: "/candidate/verify-email",
    GENERATE_ASSESSMENT_TOKEN: "/assessment/generate-token",
  },
  // Hire Employee Routes
  ADD_HIRING_EMPLOYEE: "/add-hiring-employee",

  USER_PROFILE: {
    GET_MY_PROFILE: "/get-my-profile",
    UPDATE_MY_PROFILE: "/update-my-profile",
  },
};

// update as per your use
export const LABELS = {
  city: "City",
  location: "Location",
  country: "Country",
  country_code: "Country Code",
  sms_notification: "Sms Notification",
  state: "State",
  phone: "Phone",
  email: "Email",
  age: "Age",
  gender: "Gender",
  lastName: "Last Name",
  password: "Password",
  firstName: "First Name",
  occupation: "Occupation",
  company: "Company",
  organizationCode: "Organization Code",
  image: "Image",
};

export const PLATFORM = {
  STRATUM9_INNERVIEW: "stratum9_innerview",
};

export const INTERVIEW_EMAIL_TYPE = {
  SCHEDULE: "schedule",
  UPDATE: "update",
};

// update as per your use
export const API_RESPONSE_MSG = {
  pdf_parsing_failed: "Error in parsing PDF file",
  interview_already_ended: "interview_already_ended",
  invalid_data: "invalid_data",
  unauthorized: "unauthorized",
  otp_sending_failed: "otp_sending_failed",
  otp_expired: "otp_has_expired_please_request_a_new_otp",
  interviews_fetched: "interviews_fetched",
  interviewers_fetched: "interviewers_fetched",
  interview_question_added: "interview_question_added",
  interview_is_ended: "interview_is_ended",
  job_application_is_not_approved: "job_application_is_not_approved",

  cannot_update_interview_after_ended: "cannot_update_interview_after_ended",
  interview_question_updated: "interview_question_updated",
  question_not_found: "question_not_found",
  presigned_url_generated: "presigned_url_generated",
  previously_scheduled_interview_is_not_ended:
    "previously_scheduled_interview_is_not_ended",
  cannot_add_interview_skill_question_after_ended:
    "cannot_add_interview_skill_question_after_ended",
  interview_ended: "interview_ended",

  email_exist: "email_exist",
  unsupported_email: "unsupported_email",
  interview_already_scheduled: "interview_already_scheduled",
  interview_scheduled_successfully: "interview_scheduled_successfully",

  cannot_schedule_interview_past: "cannot_schedule_interview_past",
  interview_must_be_at_least_10_min: "interview_must_be_at_least_10_min",
  end_time_must_be_after_start_time: "end_time_must_be_after_start_time",
  interview_must_not_exceed_2_hours: "interview_must_not_exceed_2_hours",
  cannot_schedule_more_than_one_month_in_advance:
    "cannot_schedule_more_than_one_month_in_advance",
  interview_not_found: "interview_not_found",
  max_career_based_questions_reached: "max_career_based_questions_reached",
  max_role_specific_questions_reached: "max_role_specific_questions_reached",
  max_culture_specific_questions_reached:
    "max_culture_specific_questions_reached",

  success: "success",
  verification_code_sent: "verification_code_sent",
  otp_verified: "otp_verified_successfully",

  submitted: "submitted_successfully",

  password_not_updated: "password_not_updated",
  user_not_found: "user_not_found",
  password_updated: "password_updated",

  password_update_failed: "password_update_failed",
  profile_updated: "profile_updated",
  user_not_exist: "user_not_exist",
  forgot_user: "email_entered_is_not_associated_with_any_registered_account",
  failed: "something_went_wrong",
  user_session_deleted: "user_session_deleted",

  wrong_password: "wrong_password",
  wrong_current_pass: "wrong_current_pass",
  login_successful: "login_successful",
  email_not_found: "email_not_found",
  email_not_verified: "email_not_verified",
  login_failed: "login_failed",

  try_again: "something_wrong_try_again",
  reset_password: "reset_password_successfully",
  reset_password_error: "reset_password_error",

  wrong_otp: "please_enter_a_valid_verification_code",
  fetch_success: "fetch_success",
  skills_generated: "skills_generated",
  fetch_failed: "fetch_failed",
  skills_generation_failed: "skills_generation_failed",
  job_requirement_generated: "job_requirement_generated",
  job_requirement_generation_failed: "job_requirement_generation_failed",
  job_details_saved: "job_details_saved",
  job_details_save_failed: "job_details_save_failed",

  job_application_not_found: "job_application_not_found",
  invalidParams: "invalid_params",
  job_fetch_success: "job_fetch_success",
  job_update_success: "job_update_success",
  pdf_generated_success: "pdf_generated_success",
  pdf_generation_failed: "pdf_generation_failed",
  dashboard_counts_fetch_success: "dashboard_counts_fetch_success",
  dashboard_counts_fetch_failed: "dashboard_counts_fetch_failed",
  no_pdf_file_uploaded: "no_pdf_file_uploaded",
  internal_server_error: "internal_server_error",
  job_description_processed_successfully:
    "job_description_processed_successfully",
  failed_to_process_the_pdf_file_ensure_it_is_a_valid_pdf:
    "failed_to_process_the_pdf_file_ensure_it_is_a_valid_pdf",
  failed_to_extract_form_fields: "failed_to_extract_form_fields",
  job_description_processed_failed: "job_description_processed_failed",
  job_not_found: "job_not_found",
  failed_to_update_job: "Failed to update job",
  jobs_meta_fetch_success: "Job metadata fetched successfully",
  failed_to_fetch_jobs_meta: "Failed to fetch job metadata",
  open_ai_key_not_configured: "OpenAI API key not configured",
  failed_to_initialize_openai_client: "Failed to initialize OpenAI client",
  open_ai_client_not_initilezed: "OpenAI client not initialized",
};

// subscription messages
export const SUBSCRIPTION_MSG = {
  invalid_plan: "invalid_or_inactive_plan_id",
  invalid_pricing: "invalid_pricing_id_for_the_given_plan",
  stripe_customer_not_found: "no_stripe_customer_found_for_the_organization",
  stripe_customer_deleted: "stripe_customer_not_found_or_has_been_deleted",
  stripe_customer_error: "error_retrieving_stripe_customer",
  no_active_subscription: "no_active_subscription_found_for_this_organization",
  subscription_plan_not_found: "subscription_plan_or_pricing_details_not_found",
  subscription_retrieved: "current_subscription_retrieved_successfully",
  subscription_retrieve_error: "error_retrieving_current_subscription",
  subscription_canceled:
    "subscription_will_be_expired_at_the_end_of_the_current_billing_cycle",
  subscription_cancel_error: "error_cancelling_subscription",
  pricing_not_found: "pricing_not_found",
  checkout_session_created: "checkout_session_created_successfully",
  checkout_session_error: "error_creating_checkout_session",
  customer_exists: "customer_already_exists",
  customer_created: "stripe_customer_created_successfully",
  customer_creation_error: "error_creating_stripe_customer",
  plans_retrieved: "all_available_plans_retrieved_successfully",
  plans_retrieve_error: "error_retrieving_subscription_plans",
  webhook_processed: "webhook_processed_successfully",
  webhook_error: "error_processing_webhook",
  organization_not_found: "organization_not_found",
  plan_same:
    "you_have_already_selected_this_plan_please_choose_a_different_plan_to_proceed",

  // General subscription messages (specific limit messages are now generated dynamically)
  using_free_plan_benefit: "using_free_plan_benefit_quota",
  benefit_quota_available: "benefit_quota_available",
  invalid_benefit_type: "invalid_benefit_type",
  subscription_benefit_check_error: "error_checking_subscription_benefit",
  job_posting_limit_reached: "job_posting_limit_reached",
  job_posting_quota_retrieved_successfully:
    "job_posting_quota_retrieved_successfully",
  no_subscription_benefits_found: "no_subscription_benefits_found",
  failed_to_retrieve_job_posting_quota: "failed_to_retrieve_job_posting_quota",
  resume_screening_limit_reached: "resume_screening_limit_reached",
  manual_resume_upload_limit_reached:
    "Only {quota_value} candidate resume upload allowed. Please upgrade for more.",
  no_subscription_plan_found_and_no_free_plan_available:
    "no_subscription_plan_found_and_no_free_plan_available",
  no_subscription_found_and_error_retrieving_free_plan:
    "no_subscription_found_and_error_retrieving_free_plan",
  stripeSubscriptionId_not_found: "stripeSubscriptionId_not_found",
};

// Job posting quota status constants
export const JOB_POSTING_QUOTA_STATUS = {
  UNLIMITED: "unlimited",
  EXHAUSTED: "exhausted",
  AVAILABLE: "available",
};

// Add custom API response messages
export const ACCESS_MANAGEMENT_MSG = {
  user_roles_fetch: "user_roles_fetch",
  user_role_added: "user_role_added",
  user_role_updated: "user_role_updated",
  user_role_deleted: "user_role_deleted",
  role_already_exists: "role_already_exists",
  role_not_found: "role_not_found",
  role_permissions_fetch: "role_permissions_fetch",
  role_permissions_updated: "role_permissions_updated",
  at_least_one_permission_required: "at_least_one_permission_required",
  add_failed: "add_failed",
  update_failed: "update_failed",
  delete_failed: "delete_failed",
  role_has_employees: "role_has_employees",
  default_role_update_not_allowed: "default_role_update_not_allowed",
  no_update_in_permissions: "no_update_in_permissions.",
};

export const FINAL_ASSESSMENT_MSG = {
  final_assessment_created: "final_assessment_created",
  final_assessment_questions_created: "final_assessment_questions_created",
  job_not_found: "job_not_found",
  job_application_not_found: "job_application_not_found",
  assessment_already_exists: "assessment_already_exists",
  assessment_status_fetched: "assessment_status_fetched",
  assessment_already_shared: "assessment_already_shared",
  assessment_already_submitted: "assessment_already_submitted",
  assessment_created: "assessment_created",
  assessment_not_found: "assessment_not_found",
  no_questions_found: "no_questions_found",
  questions_fetched: "questions_fetched",
  skill_not_found: "skill_not_found",
  candidate_not_found: "candidate_not_found",
  failed_to_send_assessment_email: "failed_to_send_assessment_email",
  assessment_shared_successfully: "assessment_shared_successfully",
  invalid_assessment_link: "invalid_assessment_link",
  invalid_question_ids: "invalid_question_ids",
  assessment_submitted_successfully: "assessment_submitted_successfully",
  email_mismatch: "email_mismatch",
  email_verified_successfully: "email_verified_successfully",
  unauthorized_access: "unauthorized_access",
  not_authenticated: "not_authenticated",
  assessment_has_expired: "assessment_has_expired",
};

export const EMPLOYEE_MANAGEMENT_MSG = {
  departments_fetch: "departments_fetch",
  department_added: "department_added",
  department_deleted: "department_deleted",
  department_updated: "department_updated",
  department_already_exists: "department_already_exists",
  department_not_found: "department_not_found",
  department_has_employees: "Cannot delete department with active employees",
  departments_by_organization_fetch: "departments_by_organization_fetch",
  employees_fetch: "employees_fetch",
  employees_added: "employees_added",
  employee_role_updated: "employee_role_updated",
  employee_deleted: "employee_deleted",
  employee_not_found: "employee_not_found",
  email_already_exists: "email_already_exists",
  add_failed: "add_failed",
  update_failed: "update_failed",
  delete_failed: "delete_failed",
  employee_interview_order_updated: "employee_interview_order_updated",
  employee_status_updated: "employee_status_updated",
  department_id_required: "department_id_required",
  name_required: "name_required",
  organization_id_required: "organization_id_required",
  no_employees_data: "no_employees_data",
  cannot_add_employee_user_registered_with_another_org:
    "cannot_add_employee_user_registered_with_another_org",
  employee_already_registered: "employee_already_registered",
  employee_already_registered_with_diff_org:
    "employee_already_registered_with_diff_org",
  employee_added: "employee_added",
  no_update_data_provided: "no_update_data_provided",
  default_department_cannot_be_deleted: "default_department_cannot_be_deleted",
  same_as_current_order: "same_as_current_order",
  invalid_sort_order: "invalid_sort_order",
  error_updating_interview_order: "error_updating_interview_order",
  user_roles_fetch: "user_roles_fetch",
  user_role_added: "user_role_added",
  user_role_updated: "user_role_updated",
  user_role_deleted: "user_role_deleted",
  role_already_exists: "role_already_exists",
  role_permissions_fetch: "role_permissions_fetch",
  role_permissions_updated: "role_permissions_updated",
  at_least_one_permission_required: "at_least_one_permission_required",
  role_has_employees: "role_has_employees",
  cannot_update_role: "cannot_update_role",
  cannot_update_default_department: "cannot_update_default_department",
  user_profile_fetched: "user_profile_fetched",
  you_are_not_authorized_to_update_this_user_profile:
    "you_are_not_authorized_to_update_this_user_profile",
  failed_to_update_user_profile: "failed_to_update_user_profile",
  user_profile_updated: "user_profile_updated",
  please_select_valid_role: "please_select_valid_role",
  please_select_valid_department: "please_select_valid_department",
  cannot_delete_employee_with_active_jobs:
    "cannot_delete_employee_with_active_jobs",
  cannot_delete_employee_with_active_job_applications:
    "cannot_delete_employee_with_active_job_applications",
  cannot_delete_employee_with_upcoming_interviews:
    "cannot_delete_employee_with_upcoming_interviews",
  cannot_delete_employee_with_job_application_status_changes:
    "cannot_delete_employee_with_job_application_status_changes",
};

// Authorization related messages
export const AUTH_MSG = {
  permission_not_available: "You don't have permission to access this feature.",
  authorization_error: "Internal server error during authorization check",
  unauthorized_role: "Unauthorized access - Role not found",
};

export const INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS = {
  BEGINNER: "beginner",
  INTERMEDIATE: "intermediate",
  ADVANCED: "advanced",
};

export const MESSAGE_TYPE = {
  SENT: "Sent",
};
export const PASSWORD_HASH_LENGTH = 8;

export const USER_TYPE = {
  user: "user",
  admin: "admin",
};
export const REQUIRED_TYPES = [
  "Mentality",
  "Cognitive Abilities",
  "Mastery of Emotions",
  "Social Interaction",
  "Personal Health",
];

export const DEFAULT_OFFSET = 0;
export const DEFAULT_LIMIT = 15;

/**
 * Redis expiry durations in seconds
 */
export const REDIS_EXPIRY = {
  // 60 seconds * 60 minutes * 24 hours * 30 days
  DEFAULT: 60 * 60 * 24 * 30, // 2,592,000 seconds (30 days)

  // 60 seconds * 60 minutes * 24 hours * 7 days
  PERMISSIONS_UPDATE_STATUS: 60 * 60 * 24 * 7, // 604,800 seconds (7 days)
};

export const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [
  "Arrive at the interview location on time with a government-issued ID.",
  "Ensure your phone is on silent mode and distractions are minimized.",
  "Bring a printed copy of your resume and any supporting documents.",
  "Dress professionally and maintain proper body language.",
  "Listen carefully, answer honestly, and ask for clarification if needed.",
  "Respect the interview flow and do not interrupt the interviewer.",
  "Take brief notes if necessary, but focus on active conversation.",
  "If you need assistance or face any issues, notify the interview coordinator.",
];

export const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [
  "Join the interview on time using the link provided.",
  "Ensure a stable internet connection and a quiet, well-lit space.",
  "Test your camera, microphone, and audio settings in advance.",
  "Keep your video on unless instructed otherwise by the interviewer.",
  "Minimize background noise and avoid multitasking during the session.",
  "Use headphones if possible for better audio clarity.",
  "Be attentive, respond clearly, and maintain professional posture.",
  "Contact support if you face technical difficulties before or during the interview.",
];

export const STRATUM_POINT_DESCRIPTION = [
  {
    id: 1,
    description:
      "You are completely unaware of a skill as it relates to your performance. You have not considered the skill’s importance, nor do you have any competency in it. It may be something you do without intention behind it, and it is most likely an innate or learned behavior. Low performance in this skill could be a blind spot resulting from a lack of feedback or coaching.",
  },
  {
    id: 2,
    description:
      'You are aware of a skill, but you are unconcerned about how it relates to your performance. The blind spot has been uncovered, but you might lack the desire or motivation to develop this behavior into a skill. This is the stratum where many individuals get stuck because of inexperience, lack of coaching, stubbornness, or being too "set in their ways." As a result, their performance remains stagnant.',
  },
  {
    id: 3,
    description:
      "The “aha” moment is when you realize your deficiency in skill and how it may be affecting your performance. You have a fundamental awareness of the skill but lack the knowledge needed to increase your competency. You may privately ponder or even reconfirm your deficiency with others, but you lack the tools for learning the basic fundamentals needed to improve. Conversely, this “aha” moment can also translate to discovering that you are too extreme in skill and now realizing it is the cause of a deficiency. You are at the starting point for learning a skill and improving your performance.",
  },
  {
    id: 4,
    description:
      "You begin to seek tools to improve your deficiency in skill. You now know how it relates to your performance and you begin to actively seek tools and resources for learning. Although you may find the tools, you don’t always begin using them right away. You are a novice because you are in the process of seeking knowledge and experience. This is an important step showcasing a turning point from talking about change to taking action to make change happen.",
  },
  {
    id: 5,
    description:
      "You are beginning to use the tools to pursue the knowledge of the steps necessary for improvement. You are actively learning; however, you are not yet applying what you have learned. Perhaps it’s because you are afraid to make a mistake due to a lack of confidence. This can be a challenging stratum because it’s easy to get stuck only learning. Through learning, you will gain an increased awareness of how the skills impact your performance. Because of this awareness, you may see a slight improvement in your performance, but you are not yet consciously applying.",
  },
  {
    id: 6,
    description:
      "You are applying what you have learned. Your current application may be basic in concept, unbalanced, and messy in execution. That’s OK because only trial and error will improve your current intermediate competency. You may become discouraged if your failures outnumber your successes. Push through by seeking feedback and coaching. Encouragement will come from those who recognize your efforts and may result in feedback that can help drive your development. Watch out, though this level can act as a yo-yo if you only apply what you learn when it suits you. Many times, you may revert back to your old ways. This leads to inconsistent behavior, which can negatively impact your performance.",
  },
  {
    id: 7,
    description:
      "You are practicing what you have learned in order to improve your performance. The results of your application drive your desire to eliminate your deficiencies. Your confidence is raised because of the results you are getting. Your competency is advanced. However, you are still inconsistent day-to-day and you begin to see the need for discipline and habit formation in order to improve. You are more aware of when you make mistakes, are unbalanced, or become too extreme in your approach. You reflect on your efforts to improve and discuss your errors with others to seek guidance. Your performance is noticeably and positively impacted.",
  },
  {
    id: 8,
    description:
      "You begin to experience expert levels of proficiency, consistency, and confidence in the skill. Your learning and application have progressed to advanced techniques, and errors are reduced. Practice, awareness, and reflection happen daily. You are disciplined in your application, resulting in early stages of habit formation (and correction). The 8th Stratum is very important because you have developed a new level of self-confidence and that behavior has now become a skill, so you can use it strategically to positively affect your performance.",
  },
  {
    id: 9,
    description:
      "You are a performance leader in this skill because you are highly competent and consistent, and are able to teach it to others. You have intentionally established a conscious daily discipline to achieve results. Strong positive habits have been formed and you use habit correction and adaptation as functional tools. You may have developed a routine or held a specific discipline in application to maintain consistency. In addition, you work to develop new and more advanced techniques and have a constant desire to learn more, innovate, and educate others. This is the stratum of high performance that results in increased consistency in achieving goals. These nine stratums can be applied to all of your specific pursuits of goals, as well as any learned skill. You can use the nine stratums of performance to evaluate and reflect on your current level of operating as you move through the pages of this book. You may find that areas you thought you were excelling in are actually hindering your performance.",
  },
];

// update as per your use
export const REDIS_KEYS = {
  ROLE_PERMISSIONS: "innerview-role-{roleId}-permissions",
  ROLE_PERMISSIONS_UPDATE_STATUS:
    "innerview-role-{roleId}-permission_update_status",
  USER_SESSIONS: "innerview-user-{userId}-sessions",
  INTERVIEW_TRANSCRIPT_KEY: "interview:transcript:",
  CONDUCT_INTERVIEW_INFORMATION: "interview:conduct:information",
};
export const DEFAULT_COUNTRY_CODE = "1";

export const DEFAULT_ORG_FIELDS = {
  DEPARTMENT: "Administrator",
  ROLE: "Admin",
};
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
// eslint-disable-next-line import/export
export enum QuestionType {
  MCQ = "mcq",
  TRUE_FALSE = "true_false",
}

export const GPT_MODEL = "gpt-4.1";
export const SKILLS_CACHE_KEY = "skills_data";

/**
 * Permission slugs for authorization
 */
export const PERMISSION = {
  CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
  SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
  VIEW_HIRED_CANDIDATES: "view-hired-candidates",
  ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
  ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
  MANUAL_RESUME_SCREENING: "manual-resume-screening",
  EDIT_SCHEDULED_INTERVIEWS: "edit-scheduled-interviews",
  ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
  ADD_OR_EDIT_INTERVIEW_NOTES: "add-or-edit-interview-notes",
  MANAGE_TOP_CANDIDATES: "manage-top-candidates", // manage promote and demote candidates
  MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
  CREATE_FINAL_ASSESSMENT: "create-final-assessment",
  VIEW_FINAL_ASSESSMENT: "view-final-assessment",
  VIEW_CANDIDATE_PROFILE_SUMMARY: "view-candidate-profile-summary",
  HIRE_CANDIDATE: "hire-candidate",
  CREATE_NEW_ROLE: "create-new-role",
  MANAGE_USER_PERMISSIONS: "manage-user-permissions",
  CREATE_NEW_DEPARTMENT: "create-new-department",
  ADD_INTERVIEW_PARTICIPANTS: "add-interview-participants",
  VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
  MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
  VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
  VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews",
};

export const CANDIDATE_APPLICATION_MSG = {
  interview_not_ended: "interview_not_ended",
  get_hired_candidate_success: "get_hired_candidate_success",
  get_hired_candidate_failed: "get_hired_candidate_failed",
  final_assessment_not_exists: "final_assessment_not_exists",
  candidates_fetched: "candidates_fetched",
  get_all_candidates_failed: "get_all_candidates_failed",
  job_application_not_found: "job_application_not_found",
  update_application_status_success: "updated_application_status_success",
  update_application_status_failed: "update_application_status_failed",
  top_candidates_retrieved: "top_candidates_retrieved",
  get_top_candidates_failed: "get_top_candidates_failed",
  candidate_application_not_found: "candidate_application_not_found",
  update_rank_status_failed: "update_rank_status_failed",
  update_rank_status_success: "update_rank_status_success",
  candidate_not_found: "candidate_not_found",
  fetch_candidate_details_failed: "fetch_candidate_details_failed",
  candidate_not_found_for_org: "candidate_not_found_for_org",
  additional_info_saved: "additional_info_saved",
  save_additional_info_failed: "save_additional_info_failed",
  interview_history_retrieved: "interview_history_retrieved",
  get_interview_history_failed: "get_interview_history_failed",
  skill_specific_assessment_retrieved: "skill_specific_assessment_retrieved",
  application_final_summary_retrieved: "application_final_summary_retrieved",
  application_final_summary_failed: "application_final_summary_failed",
  get_skill_specific_assessment_failed: "get_skill_specific_assessment_failed",
  unknown_error: "Unknown error occurred",
  no_interviews_found: "no_interviews_found",
  final_summary_generated_successfully: "final_summary_generated_successfully",
  generate_final_summary_failed: "generate_final_summary_failed",
  no_skill_score_data_found: "no_skill_score_data_found",
  no_interview_found_to_update_status: "no_interview_found_to_update_status",
  interviews_not_completed: "interviews_not_completed",
  interview_feedback_pending: "interview_feedback_pending",
};

export const PDF_CONTENT_TYPE = "application/pdf";

export const ATTEMPT = 0;
export const PDF_PARSING_MAX_ATTEMPTS = 5;
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const CANDIDATE_AVATAR_URL = {
  "1": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-1.png",
  "2": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-2.png",
  "3": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-3.png",
  "4": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-4.png",
  "5": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-5.png",
  "6": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-6.png",
  "7": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-7.png",
  "8": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-8.png",
  "9": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-9.png",
  "10": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-10.png",
  "11": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-11.png",
  "12": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-12.png",
  "13": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-13.png",
  "14": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-14.png",
  "15": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-15.png",
  "16": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-16.png",
  "17": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-17.png",
  "18": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-1.png",
  "19": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-2.png",
  "20": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-3.png",
  "21": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-4.png",
  "22": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-5.png",
  "23": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-6.png",
  "24": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-7.png",
  "25": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-8.png",
  "26": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-9.png",
  "27": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-10.png",
  "28": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-11.png",
  "29": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-12.png",
  "30": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-13.png",
};
export const SCHEDULE_INTERVIEW_ONE_MONTH_MS = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
export const SCHEDULE_INTERVIEW_MINUTES_MS = 10 * 60 * 1000; // 10 minutes in milliseconds
export const MAX_HOURS_BETWEEN_START_AND_END_TIME = 2 * 60 * 60 * 1000; // 2 hours in milliseconds

export const BENEFIT_SLUGS = {
  JOB_POSTINGS: "job_postings",
  RESUME_SCREENING: "resume_screening",
  MANUAL_RESUME_UPLOAD: "manual_resume_upload",
};

export const ActivityLogType = {
  SUBSCRIPTION_UPDATE: "Subscription Update",
  JOB_POSTING: "Job Posting",
  LOGIN: "Login",
  HIRE_REJECT_CANDIDATE: "Hire/Reject Candidate",
  ADD_EMPLOYEE: "Add Employee",
  CHANGE_ACCESS_ROLE: "Change Access Role",
  UPDATE_PERMISSIONS: "Update Permissions",
  SCHEDULE_INTERVIEW: "Schedule Interview",
  INTERVIEW_FEEDBACK: "Interview Feedback",
  CANDIDATE_SUMMARY: "Candidate Summary",
};
