{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/UploadDocumentIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype UploadDocumentIconProps = {\n  className?: string;\n};\n\nfunction UploadDocumentIcon({ className }: UploadDocumentIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"52\" height=\"52\" viewBox=\"0 0 52 52\" fill=\"none\" className={className}>\n      <g opacity=\"0.7\" clipPath=\"url(#clip0_9593_10462)\">\n        <path d=\"M32.5 17.332H32.5206\" stroke=\"#333333\" strokeWidth=\"2.6\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n        <path\n          d=\"M6.5 13C6.5 11.2761 7.18482 9.62279 8.40381 8.40381C9.62279 7.18482 11.2761 6.5 13 6.5H39C40.7239 6.5 42.3772 7.18482 43.5962 8.40381C44.8152 9.62279 45.5 11.2761 45.5 13V39C45.5 40.7239 44.8152 42.3772 43.5962 43.5962C42.3772 44.8152 40.7239 45.5 39 45.5H13C11.2761 45.5 9.62279 44.8152 8.40381 43.5962C7.18482 42.3772 6.5 40.7239 6.5 39V13Z\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M6.5 34.6673L17.3333 23.8339C19.344 21.8991 21.8227 21.8991 23.8333 23.8339L34.6667 34.6673\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M30.3281 30.3326L32.4948 28.166C34.5055 26.2311 36.9841 26.2311 38.9948 28.166L45.4948 34.666\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9593_10462\">\n          <rect width=\"52\" height=\"52\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default UploadDocumentIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,mBAAmB,EAAE,SAAS,EAA2B;IAChE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAE,SAAQ;gBAAM,UAAS;;kCACxB,8OAAC;wBAAK,GAAE;wBAAuB,QAAO;wBAAU,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;kCACvG,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonComponent/UploadBox.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport UploadDocumentIcon from \"../svgComponents/UploadDocumentIcon\";\nimport { useTranslations } from \"next-intl\";\n\ninterface UploadBoxProps {\n  UploadBoxClassName?: string;\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  inputRef?: React.RefObject<HTMLInputElement | null>;\n  isLoading?: boolean;\n  uploadingMessages?: string[]; // Optional array of dynamic messages\n  messageInterval?: number; // Optional interval for message rotation (default: 2000ms)\n}\n\nconst UploadBox = ({ UploadBoxClassName, onChange, inputRef, isLoading, uploadingMessages, messageInterval = 2000 }: UploadBoxProps) => {\n  const t = useTranslations();\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\n\n  // Dynamic message rotation effect\n  useEffect(() => {\n    if (!isLoading || !uploadingMessages || uploadingMessages.length <= 1) {\n      return;\n    }\n\n    const interval = setInterval(() => {\n      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % uploadingMessages.length);\n    }, messageInterval);\n\n    return () => clearInterval(interval);\n  }, [isLoading, uploadingMessages, messageInterval]);\n\n  // Reset message index when loading starts\n  useEffect(() => {\n    if (isLoading) {\n      setCurrentMessageIndex(0);\n    }\n  }, [isLoading]);\n\n  // Determine what message to show during loading\n  const getLoadingMessage = () => {\n    if (uploadingMessages && uploadingMessages.length > 0) {\n      return uploadingMessages[currentMessageIndex];\n    }\n    return t(\"uploading\");\n  };\n\n  return (\n    <div className={`upload-card ${UploadBoxClassName}`}>\n      <input type=\"file\" accept=\".pdf\" onChange={onChange} disabled={isLoading} ref={inputRef} />\n      <div className=\"upload-box-inner\">\n        <UploadDocumentIcon />\n        {!isLoading ? (\n          <p>\n            {t(\"upload_doc\")}\n            <br />\n            {t(\"max_file_size\")}\n          </p>\n        ) : (\n          <p className=\"uploading-message\">{getLoadingMessage()}</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UploadBox;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAWA,MAAM,YAAY,CAAC,EAAE,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAkB;IACjI,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,qBAAqB,kBAAkB,MAAM,IAAI,GAAG;YACrE;QACF;QAEA,MAAM,WAAW,YAAY;YAC3B,uBAAuB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,kBAAkB,MAAM;QAClF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAW;QAAmB;KAAgB;IAElD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,uBAAuB;QACzB;IACF,GAAG;QAAC;KAAU;IAEd,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;YACrD,OAAO,iBAAiB,CAAC,oBAAoB;QAC/C;QACA,OAAO,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,YAAY,EAAE,oBAAoB;;0BACjD,8OAAC;gBAAM,MAAK;gBAAO,QAAO;gBAAO,UAAU;gBAAU,UAAU;gBAAW,KAAK;;;;;;0BAC/E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yJAAA,CAAA,UAAkB;;;;;oBAClB,CAAC,0BACA,8OAAC;;4BACE,EAAE;0CACH,8OAAC;;;;;4BACA,EAAE;;;;;;6CAGL,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,8OAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/DeleteDarkIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype DeleteDarkIconProps = {\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n};\n\nfunction DeleteDarkIcon({ className, onClick, disabled }: DeleteDarkIconProps) {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"33\"\n      height=\"32\"\n      viewBox=\"0 0 33 32\"\n      fill=\"none\"\n      className={className}\n      onClick={disabled ? undefined : onClick}\n    >\n      <g clip-path=\"url(#clip0_9593_10502)\">\n        <path d=\"M5.99951 9.33594H27.3328\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n        <path d=\"M13.9995 14.6641V22.6641\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n        <path d=\"M19.3335 14.6641V22.6641\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n        <path\n          d=\"M7.3335 9.33594L8.66683 25.3359C8.66683 26.0432 8.94778 26.7215 9.44788 27.2216C9.94797 27.7217 10.6263 28.0026 11.3335 28.0026H22.0002C22.7074 28.0026 23.3857 27.7217 23.8858 27.2216C24.3859 26.7215 24.6668 26.0432 24.6668 25.3359L26.0002 9.33594\"\n          stroke=\"#333333\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M12.6665 9.33333V5.33333C12.6665 4.97971 12.807 4.64057 13.057 4.39052C13.3071 4.14048 13.6462 4 13.9998 4H19.3332C19.6868 4 20.0259 4.14048 20.276 4.39052C20.526 4.64057 20.6665 4.97971 20.6665 5.33333V9.33333\"\n          stroke=\"#333333\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9593_10502\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(0.666504)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default DeleteDarkIcon;\n"], "names": [], "mappings": ";;;;;AAQA,SAAS,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAuB;IAC3E,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,WAAW;QACX,SAAS,WAAW,YAAY;;0BAEhC,8OAAC;gBAAE,aAAU;;kCACX,8OAAC;wBAAK,GAAE;wBAA2B,QAAO;wBAAU,gBAAa;wBAAI,kBAAe;wBAAQ,mBAAgB;;;;;;kCAC5G,8OAAC;wBAAK,GAAE;wBAA2B,QAAO;wBAAU,gBAAa;wBAAI,kBAAe;wBAAQ,mBAAgB;;;;;;kCAC5G,8OAAC;wBAAK,GAAE;wBAA2B,QAAO;wBAAU,gBAAa;wBAAI,kBAAe;wBAAQ,mBAAgB;;;;;;kCAC5G,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/InfoIcon.tsx"], "sourcesContent": ["import React, { useId } from \"react\";\nimport { Tooltip } from \"react-tooltip\";\n\ninterface InfoIconProps {\n  tooltip: React.ReactNode;\n  id?: string;\n  place?: \"top\" | \"bottom\" | \"left\" | \"right\";\n  className?: string;\n}\n\nfunction InfoIcon({ tooltip, id, place = \"bottom\", className }: InfoIconProps) {\n  const generatedId = useId();\n  const anchorId = id || `info-icon-${generatedId}`;\n  return (\n    <>\n      <span id={anchorId} className={className}>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 23 23\" style={{ cursor: \"pointer\" }} fill=\"none\">\n          <g clipPath=\"url(#clip0_9605_3144)\">\n            <path\n              d=\"M11.5 2.15625C9.65198 2.15625 7.84547 2.70425 6.30889 3.73096C4.77232 4.75766 3.57471 6.21695 2.8675 7.9243C2.1603 9.63165 1.97526 11.5104 2.33579 13.3229C2.69632 15.1354 3.58623 16.8003 4.89298 18.107C6.19972 19.4138 7.86462 20.3037 9.67713 20.6642C11.4896 21.0247 13.3684 20.8397 15.0757 20.1325C16.783 19.4253 18.2423 18.2277 19.269 16.6911C20.2958 15.1545 20.8438 13.348 20.8438 11.5C20.8411 9.02269 19.8559 6.64759 18.1041 4.89586C16.3524 3.14413 13.9773 2.15887 11.5 2.15625ZM11.1406 6.46875C11.3539 6.46875 11.5623 6.53198 11.7396 6.65045C11.9169 6.76891 12.0551 6.93729 12.1367 7.13429C12.2183 7.3313 12.2396 7.54807 12.198 7.75721C12.1564 7.96634 12.0538 8.15845 11.903 8.30922C11.7522 8.46 11.5601 8.56268 11.351 8.60428C11.1418 8.64588 10.925 8.62453 10.728 8.54293C10.531 8.46133 10.3627 8.32315 10.2442 8.14585C10.1257 7.96855 10.0625 7.76011 10.0625 7.54688C10.0625 7.26094 10.1761 6.98671 10.3783 6.78453C10.5805 6.58234 10.8547 6.46875 11.1406 6.46875ZM12.2188 16.5312C11.8375 16.5312 11.4719 16.3798 11.2023 16.1102C10.9327 15.8406 10.7813 15.475 10.7813 15.0938V11.5C10.5906 11.5 10.4078 11.4243 10.273 11.2895C10.1382 11.1547 10.0625 10.9719 10.0625 10.7812C10.0625 10.5906 10.1382 10.4078 10.273 10.273C10.4078 10.1382 10.5906 10.0625 10.7813 10.0625C11.1625 10.0625 11.5281 10.214 11.7977 10.4835C12.0673 10.7531 12.2188 11.1188 12.2188 11.5V15.0938C12.4094 15.0938 12.5922 15.1695 12.727 15.3043C12.8618 15.4391 12.9375 15.6219 12.9375 15.8125C12.9375 16.0031 12.8618 16.1859 12.727 16.3207C12.5922 16.4555 12.4094 16.5312 12.2188 16.5312Z\"\n              fill=\"#436EB6\"\n            />\n          </g>\n          <defs>\n            <clipPath id=\"clip0_9605_3144\">\n              <rect width=\"23\" height=\"23\" fill=\"white\" />\n            </clipPath>\n          </defs>\n        </svg>\n      </span>\n      <Tooltip anchorSelect={`#${anchorId}`} className=\"responsive-tooltip\" place={place}>\n        {tooltip}\n      </Tooltip>\n    </>\n  );\n}\n\nexport default InfoIcon;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAiB;IAC3E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACxB,MAAM,WAAW,MAAM,CAAC,UAAU,EAAE,aAAa;IACjD,qBACE;;0BACE,8OAAC;gBAAK,IAAI;gBAAU,WAAW;0BAC7B,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,OAAO;wBAAE,QAAQ;oBAAU;oBAAG,MAAK;;sCACpH,8OAAC;4BAAE,UAAS;sCACV,cAAA,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;sCAGT,8OAAC;sCACC,cAAA,8OAAC;gCAAS,IAAG;0CACX,cAAA,8OAAC;oCAAK,OAAM;oCAAK,QAAO;oCAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC,oKAAA,CAAA,UAAO;gBAAC,cAAc,CAAC,CAAC,EAAE,UAAU;gBAAE,WAAU;gBAAqB,OAAO;0BAC1E;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/PrimaryEyeIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction PrimaryEyeIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"24\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M15.9998 12.8555C14.2265 12.8555 12.7852 14.2981 12.7852 16.0715C12.7852 17.8435 14.2265 19.2848 15.9998 19.2848C17.7732 19.2848 19.2158 17.8435 19.2158 16.0715C19.2158 14.2981 17.7732 12.8555 15.9998 12.8555M15.9998 21.2848C13.1238 21.2848 10.7852 18.9461 10.7852 16.0715C10.7852 13.1955 13.1238 10.8555 15.9998 10.8555C18.8758 10.8555 21.2158 13.1955 21.2158 16.0715C21.2158 18.9461 18.8758 21.2848 15.9998 21.2848\"\n      />\n      <mask id=\"mask0_11415_1842\" style={{ maskType: \"luminance\" }} maskUnits=\"userSpaceOnUse\" x=\"2\" y=\"5\" width=\"28\" height=\"22\">\n        <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M2.66602 5.33398H29.3325V26.8073H2.66602V5.33398Z\" fill=\"white\" />\n      </mask>\n      <g mask=\"url(#mask0_11415_1842)\">\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M4.76172 16.068C7.24172 21.5467 11.4191 24.804 16.0017 24.8054C20.5844 24.804 24.7617 21.5467 27.2417 16.068C24.7617 10.5907 20.5844 7.33336 16.0017 7.33203C11.4204 7.33336 7.24172 10.5907 4.76172 16.068V16.068ZM16.0036 26.8064H15.9983H15.997C10.4823 26.8024 5.5303 22.937 2.74897 16.4637C2.64097 16.2117 2.64097 15.9264 2.74897 15.6744C5.5303 9.20236 10.4836 5.33703 15.997 5.33303C15.9996 5.3317 15.9996 5.3317 16.001 5.33303C16.0036 5.3317 16.0036 5.3317 16.005 5.33303C21.5196 5.33703 26.4716 9.20236 29.253 15.6744C29.3623 15.9264 29.3623 16.2117 29.253 16.4637C26.473 22.937 21.5196 26.8024 16.005 26.8064H16.0036Z\"\n        />\n      </g>\n    </svg>\n  );\n}\n\nexport default PrimaryEyeIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,eAAe,EAAE,SAAS,EAA0B;IAC3D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;0BAEJ,8OAAC;gBAAK,IAAG;gBAAmB,OAAO;oBAAE,UAAU;gBAAY;gBAAG,WAAU;gBAAiB,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;0BACrH,cAAA,8OAAC;oBAAK,UAAS;oBAAU,UAAS;oBAAU,GAAE;oBAAoD,MAAK;;;;;;;;;;;0BAEzG,8OAAC;gBAAE,MAAK;0BACN,cAAA,8OAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;;;;;;;;;;;;;;;;;AAKZ;uCAEe", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/UploadFileIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype UploadFileIconProps = {\n  className?: string;\n  onClick?: () => void;\n};\n\nfunction UploadFileIcon({ className, onClick }: UploadFileIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" className={className} onClick={onClick}>\n      <path\n        d=\"M27.9128 7.7745L20.413 0.274687C20.2379 0.0995625 19.999 0 19.75 0H6.625C5.07419 0 3.8125 1.26169 3.8125 2.8125V29.1875C3.8125 30.7383 5.07419 32 6.625 32H25.375C26.9258 32 28.1875 30.7383 28.1875 29.1875V8.4375C28.1875 8.18188 28.0802 7.94181 27.9128 7.7745ZM20.6875 3.20081L24.9867 7.5H21.625C21.1081 7.5 20.6875 7.07944 20.6875 6.5625V3.20081ZM25.375 30.125H6.625C6.10806 30.125 5.6875 29.7044 5.6875 29.1875V2.8125C5.6875 2.29556 6.10806 1.875 6.625 1.875H18.8125V6.5625C18.8125 8.11331 20.0742 9.375 21.625 9.375H26.3125V29.1875C26.3125 29.7044 25.8919 30.125 25.375 30.125Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M21.625 13.25H10.375C9.85725 13.25 9.4375 13.6697 9.4375 14.1875C9.4375 14.7053 9.85725 15.125 10.375 15.125H21.625C22.1427 15.125 22.5625 14.7053 22.5625 14.1875C22.5625 13.6697 22.1427 13.25 21.625 13.25Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M21.625 17H10.375C9.85725 17 9.4375 17.4197 9.4375 17.9375C9.4375 18.4553 9.85725 18.875 10.375 18.875H21.625C22.1427 18.875 22.5625 18.4553 22.5625 17.9375C22.5625 17.4197 22.1427 17 21.625 17Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M21.625 20.75H10.375C9.85725 20.75 9.4375 21.1697 9.4375 21.6875C9.4375 22.2053 9.85725 22.625 10.375 22.625H21.625C22.1427 22.625 22.5625 22.2053 22.5625 21.6875C22.5625 21.1697 22.1427 20.75 21.625 20.75Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M17.875 24.5H10.375C9.85725 24.5 9.4375 24.9197 9.4375 25.4375C9.4375 25.9553 9.85725 26.375 10.375 26.375H17.875C18.3927 26.375 18.8125 25.9553 18.8125 25.4375C18.8125 24.9197 18.3927 24.5 17.875 24.5Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default UploadFileIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,eAAe,EAAE,SAAS,EAAE,OAAO,EAAuB;IACjE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;QAAW,SAAS;;0BAC5H,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\r\nimport * as http from \"@/utils/http\";\r\n\r\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\r\nimport {\r\n  AdditionalInfoPayload,\r\n  CandidateApplication,\r\n  CandidateProfileResponse,\r\n  FetchCandidatesParams,\r\n  ICandidateInterviewHistory,\r\n  IFinalAssessment,\r\n  ISkillSpecificAssessment,\r\n  PromoteDemotePayload,\r\n  topCandidateApplication,\r\n} from \"@/interfaces/candidatesInterface\";\r\n\r\n// ============================================================================\r\n// CANDIDATE APPLICATION MANAGEMENT\r\n// ============================================================================\r\n\r\n/**\r\n * Fetches candidates with their job applications using advanced filtering and pagination\r\n *\r\n * This function retrieves a paginated list of candidates along with their application details.\r\n * It supports comprehensive filtering options including job-specific filtering, name search,\r\n * and active/archived status filtering.\r\n *\r\n * @async\r\n * @function fetchCandidatesApplications\r\n * @param {FetchCandidatesParams} data - Query parameters for filtering and pagination\r\n * @param {number} [data.page] - Page offset for pagination (0-based)\r\n * @param {number} [data.limit] - Maximum number of candidates to return per page\r\n * @param {string} [data.searchStr] - Search string to filter candidates by name\r\n * @param {boolean} [data.isActive] - Filter by status: true for active, false for archived\r\n * @param {number} [data.jobId] - Optional job ID to filter candidates for specific position\r\n * @returns {Promise<ApiResponse<CandidateApplication[]>>} Promise resolving to candidate applications list\r\n *\r\n */\r\nexport const fetchCandidatesApplications = (data: FetchCandidatesParams): Promise<ApiResponse<CandidateApplication[]>> => {\r\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });\r\n};\r\n\r\n/**\r\n * Fetches top-ranked candidates with their applications for a specific job\r\n *\r\n * This function retrieves candidates who have been identified as top performers\r\n * based on AI scoring, ATS evaluation, and other ranking criteria. The results\r\n * are pre-filtered and sorted by the backend ranking algorithm.\r\n *\r\n * @async\r\n * @function fetchTopCandidatesApplications\r\n * @param {number} [jobId] - Optional job ID to filter top candidates for specific position\r\n * @returns {Promise<ApiResponse<topCandidateApplication[]>>} Promise resolving to top candidates list\r\n */\r\nexport const fetchTopCandidatesApplications = (jobId?: number): Promise<ApiResponse<topCandidateApplication[]>> => {\r\n  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {\r\n    jobId,\r\n  });\r\n};\r\n\r\n/**\r\n * Promotes or demotes a candidate in the application ranking\r\n *\r\n * This function allows hiring managers to manually adjust candidate rankings\r\n * by promoting high-potential candidates or demoting those who don't meet\r\n * expectations. The action affects the candidate's visibility and priority.\r\n *\r\n * @async\r\n * @function promoteDemoteCandidate\r\n * @param {PromoteDemotePayload} payload - The promotion/demotion request data\r\n * @param {number} payload.candidateId - ID of the candidate to promote/demote\r\n * @param {number} payload.applicationId - ID of the specific application\r\n * @param {\"Promoted\" | \"Demoted\"} payload.action - Action to perform\r\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\r\n */\r\nexport const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<ApiResponse<null>> => {\r\n  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);\r\n};\r\n\r\n/**\r\n * Adds additional information to a candidate's application\r\n *\r\n * This function allows candidates or hiring managers to submit supplementary\r\n * information, documents, or clarifications to an existing application.\r\n * Commonly used for portfolio submissions, additional certifications, or\r\n * responses to specific questions.\r\n *\r\n * @async\r\n * @function addApplicantAdditionalInfo\r\n * @param {AdditionalInfoPayload} payload - The additional information data\r\n * @param {string} payload.applicationId - ID of the application to update\r\n * @param {string} payload.description - Description or text content\r\n * @param {string} payload.images - Image URLs or file references\r\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\r\n */\r\nexport const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<ApiResponse<null>> => {\r\n  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);\r\n};\r\n\r\n// ============================================================================\r\n// CANDIDATE PROFILE MANAGEMENT\r\n// ============================================================================\r\n\r\n/**\r\n * Fetches comprehensive candidate profile details by candidate ID\r\n *\r\n * This function retrieves detailed information about a specific candidate including\r\n * personal details, job application status, assigned interviewer information,\r\n * resume links, and current round information. Essential for candidate profile views.\r\n *\r\n * @async\r\n * @function fetchCandidateProfile\r\n * @param {number | string} candidateId - The unique identifier of the candidate\r\n * @returns {Promise<ApiResponse<CandidateProfileResponse>>} Promise resolving to candidate profile data\r\n */\r\nexport const fetchCandidateProfile = (jobApplicationId: number | string): Promise<ApiResponse<CandidateProfileResponse>> => {\r\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { jobApplicationId });\r\n};\r\n\r\n/**\r\n * Updates the job application status for hire/reject decisions\r\n *\r\n * This function allows hiring managers to make final decisions on candidate applications\r\n * by updating the status to either \"Hired\" or \"Final-Reject\". This action typically\r\n * triggers workflow notifications and updates the candidate's status across the system.\r\n *\r\n * @async\r\n * @function updateJobApplicationStatus\r\n * @param {number} jobApplicationId - The unique identifier of the job application\r\n * @param {string} status - The new status (\"Hired\" or \"Final-Reject\")\r\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\r\n * ```\r\n */\r\nexport const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<ApiResponse<null>> => {\r\n  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(\":jobApplicationId\", jobApplicationId.toString()), {\r\n    status,\r\n  });\r\n};\r\n\r\n/**\r\n * Retrieves comprehensive interview history for a specific candidate\r\n *\r\n * This function fetches detailed interview records across all rounds including\r\n * interviewer information, skill scores, hard skill marks, interview summaries,\r\n * and AI-powered performance analysis. Essential for tracking candidate progress.\r\n *\r\n * @async\r\n * @function getCandidateInterviewHistory\r\n * @param {number} candidateId - The unique identifier of the candidate\r\n * @returns {Promise<ApiResponse<ICandidateInterviewHistory[]>>} Promise resolving to interview history array\r\n */\r\nexport const getCandidateInterviewHistory = async (applicationId: string): Promise<ApiResponse<ICandidateInterviewHistory[]>> => {\r\n  return await http.get(endpoint.candidatesApplication.GET_CANDIDATE_INTERVIEW_HISTORY.replace(\":applicationId\", applicationId));\r\n};\r\n\r\n/**\r\n * Retrieves comprehensive final assessment summary for a specific candidate application\r\n *\r\n * This function fetches the complete final assessment analysis generated after all\r\n * interview rounds are completed. It includes AI-powered insights, success probability\r\n * calculations, skill summaries, and personalized development recommendations.\r\n *\r\n * @async\r\n * @function getApplicationFinalSummary\r\n * @param {string} candidateId - The unique identifier of the candidate\r\n * @returns {Promise<ApiResponse<IFinalAssessment>>} Promise resolving to final assessment data\r\n *\r\n * Assessment Data Includes:\r\n * - Overall success probability percentage (0-100)\r\n * - Comprehensive skill summary with AI analysis\r\n * - Personalized development recommendations by category\r\n * - Job application reference details\r\n * ```\r\n */\r\nexport const getApplicationFinalSummary = async (jobApplicationId: string): Promise<ApiResponse<IFinalAssessment>> => {\r\n  return await http.get(endpoint.candidatesApplication.GET_APPLICATION_FINAL_SUMMARY.replace(\":jobApplicationId\", jobApplicationId));\r\n};\r\n\r\n/**\r\n * Retrieves detailed skill-specific assessment data for a candidate\r\n *\r\n * This function fetches comprehensive skill evaluation data aggregated from all\r\n * completed interview rounds. The data is flattened and optimized for frontend\r\n * consumption, providing detailed insights into each skill area evaluated.\r\n *\r\n * @async\r\n * @function getApplicationSkillScoreData\r\n * @param {string} candidateId - The unique identifier of the candidate\r\n * @returns {Promise<ApiResponse<ISkillSpecificAssessment>>} Promise resolving to skill assessment data\r\n *\r\n * Skill Data Includes:\r\n * - Individual skill scores and marks (0-10 scale)\r\n * - Skill-specific strengths and achievements\r\n * - Identified potential gaps and improvement areas\r\n * - Success probability for each skill area\r\n * - Overall career-based skills score\r\n * - Interviewer-specific evaluations and feedback\r\n */\r\nexport const getApplicationSkillScoreData = async (jobApplicationId: string): Promise<ApiResponse<ISkillSpecificAssessment>> => {\r\n  return await http.get(endpoint.candidatesApplication.GET_APPLICATION_SKILL_SCORE_DATA.replace(\":jobApplicationId\", jobApplicationId));\r\n};\r\n\r\n/**\r\n * Generates final summary for a candidate application\r\n *\r\n * This function triggers the generation of a comprehensive final summary for a candidate\r\n * based on their interview performance, skill assessments, and overall evaluation data.\r\n * The summary includes AI-powered insights and recommendations for hiring decisions.\r\n *\r\n * @async\r\n * @function generateFinalSummary\r\n * @param {number | string} candidateId - The unique identifier of the candidate\r\n * @param {number | string} jobApplicationId - The unique identifier of the job application\r\n * @returns {Promise<ApiResponse<null>>} Promise resolving to success confirmation\r\n *\r\n * Summary Generation Includes:\r\n * - Comprehensive analysis of interview performance across all rounds\r\n * - Skill-specific evaluation and scoring aggregation\r\n * - AI-powered insights and recommendations\r\n * - Overall success probability calculation\r\n * - Personalized development recommendations\r\n */\r\nexport const generateFinalSummary = async (jobApplicationId: number | string): Promise<ApiResponse<null>> => {\r\n  return await http.get(endpoint.candidatesApplication.GENERATE_FINAL_SUMMARY, {\r\n    jobApplicationId: jobApplicationId.toString(),\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAqCO,MAAM,8BAA8B,CAAC;IAC1C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,EAAE;QAAE,GAAG,IAAI;IAAC;AAC7F;AAcO,MAAM,iCAAiC,CAAC;IAC7C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,oCAAoC,EAAE;QACnF;IACF;AACF;AAiBO,MAAM,yBAAyB,OAAO;IAC3C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;AACjF;AAkBO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,eAAe,EAAE;AACzE;AAkBO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,qBAAqB,EAAE;QAAE;IAAiB;AAC3F;AAgBO,MAAM,6BAA6B,OAAO,kBAA0B;IACzE,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK;QAC5I;IACF;AACF;AAcO,MAAM,+BAA+B,OAAO;IACjD,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,OAAO,CAAC,kBAAkB;AACjH;AAqBO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB;AAClH;AAsBO,MAAM,+BAA+B,OAAO;IACjD,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,CAAC,OAAO,CAAC,qBAAqB;AACrH;AAsBO,MAAM,uBAAuB,OAAO;IACzC,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,sBAAsB,EAAE;QAC3E,kBAAkB,iBAAiB,QAAQ;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/validations/additionalInfoVAlidation.ts"], "sourcesContent": ["import * as yup from \"yup\";\n\nexport const additionalSubmissionValidation = (translation: (key: string) => string) =>\n  yup.object().shape({\n    additionalInfo: yup\n      .string()\n      .required(translation(\"additional_info_required\"))\n      .min(10, translation(\"additional_info_min_10_chars\"))\n      .max(200, translation(\"additional_info_max_200_chars\")),\n  });\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,iCAAiC,CAAC,cAC7C,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SACP,AAAD,IACL,QAAQ,CAAC,YAAY,6BACrB,GAAG,CAAC,IAAI,YAAY,iCACpB,GAAG,CAAC,KAAK,YAAY;IAC1B", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/AdditionalSubmission.tsx"], "sourcesContent": ["\"use client\";\nimport React, { use, useRef, useState } from \"react\";\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { useRouter } from \"next/navigation\";\nimport { useTranslations } from \"next-intl\";\nimport { useForm } from \"react-hook-form\";\n\nimport UploadBox from \"@/components/commonComponent/UploadBox\";\nimport Button from \"@/components/formElements/Button\";\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\nimport Textarea from \"@/components/formElements/Textarea\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\nimport DeleteDarkIcon from \"@/components/svgComponents/DeleteDarkIcon\";\nimport InfoIcon from \"@/components/svgComponents/InfoIcon\";\nimport PrimaryEyeIcon from \"@/components/svgComponents/PrimaryEyeIcon\";\nimport UploadFileIcon from \"@/components/svgComponents/UploadFileIcon\";\nimport { PDF_ADDITIONAL_SUBMISSION_LIMIT, PDF_FILE_TYPE } from \"@/constants/commonConstants\";\nimport ROUTES from \"@/constants/routes\";\nimport { addApplicantAdditionalInfo } from \"@/services/CandidatesServices/candidatesApplicationServices\";\nimport { toastMessageError, toastMessageSuccess, uploadFileOnS3 } from \"@/utils/helper\";\nimport { additionalSubmissionValidation } from \"@/validations/additionalInfoVAlidation\";\n\nimport style from \"../../../styles/commonPage.module.scss\";\n\ninterface FormValues {\n  additionalInfo: string;\n}\n\nconst AdditionalSubmission = ({ searchParams }: { searchParams: Promise<{ applicationId: string }> }) => {\n  const t = useTranslations();\n  const [isLoading, setIsLoading] = useState(false);\n  const searchParamsPromise = use(searchParams);\n  const [docInfo, setDocInfo] = useState<File | null>(null);\n  const currentFileArrayLengthRef = useRef(0);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const lastValidFileRef = useRef<File | null>(null);\n  const router = useRouter();\n  const {\n    control,\n    handleSubmit,\n    reset,\n    formState: { errors },\n  } = useForm<FormValues>({\n    resolver: yupResolver(additionalSubmissionValidation(t)),\n    defaultValues: {\n      additionalInfo: \"\",\n    },\n  });\n  const handleClick = () => {\n    const id = searchParamsPromise.applicationId;\n\n    if (!id) return;\n    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${searchParamsPromise.applicationId}`);\n  };\n\n  const onSubmit = async (data: FormValues) => {\n    console.log(\">>>>>>>>>>>>>>>>applicationId\", searchParamsPromise.applicationId);\n\n    try {\n      setIsLoading(true);\n\n      let uploadedFileUrl: string | null = null;\n\n      // If a file is uploaded, upload it to S3\n      if (docInfo) {\n        const fileNameArr = docInfo.name.split(\".\") || [];\n        const filePath = `candidate-additional-info/${searchParamsPromise.applicationId}}/${fileNameArr[0]}-${Date.now()}.${fileNameArr[1]}`;\n        uploadedFileUrl = (await uploadFileOnS3(docInfo, filePath)) as string;\n      }\n\n      const payload = {\n        applicationId: searchParamsPromise.applicationId,\n        description: data.additionalInfo,\n        images: uploadedFileUrl,\n      };\n\n      const response = await addApplicantAdditionalInfo(payload);\n      if (response?.data?.success) {\n        toastMessageSuccess(t(\"additional_info_submitted_successfully\"));\n\n        // Reset file-related fields only if file was uploaded\n        if (docInfo) {\n          setDocInfo(null);\n          if (inputRef.current) {\n            inputRef.current.value = \"\";\n          }\n          currentFileArrayLengthRef.current = 0;\n        }\n\n        // Reset form fields\n        reset({ additionalInfo: \"\" });\n      } else {\n        toastMessageError(t(response?.data?.message));\n      }\n    } catch {\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const fileClear = () => {\n    setDocInfo(null);\n    lastValidFileRef.current = null; // Clear the reference\n    if (inputRef.current) {\n      inputRef.current.value = \"\";\n    }\n    currentFileArrayLengthRef.current = 0;\n  };\n\n  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    setIsLoading(true);\n    const files = e.target.files?.[0] || null;\n    console.log(\"files===========>========\", files);\n\n    // If no file is selected, it could be due to:\n    // 1. User clicked Cancel in file dialog - preserve existing file\n    // 2. File input was programmatically cleared - should clear state\n    if (!files) {\n      // Check if we have a previously valid file stored\n      if (lastValidFileRef.current && docInfo) {\n        // User clicked Cancel - restore the previous file state\n        console.log(\"User clicked Cancel, preserving previous file:\", lastValidFileRef.current.name);\n        setIsLoading(false);\n        return null;\n      } else {\n        // No previous file or input was programmatically cleared\n        fileClear();\n        setIsLoading(false);\n        return null;\n      }\n    }\n\n    // Validate file extension\n    if (!files.name.includes(\".pdf\")) {\n      toastMessageError(t(\"unsupported_file_type\"));\n      // Reset input to preserve previous file selection\n      if (inputRef.current && docInfo) {\n        // Don't clear the input if there was a previous file\n        inputRef.current.value = \"\";\n      } else {\n        fileClear();\n      }\n      setIsLoading(false);\n      return null;\n    }\n\n    // Validate file MIME type\n    if (!PDF_FILE_TYPE.includes(files.type)) {\n      toastMessageError(t(\"pdf_only\"));\n      // Reset input to preserve previous file selection\n      if (inputRef.current && docInfo) {\n        inputRef.current.value = \"\";\n      } else {\n        fileClear();\n      }\n      setIsLoading(false);\n      return null;\n    }\n\n    // Validate file size (max 10MB)\n    if (files.size > PDF_ADDITIONAL_SUBMISSION_LIMIT) {\n      toastMessageError(t(\"invalid_size_format\"));\n      // Reset input to preserve previous file selection\n      if (inputRef.current && docInfo) {\n        inputRef.current.value = \"\";\n      } else {\n        fileClear();\n      }\n      setIsLoading(false);\n      return null;\n    }\n\n    // Validate file name length\n    if (files.name.length > 50) {\n      toastMessageError(t(\"pdf_name\"));\n      // Reset input to preserve previous file selection\n      if (inputRef.current && docInfo) {\n        inputRef.current.value = \"\";\n      } else {\n        fileClear();\n      }\n      setIsLoading(false);\n      return null;\n    }\n\n    // File is valid, update state\n    setDocInfo(files);\n    lastValidFileRef.current = files; // Store reference to valid file\n    currentFileArrayLengthRef.current = 1;\n    console.log(\"Valid file selected:\", files.name);\n    setIsLoading(false);\n  };\n\n  const clearDocument = () => {\n    // Clear the file state first\n    setDocInfo(null);\n    lastValidFileRef.current = null;\n    // Then clear the input - this ensures the input value is empty\n    // which helps distinguish between user cancellation and programmatic clearing\n    fileClear();\n  };\n\n  const resetForm = () => {\n    // Reset form fields\n    reset({ additionalInfo: \"\" });\n    // Clear file state and input\n    setDocInfo(null);\n    fileClear();\n  };\n\n  return (\n    <div className={`${style.resume_page} ${style.manual_upload_resume}`}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                <BackArrowIcon onClick={() => router.back()} />\n                {t(\"additional_info_submission\")} <span>{t(\"before_hiring\")}</span>\n              </h2>\n              <Button onClick={handleClick} className=\"clear-btn text-btn primary p-0 m-0\" disabled={isLoading}>\n                <PrimaryEyeIcon className=\"me-2\" />\n                {t(\"view_candidate_info\")}\n              </Button>\n            </div>\n            <p className=\"description\">{t(\"additional_info_description\")}</p>\n          </div>\n        </div>\n        <div className={style.inner_page}>\n          <form onSubmit={handleSubmit(onSubmit)}>\n            <InputWrapper>\n              <InputWrapper.Label htmlFor=\"additionalInfo\">\n                {t(\"additional_info\")}\n                <sup>*</sup> <InfoIcon tooltip={t(\"additional_info_placeholder\")} id=\"AdditionalInfo\" place=\"right\" />\n              </InputWrapper.Label>\n              <Textarea rows={4} name=\"additionalInfo\" control={control} placeholder={t(\"additional_info_placeholder\")} className=\"form-control\" />\n              {errors.additionalInfo?.message && <InputWrapper.Error message={errors.additionalInfo.message as string} />}\n            </InputWrapper>\n            <InputWrapper>\n              <InputWrapper.Label htmlFor=\"resume\">{t(\"upload_doc\")}</InputWrapper.Label>\n              <UploadBox UploadBoxClassName=\"upload-card-sm\" onChange={onFileChange} inputRef={inputRef} isLoading={isLoading} />\n            </InputWrapper>\n            {docInfo && (\n              <div className=\"uploded-item\">\n                <div className=\"item-name\">\n                  <UploadFileIcon />\n                  <p>{docInfo?.name}</p>\n                </div>\n                <DeleteDarkIcon onClick={clearDocument} className=\"delete-item\" disabled={isLoading} />\n              </div>\n            )}\n            <div className=\"button-align py-5\">\n              <Button type=\"submit\" className=\"primary-btn rounded-md\" disabled={isLoading}>\n                {isLoading ? t(\"Submitting\") + \"...\" : t(\"submit\")}\n              </Button>\n              <Button type=\"button\" className=\"dark-outline-btn rounded-md\" disabled={isLoading} onClick={resetForm}>\n                {t(\"reset\")}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdditionalSubmission;\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAvBA;;;;;;;;;;;;;;;;;;;;;;AA6BA,MAAM,uBAAuB,CAAC,EAAE,YAAY,EAAwD;IAClG,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACpD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAc;QACtB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,iCAA8B,AAAD,EAAE;QACrD,eAAe;YACb,gBAAgB;QAClB;IACF;IACA,MAAM,cAAc;QAClB,MAAM,KAAK,oBAAoB,aAAa;QAE5C,IAAI,CAAC,IAAI;QACT,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,oBAAoB,aAAa,EAAE;IACrF;IAEA,MAAM,WAAW,OAAO;QACtB,QAAQ,GAAG,CAAC,iCAAiC,oBAAoB,aAAa;QAE9E,IAAI;YACF,aAAa;YAEb,IAAI,kBAAiC;YAErC,yCAAyC;YACzC,IAAI,SAAS;gBACX,MAAM,cAAc,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACjD,MAAM,WAAW,CAAC,0BAA0B,EAAE,oBAAoB,aAAa,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,EAAE;gBACpI,kBAAmB,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;YACnD;YAEA,MAAM,UAAU;gBACd,eAAe,oBAAoB,aAAa;gBAChD,aAAa,KAAK,cAAc;gBAChC,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE;YAClD,IAAI,UAAU,MAAM,SAAS;gBAC3B,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE;gBAEtB,sDAAsD;gBACtD,IAAI,SAAS;oBACX,WAAW;oBACX,IAAI,SAAS,OAAO,EAAE;wBACpB,SAAS,OAAO,CAAC,KAAK,GAAG;oBAC3B;oBACA,0BAA0B,OAAO,GAAG;gBACtC;gBAEA,oBAAoB;gBACpB,MAAM;oBAAE,gBAAgB;gBAAG;YAC7B,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAM;YACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,YAAY;QAChB,WAAW;QACX,iBAAiB,OAAO,GAAG,MAAM,sBAAsB;QACvD,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK,GAAG;QAC3B;QACA,0BAA0B,OAAO,GAAG;IACtC;IAEA,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QACrC,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,8CAA8C;QAC9C,iEAAiE;QACjE,kEAAkE;QAClE,IAAI,CAAC,OAAO;YACV,kDAAkD;YAClD,IAAI,iBAAiB,OAAO,IAAI,SAAS;gBACvC,wDAAwD;gBACxD,QAAQ,GAAG,CAAC,kDAAkD,iBAAiB,OAAO,CAAC,IAAI;gBAC3F,aAAa;gBACb,OAAO;YACT,OAAO;gBACL,yDAAyD;gBACzD;gBACA,aAAa;gBACb,OAAO;YACT;QACF;QAEA,0BAA0B;QAC1B,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS;YAChC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,kDAAkD;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS;gBAC/B,qDAAqD;gBACrD,SAAS,OAAO,CAAC,KAAK,GAAG;YAC3B,OAAO;gBACL;YACF;YACA,aAAa;YACb,OAAO;QACT;QAEA,0BAA0B;QAC1B,IAAI,CAAC,mIAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG;YACvC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,kDAAkD;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS;gBAC/B,SAAS,OAAO,CAAC,KAAK,GAAG;YAC3B,OAAO;gBACL;YACF;YACA,aAAa;YACb,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,MAAM,IAAI,GAAG,mIAAA,CAAA,kCAA+B,EAAE;YAChD,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,kDAAkD;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS;gBAC/B,SAAS,OAAO,CAAC,KAAK,GAAG;YAC3B,OAAO;gBACL;YACF;YACA,aAAa;YACb,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI;YAC1B,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,kDAAkD;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS;gBAC/B,SAAS,OAAO,CAAC,KAAK,GAAG;YAC3B,OAAO;gBACL;YACF;YACA,aAAa;YACb,OAAO;QACT;QAEA,8BAA8B;QAC9B,WAAW;QACX,iBAAiB,OAAO,GAAG,OAAO,gCAAgC;QAClE,0BAA0B,OAAO,GAAG;QACpC,QAAQ,GAAG,CAAC,wBAAwB,MAAM,IAAI;QAC9C,aAAa;IACf;IAEA,MAAM,gBAAgB;QACpB,6BAA6B;QAC7B,WAAW;QACX,iBAAiB,OAAO,GAAG;QAC3B,+DAA+D;QAC/D,8EAA8E;QAC9E;IACF;IAEA,MAAM,YAAY;QAChB,oBAAoB;QACpB,MAAM;YAAE,gBAAgB;QAAG;QAC3B,6BAA6B;QAC7B,WAAW;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,yJAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,yJAAA,CAAA,UAAK,CAAC,oBAAoB,EAAE;kBAClE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,oJAAA,CAAA,UAAa;gDAAC,SAAS,IAAM,OAAO,IAAI;;;;;;4CACxC,EAAE;4CAA8B;0DAAC,8OAAC;0DAAM,EAAE;;;;;;;;;;;;kDAE7C,8OAAC,4IAAA,CAAA,UAAM;wCAAC,SAAS;wCAAa,WAAU;wCAAqC,UAAU;;0DACrF,8OAAC,qJAAA,CAAA,UAAc;gDAAC,WAAU;;;;;;4CACzB,EAAE;;;;;;;;;;;;;0CAGP,8OAAC;gCAAE,WAAU;0CAAe,EAAE;;;;;;;;;;;;;;;;;8BAGlC,8OAAC;oBAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,UAAU;8BAC9B,cAAA,8OAAC;wBAAK,UAAU,aAAa;;0CAC3B,8OAAC,kJAAA,CAAA,UAAY;;kDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wCAAC,SAAQ;;4CACzB,EAAE;0DACH,8OAAC;0DAAI;;;;;;4CAAO;0DAAC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAS,EAAE;gDAAgC,IAAG;gDAAiB,OAAM;;;;;;;;;;;;kDAE9F,8OAAC,8IAAA,CAAA,UAAQ;wCAAC,MAAM;wCAAG,MAAK;wCAAiB,SAAS;wCAAS,aAAa,EAAE;wCAAgC,WAAU;;;;;;oCACnH,OAAO,cAAc,EAAE,yBAAW,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wCAAC,SAAS,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;0CAE/F,8OAAC,kJAAA,CAAA,UAAY;;kDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wCAAC,SAAQ;kDAAU,EAAE;;;;;;kDACxC,8OAAC,kJAAA,CAAA,UAAS;wCAAC,oBAAmB;wCAAiB,UAAU;wCAAc,UAAU;wCAAU,WAAW;;;;;;;;;;;;4BAEvG,yBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,qJAAA,CAAA,UAAc;;;;;0DACf,8OAAC;0DAAG,SAAS;;;;;;;;;;;;kDAEf,8OAAC,qJAAA,CAAA,UAAc;wCAAC,SAAS;wCAAe,WAAU;wCAAc,UAAU;;;;;;;;;;;;0CAG9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAyB,UAAU;kDAChE,YAAY,EAAE,gBAAgB,QAAQ,EAAE;;;;;;kDAE3C,8OAAC,4IAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,WAAU;wCAA8B,UAAU;wCAAW,SAAS;kDACzF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCAEe", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/additional-submission/%5BapplicationId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport AdditionalSubmission from \"@/components/views/conductInterview/AdditionalSubmission\";\nimport React from \"react\";\n\nconst page = ({ params }: { params: Promise<{ applicationId: string }> }) => {\n  return (\n    <div>\n      <AdditionalSubmission searchParams={params} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAkD;IACtE,qBACE,8OAAC;kBACC,cAAA,8OAAC,uKAAA,CAAA,UAAoB;YAAC,cAAc;;;;;;;;;;;AAG1C;uCAEe", "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}