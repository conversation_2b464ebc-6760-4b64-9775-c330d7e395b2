{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c13982b5._.js", "server/edge/chunks/[root of the server]__9de05221._.js", "server/edge/chunks/edge-wrapper_27c01396.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Cw15iGTz+4LQllr6P5QNxInPq5+a4lqlZYHYOfialKg=", "__NEXT_PREVIEW_MODE_ID": "1380868be1f1561aeefa6fdbfa07f77f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1a710415f1163f10c533d7b873c44f20747ee10682504f4980205380cc7a2aa9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "01bd9b705b200ec0666e84d0fe964348bf75a7924f615faa2db281f74664634b"}}}, "sortedMiddleware": ["/"], "functions": {}}