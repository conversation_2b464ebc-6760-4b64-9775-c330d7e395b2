{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard__stats_header\": \"commonPage-module-scss-module__em0r7a__dashboard__stats_header\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/LinkedinIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype LinkedinIconProps = {\n  className?: string;\n};\n\nfunction LinkedinIcon({ className }: LinkedinIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"85\" height=\"24\" viewBox=\"0 0 85 24\" fill=\"none\" className={className}>\n      <g clipPath=\"url(#clip0_9859_6933)\">\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M3.18711 4.8521H0.000976562V18.98H8.80458V16.297H3.18711V4.8521ZM11.5497 4.28654C10.5227 4.28654 9.85178 4.97815 9.85178 5.87952C9.85178 6.75964 10.5016 7.47232 11.5078 7.47232C12.5766 7.47232 13.2264 6.75946 13.2264 5.87952C13.2057 4.97815 12.5766 4.28654 11.5497 4.28654ZM9.93569 18.98H13.122V8.73021H9.93569V18.98ZM21.0862 8.49955C19.3675 8.49955 18.3405 9.48465 17.9213 10.1556H17.8583L17.7113 8.73021H14.9445C14.9865 9.65265 15.0283 10.7215 15.0283 12.0001V18.9802H18.2146V13.0693C18.2146 12.7756 18.2355 12.4823 18.3192 12.2725C18.5499 11.6859 19.074 11.0778 19.9545 11.0778C21.1072 11.0778 21.5684 11.9792 21.5684 13.2996V18.98H24.7547V12.9223C24.7545 9.90401 23.1824 8.49955 21.0862 8.49955ZM36.5337 8.73021H32.698L30.6228 11.8114C30.3712 12.1888 30.1199 12.6079 29.8892 13.048H29.8473V4.09766H26.6611V18.9799H29.8474V15.7726L30.6438 14.7667L33.1384 18.9799H37.0581L32.866 12.8802L36.5337 8.73021ZM41.6688 8.49955C38.1058 8.49955 36.4916 11.3713 36.4916 13.9703C36.4916 17.1775 38.4831 19.1894 41.9627 19.1894C43.3461 19.1894 44.6245 18.9799 45.6726 18.5396L45.2538 16.3807C44.394 16.6742 43.5137 16.821 42.4238 16.821C40.9355 16.821 39.6149 16.1918 39.5312 14.8715H46.1337C46.1757 14.6408 46.2385 14.1586 46.2385 13.6139C46.2383 11.0778 44.981 8.49955 41.6688 8.49955ZM39.5099 12.6917C39.5936 11.8535 40.1389 10.6165 41.4803 10.6165C42.9476 10.6165 43.2827 11.9162 43.2827 12.6917H39.5099ZM57.431 15.9198V4.09784H54.2446V9.63158H54.2027C53.7414 8.9398 52.7773 8.47867 51.4987 8.47867C49.0463 8.47867 46.8874 10.4908 46.9083 13.9494C46.9083 17.1357 48.8578 19.2109 51.2892 19.2109C52.6095 19.2109 53.8672 18.6237 54.4964 17.4917H54.559L54.6847 18.98H57.5147C57.4727 18.2883 57.431 17.0725 57.431 15.9198ZM54.2446 14.4107C54.2446 14.6621 54.2239 14.9136 54.1818 15.1443C53.9931 16.0454 53.2387 16.6742 52.3162 16.6742C50.9958 16.6742 50.1361 15.5841 50.1361 13.8448C50.1361 12.2305 50.8698 10.9312 52.3375 10.9312C53.3227 10.9312 54.0142 11.6441 54.2029 12.4823C54.2448 12.6708 54.2448 12.9013 54.2448 13.0693V14.4107H54.2446Z\"\n          fill=\"#181818\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M81.8876 0.0175781H62.9813C61.6624 0.0175781 60.5938 1.09007 60.5938 2.413V21.5839C60.5938 22.907 61.6624 23.9793 62.9813 23.9793H81.8874C83.2063 23.9793 84.275 22.9068 84.275 21.5839V2.41318C84.2752 1.09025 83.2065 0.0175781 81.8876 0.0175781ZM68.1926 18.9772H65.0065V8.72738H68.1926V18.9772ZM66.5785 7.46949C65.5726 7.46949 64.9227 6.75663 64.9227 5.87669C64.9227 4.97532 65.5933 4.28371 66.6205 4.28371C67.6474 4.28371 68.2765 4.97532 68.2972 5.87669C68.2972 6.75682 67.6474 7.46949 66.5785 7.46949ZM79.8253 18.9772H76.6393V13.2967C76.6393 11.9764 76.1778 11.075 75.0249 11.075C74.1446 11.075 73.6208 11.6829 73.3901 12.2697C73.3064 12.4794 73.2852 12.7728 73.2852 13.0664V18.9774H70.0992V11.9972C70.0992 10.7186 70.0573 9.64982 70.0155 8.72738H72.7823L72.9292 10.1527H72.992C73.4112 9.48182 74.4384 8.49672 76.1571 8.49672C78.2532 8.49672 79.8251 9.90118 79.8251 12.9195V18.9772H79.8253Z\"\n          fill=\"#006699\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9859_6933\">\n          <rect width=\"84.2748\" height=\"24\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default LinkedinIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,aAAa,EAAE,SAAS,EAAqB;IACpD,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,6LAAC;gBAAE,UAAS;;kCACV,6LAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,6LAAC;0BACC,cAAA,6LAAC;oBAAS,IAAG;8BACX,cAAA,6LAAC;wBAAK,OAAM;wBAAU,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAKjD;KAxBS;uCA0BM", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ApplicationsSourcesModal.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport Button from \"../formElements/Button\";\nimport LinkedinIcon from \"../svgComponents/LinkedinIcon\";\n\ninterface IProps {\n  onCancel?: () => void;\n}\n\nconst ApplicationsSourcesModal: React.FC<IProps> = ({ onCancel }) => {\n  return (\n    <div className=\"modal theme-modal show-modal applications-sources-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center\">\n            <h2 className=\"text-left\">Applications Sources</h2>\n          </div>\n          <div className=\"modal-body\">\n            <div className=\"applications-list\">\n              <div className=\"item\">\n                <div className=\"left-item\">\n                  <LinkedinIcon />\n                </div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n              <div className=\"item\">\n                <div className=\"left-item\">\n                  <LinkedinIcon />\n                </div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n              <div className=\"item\">\n                <div className=\"left-item\">\n                  <LinkedinIcon />\n                </div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n              <div className=\"item\">\n                <div className=\"left-item\">Other</div>\n                <div className=\"item-right\">42 Applicants</div>\n              </div>\n            </div>\n\n            <div className=\"action-btn justify-content-center\">\n              <Button className=\"primary-btn rounded-md w-100\" onClick={onCancel}>\n                Ok\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\nexport default ApplicationsSourcesModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,MAAM,2BAA6C,CAAC,EAAE,QAAQ,EAAE;IAC9D,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAY;;;;;;;;;;;kCAE5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+IAAA,CAAA,UAAM;oCAAC,WAAU;oCAA+B,SAAS;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlF;KA5CM;uCA6CS", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/candidates/Candidates.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\n\n// External libraries\nimport Link from \"next/link\";\nimport Image from \"next/image\";\n\n// CSS\nimport style from \"@/styles/commonPage.module.scss\";\nimport user from \"../../../../public/assets/images/user.png\";\nimport ApplicationsSourcesModal from \"@/components/commonModals/ApplicationsSourcesModal\";\nimport { useTranslations } from \"next-intl\";\n\nfunction Candidates() {\n  const [showApplicationsSourcesModal, setShowApplicationsSourcesModal] = useState(false);\n  const t = useTranslations();\n  return (\n    <div className=\"container\">\n      {/* --- Page Header --- */}\n      <div className=\"common-page-header\">\n        <div className=\"common-page-head-section\">\n          <div className=\"main-heading\">\n            <h2>\n              Hiring Manager Dashboard - <span>{t(\"candidates\")}</span>\n            </h2>\n            {/* <div className=\"d-flex\">\n                <NotificationIcon hasNotification={true} />\n              </div> */}\n          </div>\n        </div>\n      </div>\n\n      {/* --- Main Layout --- */}\n      <div className=\"common-box\">\n        {/* <Sidebar /> */}\n        <main className=\"main-content\">\n          <div className={style.dashboard_page}>\n            {/* --- Hired Candidates Table (No Tabs UI) --- */}\n            <div className=\"table-responsive\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>Candidate Name</th>\n                    <th>Date Hired</th>\n                    <th>Job Compatibility</th>\n                    <th>Interviewed By</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr>\n                    <td>\n                      <Link href=\"/\" className=\"primary underline\">\n                        John Doe\n                      </Link>\n                    </td>\n                    <td>Aug 18, 2024</td>\n                    <td>80%</td>\n                    <td>\n                      <ul className=\"multi-user-list\">\n                        {[...Array(4)].map((_, i) => (\n                          <li key={i}>\n                            <Image src={user} alt=\"user\" />\n                          </li>\n                        ))}\n                      </ul>\n                    </td>\n                  </tr>\n                  <tr>\n                    <td>\n                      <Link href=\"/\" className=\"primary underline\">\n                        Michael Johnson\n                      </Link>\n                    </td>\n                    <td>Administrator</td>\n                    <td>40%</td>\n                    <td>\n                      <ul className=\"multi-user-list\">\n                        {[...Array(4)].map((_, i) => (\n                          <li key={i}>\n                            <Image src={user} alt=\"user\" />\n                          </li>\n                        ))}\n                      </ul>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </main>\n      </div>\n\n      {/* --- Modals --- */}\n      {showApplicationsSourcesModal && <ApplicationsSourcesModal onCancel={() => setShowApplicationsSourcesModal(false)} />}\n    </div>\n  );\n}\n\nexport default Candidates;\n"], "names": [], "mappings": ";;;;AAEA;AAEA,qBAAqB;AACrB;AACA;AAEA,MAAM;AACN;AACA;AACA;AACA;;;AAZA;;;;;;;;AAcA,SAAS;;IACP,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjF,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;gCAAG;8CACyB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAW,4JAAA,CAAA,UAAK,CAAC,cAAc;kCAElC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;kDACC,cAAA,6LAAC;;8DACC,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;kDAGR,6LAAC;;0DACC,6LAAC;;kEACC,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAI,WAAU;sEAAoB;;;;;;;;;;;kEAI/C,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEACC,cAAA,6LAAC;4DAAG,WAAU;sEACX;mEAAI,MAAM;6DAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;8EACC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEAAC,KAAK,uSAAA,CAAA,UAAI;wEAAE,KAAI;;;;;;mEADf;;;;;;;;;;;;;;;;;;;;;0DAOjB,6LAAC;;kEACC,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAI,WAAU;sEAAoB;;;;;;;;;;;kEAI/C,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEACC,cAAA,6LAAC;4DAAG,WAAU;sEACX;mEAAI,MAAM;6DAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;8EACC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEAAC,KAAK,uSAAA,CAAA,UAAI;wEAAE,KAAI;;;;;;mEADf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAe5B,8CAAgC,6LAAC,iKAAA,CAAA,UAAwB;gBAAC,UAAU,IAAM,gCAAgC;;;;;;;;;;;;AAGjH;GAnFS;;QAEG,yMAAA,CAAA,kBAAe;;;KAFlB;uCAqFM", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/candidates/page.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport Candidates from \"@/components/views/candidates/Candidates\";\n\nconst page = () => {\n  return (\n    <div>\n      <Candidates />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,OAAO;IACX,qBACE,6LAAC;kBACC,cAAA,6LAAC,0JAAA,CAAA,UAAU;;;;;;;;;;AAGjB;uCAEe", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}