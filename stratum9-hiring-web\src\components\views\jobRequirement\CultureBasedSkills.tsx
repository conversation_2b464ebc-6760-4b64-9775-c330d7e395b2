import React, { useEffect, useState } from "react";

// External libraries
import { redirect, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";

import Button from "@/components/formElements/Button";
import Loader from "@/components/loader/Loader";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import EditSkillIcon from "@/components/svgComponents/EditSkillIcon";
import { SKILL_TYPE } from "@/constants/jobRequirementConstant";
import ROUTES from "@/constants/routes";
import { ISkillData } from "@/interfaces/jobRequirementesInterfaces";
import { selectAllSkills } from "@/redux/slices/allSkillsSlice";
import { selectJobDetails } from "@/redux/slices/jobDetailsSlice";
import { setJobRequirement } from "@/redux/slices/jobRequirementSlice";
import { selectCareerSkills, selectCultureSpecificSkills, selectRoleSpecificSkills } from "@/redux/slices/jobSkillsSlice";
import { generateJobRequirement } from "@/services/jobRequirements/generateJobServices";
import { ExtendedFormValues } from "@/types/types";

import style from "@/styles/commonPage.module.scss";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";
import { toastMessageSuccess } from "@/utils/helper";

function CultureBasedSkills() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  // Get culture-specific skills from Redux store
  const cultureSpecificSkills = useSelector(selectCultureSpecificSkills) || [];
  const careerSkills = useSelector(selectCareerSkills) || [];
  const roleSpecificSkills = useSelector(selectRoleSpecificSkills) || [];
  // Get job details from Redux store
  const jobDetails = useSelector(selectJobDetails) as ExtendedFormValues;
  // Get all skills from Redux store
  const allSkills = useSelector(selectAllSkills);
  const dispatch = useDispatch();
  const t = useTranslations();
  const tCommon = useTranslations("jobRequirement");
  /**
   *
   *
   * @effect
   * @description Checks if the skill type is valid and if necessary Redux data is available
   *              Redirects to the career-based skills page if any validation fails
   *              Runs only once on component mount due to empty dependency array
   */

  useEffect(() => {
    if (!cultureSpecificSkills || cultureSpecificSkills.length === 0) redirect(`${ROUTES.JOBS.GENERATE_JOB}`);
  }, []);
  // Function to handle generating job requirement
  const handleGenerateJobRequirement = async () => {
    try {
      setIsLoading(true);

      // Prepare data for API call by cleaning and combining job details and skills
      // First, extract only the needed fields from jobDetails to avoid sending _persist and other Redux metadata
      const {
        title,
        department_id,
        hiring_type,
        employment_type,
        salary_range,
        salary_cycle,
        location_type,
        state,
        city,
        role_overview,
        responsibilities,
        educations_requirement,
        certifications,
        skills_and_software_expertise,
        experience_required,
        ideal_candidate_traits,
        about_company,
        perks_benefits,
        tone_style,
        additional_info,
        compliance_statement,
        show_compliance,
        experience_level,
      } = jobDetails;

      const requestData = {
        department_id,
        hiring_type,
        title,
        employment_type,
        salary_range,
        salary_cycle,
        location_type,
        state,
        city,
        role_overview,
        responsibilities,
        educations_requirement,
        certifications,
        skills_and_software_expertise,
        experience_required,
        ideal_candidate_traits,
        about_company,
        perks_benefits,
        tone_style,
        additional_info,
        compliance_statement: JSON.stringify(compliance_statement),
        show_compliance,
        experience_level,
        // Add all skills data
        all_skills: allSkills,
        // Add the skills data user based on the skill type
        career_skills: careerSkills.map((skill: ISkillData) => ({
          name: skill.name,
          description: skill.description,
        })),
        role_specific_skills: roleSpecificSkills.map((skill: ISkillData) => ({
          id: skill.id,
          name: skill.name,
          description: skill.description,
        })),
        culture_specific_skills: cultureSpecificSkills.map((skill) => ({
          id: skill.id,
          name: skill.name,
          description: skill.description,
        })),
      };

      // Call the API to generate job requirement
      const response = await generateJobRequirement(requestData);

      // Check if the API response was successful (based on your API structure)
      // Assuming your API returns a successful response with data.status or simila
      if (response && response.data && response.data.success) {
        // Show success message
        toastMessageSuccess(t("job_requirements_generated"));
        dispatch(setJobRequirement(response.data.data as string));
        // Navigate to the next page
        router.push(ROUTES.JOBS.JOB_EDITOR);
      } else {
        // Show error message
        // Extract error message from the response if available
        const errorMsg = response?.error?.message || response?.data?.message || t("generate_job_requirement_failed");
        console.error(errorMsg);
        toast.error(errorMsg as string);
      }
    } catch (error) {
      console.error(t("skill_name_required"), error);
      toast.error(t("generate_job_requirement_error"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon
                  onClick={() => {
                    router.push(ROUTES.JOBS.ROLE_BASED_SKILLS);
                  }}
                />
                {tCommon("top_performance_based_skills")}
                <span>{jobDetails?.title || ""}</span>
              </h2>
            </div>
            <p className="description">
              {t("performance_skills_part1")} <span>{jobDetails?.title || ""}</span> {t("performance_skills_part2")}{" "}
              <strong className="color-primary">{t("performance_skills_part3")}</strong>.
            </p>
          </div>
        </div>
        <div className="inner-section culture-based-skills">
          <div className="d-flex justify-content-between align-items-center">
            <h3 className={style.inner_heading}>
              {t("top")} <span> {t("culture_specific_performance_based_skills")} </span>
            </h3>
            <Button
              className="clear-btn p-0 primary"
              onClick={() => {
                router.push(`${ROUTES.JOBS.EDIT_SKILLS}?skillType=${SKILL_TYPE.CULTURE}`);
              }}
            >
              <EditSkillIcon className="me-3" />
              {t("edit_skills")}
            </Button>
          </div>

          <div className="row g-4">
            {cultureSpecificSkills.length > 0 ? (
              cultureSpecificSkills.map((skill: ISkillData, index: number) => (
                <div className="col-md-4" key={index}>
                  <div className="career-skill-card">
                    <div className="head">
                      <h3>{skill.name || `Culture Skill ${index + 1}`}</h3>
                    </div>
                    <div className="skill-content">
                      <p>{skill.description || t("no_description_available")}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-12">
                <NoDataFoundIcon width={300} height={300} />
                <p>{t("no_culture_specific_skills_found")}</p>
              </div>
            )}
          </div>
        </div>

        <div className="button-align py-5">
          <Button className="primary-btn rounded-md" onClick={handleGenerateJobRequirement} disabled={isLoading}>
            {tCommon("generate_job_requirement")} {isLoading && <Loader />}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default CultureBasedSkills;
